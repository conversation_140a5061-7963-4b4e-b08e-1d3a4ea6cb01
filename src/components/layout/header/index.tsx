"use client";

import { Skeleton } from "@/components/ui/loader/skeleton";
import useScrolled from "@/lib/hooks/useScrolled";
import classNames from "classnames";
import { lazy, Suspense } from "react";
import Branding from "./components/branding";
import HamburgerMenu from "./components/hamburger";
import HeaderSearch from "./components/search";

const TopHeader = lazy(() => import("./top"));
const Navigation = lazy(() => import("./components/navigation"));
const Actions = lazy(() => import("./components/actions"));

const GlobalHeader = () => {
  const isScrolled = useScrolled();

  return (
    <header id="global-header" className="z-50 sticky top-0">
      <Suspense fallback={<div className="bg-dark w-screen h-[26px]" />}>
        <TopHeader />
      </Suspense>
      <section className="relative bg-white shadow">
        <div className="container">
          <div className="flex items-center justify-between gap-6">
            <div className="flex-1 flex items-center justify-start gap-2 sm:gap-6">
              <HamburgerMenu className={classNames("block sm:hidden")} />
              <Branding />
              <HeaderSearch
                className={classNames(
                  "block h-full flex-1",
                  isScrolled ? "sm:!block" : "sm:!hidden"
                )}
              />
              <div
                className={classNames(
                  "h-full",
                  isScrolled ? "hidden" : "block"
                )}
              >
                <Suspense fallback={<Skeleton className="h-6 w-full" />}>
                  <Navigation />
                </Suspense>
              </div>
            </div>
            <Suspense fallback={<Skeleton className="h-6 w-full" />}>
              <Actions />
            </Suspense>
          </div>
        </div>
      </section>
    </header>
  );
};

export default GlobalHeader;
