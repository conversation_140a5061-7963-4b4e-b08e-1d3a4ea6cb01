"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import ListPagination from "@/components/elements/list/pagination";
import { Select } from "@/components/ui/input/select";
import { getPaginationOptions } from "@/lib/utils/data";
import { RootDispatch } from "@/redux/store";
import { debounce } from "lodash";
import { AlertCircle } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";

const data = Array.from({ length: 8 })?.map((_, index) => {
  return {
    _id: String(index + 1),
    match: ["good", "perfect", "fair"][index % 3],
    title: "Plastic Ball 250 pieces",
    budget: "$400",
    proposals: "2/5",
    client: "123",
    contact: "321",
    status: "open",
    date: new Date(),
  };
});

const ProposalsTable = () => {
  const dispatch = useDispatch<RootDispatch>();
const router = useRouter();

  const pageNow = "2";

  const pagination = {
    totalPages: 3,
    page: 2,
  };

  const finalData = data;

  useEffect(() => {
    // dispatch(getProducts());
  }, [dispatch]);

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (searchTerm?: string, page?: string) =>
          //   dispatch(getProducts({ searchTerm, page })),
          500
      ),
    [dispatch]
  );

  const handlePagination = (page: string) => {
    // dispatch(Actions.setCurrentPage(page));
    // debouncedSearch(search, page);
  };

  //   const isDataThere = !loading && productData && pagination?.totalPages > 1;
  const isDataThere = true;

  return (
    <div className="space-y-4 mt-6 max-w-full overflow-hidden">
      <div className="whitespace-nowrap w-full overflow-x-auto">
        <div className="flex flex-col items-start gap-2.5 [&>*]:w-full">
          {Array.isArray(finalData) && finalData?.length > 0 ? (
            <>
              <ListCardHeader
                className="whitespace-nowrap"
                data={[
                  {
                    label: "Match",
                    key: "match",
                    className: "max-w-[10%] text-start",
                  },
                  {
                    label: "products / services",
                    key: "title",
                    className: "max-w-[30%] text-start",
                  },
                  {
                    label: "budget",
                    key: "budget",
                    className: "max-w-[10%] text-center",
                  },
                  {
                    label: "proposals",
                    key: "proposals",
                    className: "max-w-[10%] text-center",
                  },
                  {
                    label: "client",
                    key: "client",
                    className: "max-w-[10%] text-center",
                  },
                  {
                    label: "contact",
                    key: "contact",
                    className: "max-w-[10%] text-center",
                  },
                  {
                    label: "status",
                    key: "status",
                    className: "max-w-[10%] text-center",
                  },
                  {
                    label: "date",
                    key: "date",
                    className: "max-w-[10%] text-center",
                  },
                ]}
              />
              {finalData?.map((prop: any, index: number) => {
                return (
                  <ListCard
                    key={prop?._id + String(index)}
                    match={prop?.match}
                    title={prop?.title}
                    budget={prop?.budget}
                    proposals={prop?.proposals}
                    client={prop?.client}
                    contact={prop?.contact}
                    status={prop?.status}
                    date={prop?.date}
                    locked={{
                      client: true,
                      contact: true,
                    }}
                    classNames={{
                      match: "",
                      title: "!max-w-[30%]",
                      budget: "!max-w-[10%]",
                      proposals: "!max-w-[10%]",
                      client: "!max-w-[10%]",
                      contact: "!max-w-[10%]",
                      status: "!max-w-[10%]",
                      date: "!max-w-[10%]",
                    }}
                    hideImage
                    hideAction
                    onSelect={() => router.push(prop?._id)}
                  />
                );
              })}
            </>
          ) : (
            <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
              <AlertCircle /> No Projects Available
            </div>
          )}
        </div>
      </div>

      {isDataThere && (
        <div className="flex justify-between mt-8 items-center gap-4">
          <ListPagination
            totalPages={pagination?.totalPages}
            currentPage={pagination?.page}
            onPageChange={(page) => handlePagination(String(page))}
          />

          <div className="ml-auto">
            <Select
              name="pages"
              value={pageNow}
              onChange={(e) => handlePagination(e?.target?.value)}
              placeholder="Page"
              options={getPaginationOptions(pagination?.totalPages)}
              className="w-max"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProposalsTable;
