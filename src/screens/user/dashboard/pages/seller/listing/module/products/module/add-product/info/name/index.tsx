import Suggestions from "@/components/elements/form/suggestions";
import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import { Input } from "@/components/ui/input/text";
import { Loader } from "@/components/ui/loader";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import useOutsideAlert from "@/lib/hooks/useOutsideAlert";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import { debounce } from "lodash";
import { useParams } from "next/navigation";
import { FC, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getSubSubCategories } from "../../../../../company/@redux/thunk";
import { productSuggestions } from "../../../../@redux/selectors";
import {
  getProductPreValues,
  getProductSuggestions,
} from "../../../../@redux/thunk";

interface ProductNameType {
  handleChange: (e: any) => void;
  values: any;
  modalPopover?: boolean;
}

const ProductName: FC<ProductNameType> = ({
  handleChange,
  values,
  modalPopover,
}) => {
  const dispatch = useDispatch<RootDispatch>();
  const dropdownRef = useRef(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const params = useParams();
  const product_id = params?.id;

  const { data: suggestions, loading } = useSelector(productSuggestions);

  const optionData = useMemo(() => {
    if (Array.isArray(suggestions)) {
      return suggestions?.map((item: any) => ({
        ...item,
        label: item?.product_name,
      }));
    }

    return [];
  }, [suggestions]);

  const isValidToOpen =
    !product_id &&
    values?.product_name &&
    Array.isArray(optionData) &&
    optionData?.length > 0;

  const value = values?.product_name ?? "";
  const [isPopoverOpen, setIsPopoverOpen] = useState<boolean>(
    Boolean(isValidToOpen)
  );

  useEffect(() => {
    setIsPopoverOpen(Boolean(isValidToOpen));
  }, [isValidToOpen]);

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (value: string) =>
          dispatch(getProductSuggestions({ searchTerm: value })),
        500
      ),
    [dispatch]
  );

  const handleSearch = (e: any) => {
    handleChange(e);
    debouncedSearch(e?.target?.value);
  };

  const handleFocus = () => {
    if (isValidToOpen) {
      setIsPopoverOpen(true);
    }
    inputRef.current?.focus();
  };

  const handleSelect = (suggestion: Record<string, any>) => {
    dispatch(getProductPreValues({ suggestionId: suggestion?._id }));
    dispatch(Actions.setSuggestedProduct(suggestion));
    const payload: any = {};
    payload.sub_sub_category = suggestion?.sub_sub_category;
    dispatch(Actions.setProductDetails(payload));
    dispatch(
      getSubSubCategories({ search: suggestion?.sub_sub_category_name })
    );
    setIsPopoverOpen(false);
  };

  useOutsideAlert(dropdownRef, () => setIsPopoverOpen(false));

  return (
    <div className="flex flex-col md:flex-row items-start gap-6 [&>*]:w-full md:[&>*]:w-auto">
      <div className="min-w-min flex flex-col text-start gap-2 md:w-[25%]">
        <div className="flex items-center justify-start gap-1.5">
          <InputLabel>Product Name</InputLabel>
          <Badge
            className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
            variant="destructive"
          >
            Required
          </Badge>
        </div>
        <span className="text-stone-500 text-[0.7rem]">
          Required, Text (Mx 100 chars)
        </span>
      </div>
      <Popover
        open={isPopoverOpen}
        onOpenChange={setIsPopoverOpen}
        modal={modalPopover}
      >
        <PopoverTrigger
          className="flex-1 items-start w-full text-start"
          onClick={(e) => e?.preventDefault()}
          ref={dropdownRef}
        >
          <Input
            ref={inputRef}
            type="text"
            name="product_name"
            onChange={handleSearch}
            value={value}
            placeholder="Product Name"
            containerClassName="flex-1"
            labelClassName="hidden md:block"
            maxLength={70}
            showCharCount
            autoComplete="off"
            action={loading && <Loader small className="!w-max" />}
            onFocus={() => handleFocus()}
            // onBlur={() => setIsPopoverOpen(true)}
            required
          />
        </PopoverTrigger>
        <PopoverContent
          align="start"
          onEscapeKeyDown={() => setIsPopoverOpen(false)}
          ref={dropdownRef}
          className="-translate-y-6 !p-1"
        >
          <Suggestions
            data={optionData}
            value={value}
            params={{
              subText: [
                "category_name",
                "sub_category_name",
                "sub_sub_category_name",
              ],
              description: ["brand_name"],
            }}
            onSelect={handleSelect}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default ProductName;
