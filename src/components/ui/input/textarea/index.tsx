import classNames from "classnames";
import * as React from "react";
import { InputLabel } from "../components/label";
import { InputWrapper } from "../components/wrapper";

interface TextareaType extends React.ComponentProps<"textarea"> {
  icon?: React.ReactNode | string;
  containerClassName?: string;
  wrapperClassName?: string;
  labelClassName?: string;
  label?: string;
  value?: React.ComponentProps<"textarea">["value"];
  maxLength?: number;
  showCharCount?: boolean;
}

const Textarea = React.memo(
  React.forwardRef<HTMLTextAreaElement, TextareaType>(
    (
      {
        className,
        containerClassName,
        wrapperClassName,
        labelClassName,
        label,
        id,
        icon,
        value,
        maxLength,
        showCharCount,
        ...props
      },
      ref
    ) => {
      const uniqueId = React.useId();

      return (
        <div
          className={classNames(
            "textarea flex flex-col gap-1.5 items-center [&>*]:w-full",
            containerClassName
          )}
        >
          {label && (
            <InputLabel htmlFor={id ?? uniqueId} className={labelClassName}>
              {label}
            </InputLabel>
          )}
          <InputWrapper
            icon={icon}
            className={classNames("!h-full", wrapperClassName)}
          >
            <textarea
              className={classNames(
                "flex w-full h-full bg-transparent text-base placeholder:text-muted-foreground outline-0 ring-0 border-0 focus:outline-0 focus:ring-0 focus:border-0 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm px-0 py-1",
                className
              )}
              ref={ref}
              value={value}
              maxLength={maxLength}
              {...props}
            />
          </InputWrapper>
          {showCharCount && value && maxLength && (
            <div className="flex justify-end text-xs text-gray-500">
              max {value?.toString()?.length}/{maxLength} chars
            </div>
          )}
        </div>
      );
    }
  )
);
Textarea.displayName = "Textarea";

export { Textarea };
export type { TextareaType };
