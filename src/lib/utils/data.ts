export type GetFilteredDataType = {
  data: any[];
  compareValue: string;
  compareParams?: string[];
};

export const getFilteredData = ({
  data,
  compareValue,
  compareParams,
}: GetFilteredDataType) => {
  try {
    if (!compareValue.trim()) return data;

    if (!(Array.isArray(data) && data.length)) return [];

    if (Array.isArray(data) && typeof data[0] === "string") {
      return data.filter((item) =>
        item?.toLowerCase()?.includes(compareValue.toLowerCase())
      );
    }

    if (compareParams) {
      const uniqueParams = [...new Set(compareParams)];

      return data.filter((item) =>
        uniqueParams.some(
          (param) =>
            item?.[param] &&
            item[param]
              ?.toString()
              .toLowerCase()
              .includes(compareValue.toString().toLowerCase())
        )
      );
    }

    return data;
  } catch (error) {
    console.error(error);
    return [];
  }
};

export const capitalizeWords = (str: string): string => {
  return str.replace(/\b\w/g, (char) => char.toUpperCase());
};

export const isEmptyObject = (obj: Record<string, any>): boolean => {
  return obj === null || Object.keys(obj).length === 0;
};

export const hasRequiredKeys = (
  obj: Record<string, any>,
  keys: string[]
): boolean => {
  return keys.every((key) => obj.hasOwnProperty(key) && Boolean(obj[key]));
};

export const removeAtIndex = <T>(arr: T[], index: number): T[] => {
  if (index < 0 || index >= arr.length) return arr;
  return [...arr.slice(0, index), ...arr.slice(index + 1)];
};

export const getFirstName = (fullName: string): string => {
  if (typeof fullName !== "string" || !fullName.trim()) return "";
  return fullName.trim().split(" ")[0];
};

export interface PaginationSummaryType {
  limit: number;
  page: number;
  totalData: number;
}

export const getPaginationSummary = (
  pagination?: PaginationSummaryType
): string => {
  if (!pagination) return "";

  const { limit, page, totalData } = pagination;

  if (totalData === 0) return "No entries available";

  const start = (page - 1) * limit + 1;
  const end = Math.min(page * limit, totalData);

  return `Showing ${start} to ${end} of ${totalData} Entries`;
};

interface PaginationOptionsExport {
  label: string;
  value: string;
}
export const getPaginationOptions = (
  page: number
): PaginationOptionsExport[] => {
  return Array.from({ length: page })?.map((_, i) => ({
    label: String(i + 1),
    value: String(i + 1),
  }));
};

export const flattenArraysOfObject = <T extends Record<string, any>>(
  data: T,
  keys: string[],
  extractKey: string
): T => {
  if (!data || typeof data !== "object") return data;

  return Object.fromEntries(
    Object.entries(data).map(([key, value]) =>
      keys.includes(key) && Array.isArray(value)
        ? [key, value.map((item) => item[extractKey])]
        : [key, value]
    )
  ) as T;
};

export const extractArrayOfKey = <
  T extends Record<string, any>,
  K extends keyof T,
>(
  array: T[] | undefined,
  extractKey: K
): T[K][] => {
  if (!Array.isArray(array)) return [];
  return array.map((item) => item[extractKey]);
};

export const generateNumberRanges = (
  start: number,
  end: number,
  gaps: number[]
): { label: string; value: string }[] => {
  const ranges: any[] = [];
  let current = start;

  for (const gap of gaps) {
    const next = current + gap;
    if (next > end) break;

    ranges.push({
      label: `${current} to ${next}`,
      value: `${current} to ${next}`,
    });
    current = next;
  }

  ranges.push({ label: `${end}+`, value: `${end}+` }); // Add final range

  return ranges;
};

export const isIdentificationNumber = (segment: string): boolean => {
  const objectIdRegex = /^[a-f0-9]{24}$/i;
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i; // UUID
  const numericIdRegex = /^\d+$/; // Numeric ID

  return (
    objectIdRegex.test(segment) ||
    uuidRegex.test(segment) ||
    numericIdRegex.test(segment)
  );
};

export const getCookie = (name: string) => {
  const match = document.cookie.match(new RegExp("(^| )" + name + "=([^;]+)"));
  return match ? decodeURIComponent(match[2]) : null;
};

export const getJsonCookie = (name: string) => {
  try {
    const match = document.cookie.match(
      new RegExp("(^| )" + name + "=([^;]+)")
    );
    const data = match ? decodeURIComponent(match[2]) : null;
    return data ? JSON.parse(data) : {};
  } catch (error) {
    console.error(error);
    return {};
  }
};
