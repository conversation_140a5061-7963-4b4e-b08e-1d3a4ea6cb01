import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";
import privateApiClient from "@/services/axios/private-api-client";

const formDataConfig = {
  headers: {
    "Content-Type": "multipart/form-data",
  },
};

// Function to get RFQ list with pagination and type filter
export const getRFQList = async ({ page, limit, type }: { page?: number; limit?: number; type?: string } = {}) => {
  try {
    const response = await formDataClient.get(
      apiRoutes().buyer.manage.fetchRFQ,
      { params: { page, limit, type } }
    );
    return response.data;
  } catch (exception) {
    console.error("API getRFQList error:", exception);
    throw exception;
  }
};

// Function to get a specific RFQ detail by ID
export const getRFQDetail = async (id?: string) => {
  try {
    const response = await formDataClient.get(
      `${apiRoutes().buyer.manage.fetchRFQDetails}${id ? `/${id}` : ''}`
    );
    return response.data;
  } catch (exception) {
    console.error("API getRFQDetail error:", exception);
    throw exception;
  }
};

// Function to update RFQ status
export const updateRFQStatus = async (id: string, status: string) => {
  try {
    const response = await formDataClient.put(
      `${apiRoutes().buyer.manage.updateRFQ}/${id}`,
      { status: status.toLowerCase() },
      formDataConfig
    );
    return response.data;
  } catch (exception) {
    console.error("API updateRFQStatus error:", exception);
    throw exception;
  }
};

// Function to handle proposal actions (accept/reject)
export const handleProposalAction = async (params: {
  action: 'accept' | 'reject';
  proposalId: string;
  opportunityId: string;
}) => {
  try {
    const response = await formDataClient.post(
      `${apiRoutes().buyer.manage.proposalAction}`,
      null,
      {
        params: {
          action: params.action,
          proposalId: params.proposalId,
          opportunityId: params.opportunityId
        }
      }
    );
    return response.data;
  } catch (exception) {
    console.error("API handleProposalAction error:", exception);
    throw exception;
  }
};

export const deleteRFQ = async (RFQ_id?: string) => {
  return await privateApiClient.delete(
    `${apiRoutes().buyer.rfq.deleteRFQ}/${RFQ_id}`
  );
};

export const updateRFQDetails = async (
  payload: any, // plain JS object with nested contactInfo
  rfq_id: string
) => {
  return await privateApiClient.put(
    `${apiRoutes().buyer.manage.updateRFQ}/${rfq_id}`,
    payload,
    {
      headers: {
        "Content-Type": "application/json"
      }
    }
  );
};