'use client';

import RATING_STAR from "../../../../../../../../../../public/assets/pages/rating-star.svg";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { FaRegHeart } from "react-icons/fa";
import { useRouter } from "next/navigation";

interface ProductDetails {
  _id?: string;
  product_name?: string;
  brand_name?: string;
  mrp_price?: number;
  discounted_price?: number;
  stock?: boolean;
  main_product_images?: string[];
  average_rating?: number;
  name?: string;
  title?: string;
  description?: string;
}

interface ProductCardProps {
  product: {
    favoriteId?: string;
    favoriteDetails?: ProductDetails;
  };
  onUnfavourite?: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onUnfavourite }) => {
 const router = useRouter();

  const details = product?.favoriteDetails || {};
  const {
    _id,
    product_name,
    brand_name,
    mrp_price,
    discounted_price,
    average_rating,
    name,
    title,
    description,
  } = details;

  const displayTitle = title || product_name || name || "Apple HomePod, 2nd Generation";
  const displayBrand = brand_name || "Procter & Gamble";
  const displayMRP = mrp_price ? `$${mrp_price}` : "$371.99";
  const displayDiscount = discounted_price ? `$${discounted_price}` : "$381.99";
  const displayRating = average_rating || 4.5;
  const displayDescription = description || "Experience the power of iPhone 15 Pro";

  const isFavourite = !!product.favoriteId;

  return (
    <div
      role="button"
      onClick={() => router.push(`/products/details/${_id || "123"}`)}
      className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden w-full"
    >
      <div className="relative w-full aspect-square">
        <img
          src="https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg"
          alt="Apple HomePod"
          className="w-full h-full object-cover"
        />
        <button
          className={`absolute right-4 top-4 transition-all h-8 w-8 rounded-full flex items-center justify-center shadow group
            ${isFavourite ? "bg-red-500" : "bg-white hover:bg-red-500"}`}
          onClick={(e) => {
            e.stopPropagation();
            if (isFavourite && onUnfavourite) {
              onUnfavourite();
            }
          }}
        >
          <FaRegHeart
            size={16}
            className={`mt-0.5 transition-colors
              ${isFavourite ? "text-white" : "text-gray-400 group-hover:text-white"}`}
          />
        </button>
      </div>

      <div className="p-4 flex flex-col gap-2">
        <h3 className="text-sm font-bold text-gray-900 line-clamp-2">{displayTitle}</h3>

        <div className="flex items-center gap-2 mt-1">
          <img src="/assets/pages/rating-star.svg" alt="rating star" className="w-4 h-4" />
          <div className="flex items-center gap-2 text-xs font-semibold text-gray-700">
            <span className="border-r border-slate-300 pr-2">{displayRating}/5</span>
            <span>150 Reviews</span>
          </div>
        </div>

        <div className="flex items-center gap-2 mt-1">
          <span className="text-lg font-bold text-gray-900">{displayDiscount}</span>
          <span className="text-sm text-gray-400 line-through">{displayMRP}</span>
        </div>

        <div className="flex items-center gap-2 mt-1">
          <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-xs">{displayBrand.slice(0, 3).toUpperCase()}</span>
          </div>
          <span className="text-xs font-medium">{displayBrand}</span>
        </div>

        <div className="flex items-center gap-1.5 mt-1">
          <img
            src="https://upload.wikimedia.org/wikipedia/en/thumb/4/41/Flag_of_India.svg/1200px-Flag_of_India.svg.png"
            alt="flag"
            className="w-4 h-auto object-contain"
          />
          <span className="text-xs text-gray-600">India Mumbai</span>
        </div>

        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs text-gray-700">Min Order:</span>
          <span className="text-xs text-gray-600">500 Pieces</span>
        </div>

  <div className="flex flex-col gap-2 mt-3 w-full">
  <Button
    variant="outline"
    className="bg-white text-orange-500 border-orange-500 hover:bg-orange-50 w-full h-8 rounded-full text-xs px-4"
  >
    Get Quote
  </Button>

  <div className="w-full h-8 flex items-center justify-start border border-gray-300 px-3 rounded-full">
    <Checkbox
      id={`compare-${_id}`}
      className="border-gray-400 h-3 w-3 mr-2 shrink-0"
    />
    <label
      htmlFor={`compare-${_id}`}
      className="text-xs text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis"
    >
      Add to Compare
    </label>
  </div>
</div>
      </div>
    </div>
  );
};

export default ProductCard;
