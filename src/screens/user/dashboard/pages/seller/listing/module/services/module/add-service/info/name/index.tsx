import Suggestions from "@/components/elements/form/suggestions";
import { Badge } from "@/components/ui/badge";
import Dropdown, { DropdownReferenceType } from "@/components/ui/dropdown";
import { InputLabel } from "@/components/ui/input/components/label";
import { Input } from "@/components/ui/input/text";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import classNames from "classnames";
import { debounce } from "lodash";
import { useParams } from "next/navigation";
import { FC, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";

import { getSubSubCategories } from "../../../../../company/@redux/thunk";
import { serviceSuggestions } from "../../../../@redux/selectors";
import {
  getServicePreValues,
  getServiceSuggestions,
} from "../../../../@redux/thunk";

interface ServiceNameType {
  handleChange: (e: any) => void;
  values: any;
}

const ServiceName: FC<ServiceNameType> = ({ handleChange, values }) => {
  const dispatch = useDispatch<RootDispatch>();
  const dropdownRef = useRef<DropdownReferenceType>(null);
  const params = useParams();
  const service_id = params?.id;
  const { data: suggestions, loading } = useSelector(serviceSuggestions);

  const optionData = useMemo(() => {
    if (Array.isArray(suggestions)) {
      return suggestions?.map((item: any) => ({
        ...item,
        label: item?.service_name,
      }));
    }

    return [];
  }, [suggestions]);

  const value = values?.service_name ?? "";

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (value: string) =>
          dispatch(getServiceSuggestions({ searchTerm: value })),
        500
      ),
    [dispatch]
  );

  const handleSearch = (e: any) => {
    handleChange(e);
    debouncedSearch(e?.target?.value);
    dropdownRef?.current?.toggle(true);
  };

  const handleSelect = (suggestion: Record<string, any>) => {
    dispatch(getServicePreValues({ suggestionId: suggestion?._id }));
    dispatch(Actions.setSuggestedService(suggestion));
    const payload: any = {};
    payload.sub_sub_category = suggestion?.sub_sub_category;
    dispatch(Actions.setServiceDetails(payload));
    dispatch(
      getSubSubCategories({ search: suggestion?.sub_sub_category_name })
    );
    dropdownRef?.current?.toggle(false);
  };

  return (
    <div className="flex flex-col md:flex-row items-start gap-6 [&>*]:w-full md:[&>*]:w-auto">
      <div className="min-w-min flex flex-col text-start gap-2 md:w-[25%]">
        <div className="flex items-center justify-start gap-1.5">
          <InputLabel>Service Name</InputLabel>
          <Badge
            className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
            variant="destructive"
          >
            Required
          </Badge>
        </div>
        <span className="text-stone-500 text-[0.7rem]">
          Required, Text (Mx 100 chars)
        </span>
      </div>
      <Dropdown
        ref={dropdownRef}
        className="flex-1 min-w-36"
        open={!service_id && Boolean(value)}
        content={
          <Suggestions
            data={optionData}
            params={{
              subText: [
                "category_name",
                "sub_category_name",
                "sub_sub_category_name",
              ],
              description: ["brand_name"],
            }}
            onSelect={handleSelect}
          />
        }
        contentClassName={classNames(
          "w-max min-w-[325px]",
          value && "-translate-y-5"
        )}
      >
        <Input
          type="text"
          name="service_name"
          label="service Name"
          onChange={handleSearch}
          onFocus={() => (value ? dropdownRef?.current?.toggle(true) : null)}
          value={value}
          placeholder="Service Name"
          containerClassName="flex-1"
          labelClassName="hidden md:block"
          maxLength={70}
          showCharCount
          autoComplete="off"
          required
        />
      </Dropdown>
    </div>
  );
};

export default ServiceName;
