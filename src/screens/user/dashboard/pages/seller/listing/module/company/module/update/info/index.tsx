import { Badge } from "@/components/ui/badge";
import InputLabel from "@/components/ui/input/components/label";
import {
  Dropzone,
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import {
  businessTypes,
  ownershipTypes,
  paymentMethods,
} from "@/constants/user/seller/company/update";
import {
  categories,
  companyDetails,
} from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import { Image } from "lucide-react";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";

const Information = () => {
  const dispatch = useDispatch();
  const { data: values } = useSelector(companyDetails);
  const { data: categoriesData } = useSelector(categories);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        if (e.target?.name === "business_logo") {
          payload[e?.target?.name] = e.target.files?.[0];
        } else {
          const fileArray = Array.from(e.target.files);
          let fileList = Array.isArray(values?.[e?.target?.name])
            ? Array.from(values?.[e?.target?.name])
            : [];

          fileList = fileList.concat(fileArray);
          payload[e?.target?.name] = fileList;
        }
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const businessPhotosData = Array.from(
    { length: 4 },
    (_, i) => values?.business_photos?.[i] || null
  );

  type FileOrString = File | string;

  const handleFileDelete = (file: FileOrString, fieldName: string) => {
    try {
      const payload: any = {};

      const originalList: FileOrString[] = Array.isArray(values[fieldName])
        ? values[fieldName]
        : [];

      const filteredList: FileOrString[] = originalList.filter((photo) => {
        if (typeof file === "string" && typeof photo === "string") {
          return photo !== file;
        }

        if (file instanceof File && photo instanceof File) {
          return photo.name !== file.name;
        }

        return true;
      });

      payload[fieldName] = filteredList;

      dispatch(Actions.setFormValues(payload));
    } catch {
      toast.error("Failed to delete photo", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Business Logo & Photos</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload logo & images (500x500 px)
          </span>
        </div>
        {/* TOTO */}
        {/* need separate box for Logo at start */}
        <div className="flex-1 w-full flex items-start gap-4 flex-wrap border border-dashed border-stone-500 rounded-lg p-4 [&>*]:min-w-20">
          <Dropzone
            containerClassName="flex-1"
            onChange={(e) => handleChange(e)}
            onFileDelete={(file, fieldName) =>
              handleFileDelete(file, fieldName)
            }
            name="business_logo"
            value={[values?.business_logo]}
            preview
            contentClassName="!border-none !p-0"
            filePreviewClassName="w-full [&_.preview-item]:w-24 [&_.preview-item]:h-24"
          >
            <div className="flex flex-col gap-2 items-center">
              {!values?.business_logo && (
                <div className="flex items-center justify-center border border-stone-300 hover:bg-slate-100 w-full h-24 rounded-lg">
                  <Image />
                </div>
              )}
              <span className="text-center text-xs font-light">
                Company Logo
              </span>
            </div>
          </Dropzone>
          {businessPhotosData?.map((item, index) => (
            <Dropzone
              key={index}
              containerClassName="flex-1"
              onChange={(e) => handleChange(e)}
              onFileDelete={(file, fieldName) =>
                handleFileDelete(file, fieldName)
              }
              name="business_photos"
              value={
                item ? (item instanceof File ? [item] : [item?.url]) : undefined
              }
              preview
              contentClassName="!border-none !p-0"
              inputProps={{
                multiple: true,
              }}
            >
              {!item ? (
                <div className="flex flex-col gap-2 items-center">
                  <div className="flex items-center justify-center border border-stone-300 hover:bg-slate-100 w-full h-24 rounded-lg">
                    <Image />
                  </div>
                </div>
              ) : (
                " "
              )}
            </Dropzone>
          ))}
        </div>
      </div>

      {/* User Type selection removed */}
      {/* <div className="flex items-center flex-wrap gap-4 md:gap-10">
        <InputLabel>Please select your role</InputLabel>

        <RadioGroup
          value={values?.role}
          defaultValue="buyer"
          className="grid-cols-2 md:grid-cols-4 gap-6 w-max"
        >
          <div className="flex items-center space-x-2 w-max">
            <RadioGroupItem value="buyer" id="r1" />
            <InputLabel htmlFor="r1">Buyer</InputLabel>
          </div>
          <div className="flex items-center space-x-2 w-max">
            <RadioGroupItem value="seller" id="r2" />
            <InputLabel htmlFor="r2">Seller</InputLabel>
          </div>
          <div className="flex items-center space-x-2 w-max">
            <RadioGroupItem value="advertiser" id="r3" />
            <InputLabel htmlFor="r3">Advertiser</InputLabel>
          </div>
          <div className="flex items-center space-x-2 w-max">
            <RadioGroupItem value="all" id="r4" />
            <InputLabel htmlFor="r3">All in one</InputLabel>
          </div>
        </RadioGroup>
      </div> */}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          type="text"
          name="business_name"
          label="Business Name"
          onChange={handleChange}
          value={values?.business_name ?? ""}
          placeholder="Enter your business address"
        />

        <Select
          name="ownership_type"
          label="Ownership Type"
          onChange={handleChange}
          value={values?.ownership_type ?? ""}
          placeholder="Select Ownership Type"
          options={ownershipTypes}
        />
      </div>
      <div className="grid grid-cols-1 gap-6">
        <MultiSelect
          onValueChange={handleChange}
          name="business_type"
          label="Business Type"
          placeholder="Select Business type"
          variant="inverted"
          maxCount={10}
          options={businessTypes}
          defaultValue={values?.business_type}
          value={values?.business_type}
          addNew
        />
        <MultiSelect
          onValueChange={handleChange}
          name="industry_category"
          label="Industry category"
          placeholder="Choose main Industry"
          variant="inverted"
          maxCount={10}
          options={categoriesData}
          optionKeys={{
            label: "sub_sub_category_name",
            value: "_id",
          }}
          defaultValue={values?.industry_category}
          value={values?.industry_category}
          addNew
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          type="text"
          name="business_registration_number"
          label="Company Registration No"
          onChange={handleChange}
          value={values?.business_registration_number}
          placeholder="Enter issued business ID"
        />

        <Input
          type="text"
          name="tax_number"
          label="Tax No"
          onChange={handleChange}
          value={values?.tax_number}
          placeholder="Enter tax identification (GST,VAT)"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Select
          name="currency"
          label="Business Currency"
          onChange={handleChange}
          value={values?.currency ?? ""}
          placeholder="Currency"
          options={[
            { label: "$ Dollar", value: "us-dollar" },
            { label: "€ Euro", value: "euro" },
          ]}
        />
        <MultiSelect
          onValueChange={handleChange}
          defaultValue={values?.payment_methods ?? []}
          name="payment_methods"
          label="Payment Terms"
          placeholder="Select Payments"
          variant="inverted"
          maxCount={3}
          options={paymentMethods}
        />
      </div>

      <Input
        type="text"
        name="meta_title"
        label="Meta Title"
        onChange={handleChange}
        value={values?.meta_title}
        placeholder="SEO Title"
        maxLength={500}
        showCharCount
      />

      <Input
        type="text"
        name="meta_description"
        label="Meta Description"
        onChange={handleChange}
        value={values?.meta_description}
        placeholder="SEO Meta Description"
        maxLength={500}
        showCharCount
      />
    </>
  );
};

export default Information;
