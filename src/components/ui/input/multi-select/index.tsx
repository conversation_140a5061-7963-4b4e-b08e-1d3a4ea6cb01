import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import {
  CheckIcon,
  ChevronDown,
  PlusSquare,
  WandSparkles,
  XCircle,
  XIcon,
} from "lucide-react";
import * as React from "react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import classNames from "classnames";
import InputLabel from "../components/label";

/**
 * Variants for the multi-select component to handle different styles.
 * Uses class-variance-authority (cva) to define different styles based on "variant" prop.
 */
const multiSelectVariants = cva(
  "m-1 transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300",
  {
    variants: {
      variant: {
        default:
          "border-foreground/10 text-foreground bg-card hover:bg-card/80",
        secondary:
          "border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        inverted: "inverted",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

type MultiSelectTarget = {
  target: {
    value: string[];
    name: string;
  };
};

/**
 * Props for MultiSelect component
 */

interface OptionType {
  label: string;
  /** The unique value associated with the option. */
  value: string;
  /** Optional icon component to display alongside the option. */
  icon?: React.ComponentType<{ className?: string }>;
  [key: string]: any;
}

interface MultiSelectProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof multiSelectVariants> {
  /**
   * An array of option objects to be displayed in the multi-select component.
   * Each option object has a label, value, and an optional icon.
   */
  options: OptionType[];
  optionKeys?: {
    label?: string;
    value?: string;
    icon?: string;
  };

  name?: string;

  /**
   * Callback function triggered when the selected values change.
   * Receives an array of the new selected values.
   */
  onValueChange: (value: MultiSelectTarget) => void;

  /** The default selected values when the component mounts. */
  defaultValue?: string[];
  value?: string[];

  /**
   * Placeholder text to be displayed when no values are selected.
   * Optional, defaults to "Select options".
   */
  placeholder?: string;

  morePlaceholder?: string;

  /**
   * Animation duration in seconds for the visual effects (e.g., bouncing badges).
   * Optional, defaults to 0 (no animation).
   */
  animation?: number;

  /**
   * Maximum number of items to display. Extra selected items will be summarized.
   * Optional, defaults to 3.
   */
  maxCount?: number;

  /**
   * The modality of the popover. When set to true, interaction with outside elements
   * will be disabled and only popover content will be visible to screen readers.
   * Optional, defaults to false.
   */
  modalPopover?: boolean;

  /**
   * If true, renders the multi-select component as a child of another component.
   * Optional, defaults to false.
   */
  asChild?: boolean;

  /**
   * If true, can add new option with searched keyword.
   * Optional, defaults to false.
   */
  addNew?: boolean;

  /**
   * Additional class names to apply custom styles to the multi-select component.
   * Optional, can be used to add custom styles.
   */
  className?: string;
  containerClassName?: string;
  label?: string;
  labelClassName?: string;
}

const MultiSelect = React.memo(
  React.forwardRef<HTMLButtonElement, MultiSelectProps>(
    (
      {
        options = [],
        optionKeys,
        name,
        onValueChange,
        variant,
        defaultValue = [],
        value = [],
        placeholder = "Select options",
        morePlaceholder = "more",
        animation = 0,
        maxCount = 3,
        modalPopover = false,
        asChild = false,
        className,
        label,
        labelClassName,
        containerClassName,
        addNew,
        ...props
      },
      ref
    ) => {
      const [selectedValues, setSelectedValues] =
        React.useState<string[]>(defaultValue);
      const [isPopoverOpen, setIsPopoverOpen] = React.useState(false);
      const [isAnimating, setIsAnimating] = React.useState(false);
      const [searchTerm, setSearchTerm] = React.useState("");
      const [commandListCount, setCommandListCount] =
        React.useState<number>(20);
      const [optionData, setOptionData] = React.useState<OptionType[]>([]);
      
      const getLabel = (option: OptionType) => {
        try {
          if (optionKeys && optionKeys?.label) {
            return option?.[optionKeys?.label];
          }

          return option?.label;
        } catch {
          return option?.label;
        }
      };

      const getValue = React.useCallback(
        (option: OptionType) => {
          try {
            if (optionKeys && optionKeys?.value) {
              return option?.[optionKeys?.value];
            }

            return option?.value;
          } catch {
            return option?.value;
          }
        },
        [optionKeys]
      );

      React.useEffect(() => {
        if (!defaultValue || !Array.isArray(defaultValue)) return;

        const currentValues = options?.map(getValue) || [];

        const missingDefaults = defaultValue.filter(
          (val) => !currentValues.includes(val)
        );

        if (missingDefaults?.length > 0) {
          const addedOptions = missingDefaults?.map((val) => ({
            label: val,
            value: val,
          }));

          const newOptions = [...addedOptions, ...(options ?? [])];
          const newValues = newOptions.map(getValue);
          const prevValues = optionData.map(getValue);

          const isDifferent =
            newValues.length !== prevValues.length ||
            !newValues.every((v, i) => v === prevValues[i]);

          if (isDifferent) {
            setOptionData(newOptions);
          }
        } else if (optionData && optionData?.length === 0) {
          setOptionData(options);
        }
      }, [defaultValue, getValue, optionData, options]);

      React.useEffect(() => {
        if (selectedValues?.length === 0 && defaultValue.length > 0) {
          setSelectedValues(defaultValue);
        }
      }, [defaultValue, selectedValues?.length]);

      const handleValueChange = (value: string[]) => {
        try {
          if (onValueChange) {
            onValueChange({
              target: {
                value: value,
                name: name ?? "multi-select",
              },
            });
          }
        } catch (error) {
          console.error(error);
        }
      };

      const handleInputKeyDown = (
        event: React.KeyboardEvent<HTMLInputElement>
      ) => {
        if (event.key === "Enter") {
          setIsPopoverOpen(true);
        } else if (event.key === "Backspace" && !event.currentTarget.value) {
          const newSelectedValues = [...selectedValues];
          newSelectedValues.pop();
          setSelectedValues(newSelectedValues);
          handleValueChange(newSelectedValues);
        }
      };

      const toggleOption = (option: string) => {
        const newSelectedValues = selectedValues.includes(option)
          ? selectedValues.filter((value) => value !== option)
          : [...selectedValues, option];
        setSelectedValues(newSelectedValues);
        handleValueChange(newSelectedValues);
      };

      const handleClear = () => {
        setSelectedValues([]);
        handleValueChange([]);
      };

      const handleTogglePopover: any = () => {
        setIsPopoverOpen((prev) => !prev);
      };

      const clearExtraOptions = () => {
        const newSelectedValues = selectedValues.slice(0, maxCount);
        setSelectedValues(newSelectedValues);
        handleValueChange(newSelectedValues);
      };

      const toggleAll = () => {
        if (optionData && selectedValues?.length === optionData?.length) {
          handleClear();
        } else {
          const allValues: string[] = Array.isArray(optionData)
            ? optionData?.map((option) => getValue(option))
            : [];
          setSelectedValues(allValues);
          handleValueChange(allValues);
        }
      };

      return (
        <Popover
          open={isPopoverOpen}
          onOpenChange={setIsPopoverOpen}
          modal={modalPopover}
        >
          <div
            className={classNames(
              "flex flex-col gap-1.5 items-center",
              containerClassName
            )}
          >
            {label && (
              <InputLabel className={classNames("w-full", labelClassName)}>
                {label}
              </InputLabel>
            )}
            <PopoverTrigger asChild>
              <Button
                ref={ref}
                {...props}
                onClick={handleTogglePopover}
                className={classNames(
                  "multi-select-trigger flex w-full rounded-md border min-h-input h-auto items-center justify-between [&_svg]:pointer-events-auto",
                  "bg-white hover:bg-slate-50 !border-light !py-1 !px-3 shadow-none",
                  className
                )}
                value={value}
              >
                {selectedValues?.length > 0 ? (
                  <div className="trigger-wrapper flex justify-between items-center w-full">
                    <div className="selected-container flex flex-wrap items-center">
                      {selectedValues?.slice(0, maxCount).map((value) => {
                        const option =
                          Array.isArray(optionData) && optionData?.length > 0
                            ? optionData?.find((o) => getValue(o) === value)
                            : null;

                        const IconComponent = option?.icon;

                        if (!option) return null;

                        return (
                          <Badge
                            key={value}
                            className={classNames(
                              "capitalize",
                              isAnimating ? "animate-bounce" : "",
                              multiSelectVariants({ variant })
                            )}
                            style={{ animationDuration: `${animation}s` }}
                            variant="destructive"
                          >
                            {IconComponent && (
                              <IconComponent className="h-4 w-4 mr-2" />
                            )}
                            {getLabel(option)}
                            <XCircle
                              className="ml-2 h-4 w-4 cursor-pointer"
                              onClick={(event) => {
                                event.stopPropagation();
                                toggleOption(value);
                              }}
                            />
                          </Badge>
                        );
                      })}
                      {selectedValues?.length > maxCount && (
                        <Badge
                          className={classNames(
                            "bg-transparent text-font border-font/1 hover:bg-transparent",
                            isAnimating ? "animate-bounce" : "",
                            multiSelectVariants({ variant })
                          )}
                          style={{ animationDuration: `${animation}s` }}
                          variant="destructive"
                        >
                          {`+ ${
                            selectedValues?.length - maxCount
                          } ${morePlaceholder}`?.trim()}
                          <XCircle
                            className="ml-2 h-4 w-4 cursor-pointer"
                            onClick={(event) => {
                              event.stopPropagation();
                              clearExtraOptions();
                            }}
                          />
                        </Badge>
                      )}
                      <span className="text-sm text-font/50 mx-1">
                        {placeholder}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <XIcon
                        className="h-4 mx-2 cursor-pointer text-muted-foreground"
                        onClick={(event) => {
                          event.stopPropagation();
                          handleClear();
                        }}
                      />
                      <Separator
                        orientation="vertical"
                        className="flex min-h-6 h-full"
                      />
                      <ChevronDown className="h-4 mx-2 cursor-pointer text-muted-foreground" />
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-between w-full mx-auto">
                    <span className="text-sm text-font/50 mx-1">
                      {placeholder}
                    </span>
                    <ChevronDown className="h-4 cursor-pointer text-muted-foreground mx-2" />
                  </div>
                )}
              </Button>
            </PopoverTrigger>
          </div>
          <PopoverContent
            className="p-0 bg-white"
            align="start"
            onEscapeKeyDown={() => setIsPopoverOpen(false)}
          >
            <Command>
              <CommandInput
                placeholder={addNew ? "Search or + Add new" : "Search..."}
                onKeyDown={handleInputKeyDown}
                className="focus:outline-0 focus:ring-0 h-input px-3 !py-1"
                containerClassName="pb-1"
                onValueChange={(value) => setSearchTerm(value)}
              />
              <CommandList
                onScroll={() => setCommandListCount((prev) => prev + 15)}
              >
                <CommandEmpty className={addNew ? "!py-3" : ""}>
                  {addNew ? (
                    <button
                      className="text-sm font-normal text-start px-3 flex items-center justify-start gap-2 w-full"
                      onClick={(e) => {
                        e?.stopPropagation();
                        const newOption: any = {
                          [optionKeys && optionKeys?.label
                            ? optionKeys?.label
                            : "label"]: searchTerm,
                          [optionKeys && optionKeys?.value
                            ? optionKeys?.value
                            : "value"]: searchTerm,
                        };

                        if (
                          !(
                            Array.isArray(optionData) && optionData?.length > 0
                          ) ||
                          !optionData?.find(
                            (option) => option.label === searchTerm
                          )
                        ) {
                          setOptionData((prev) => [newOption, ...(prev ?? [])]);
                        }

                        toggleOption(getValue(newOption));
                        setIsPopoverOpen(false);
                      }}
                    >
                      <PlusSquare className="h-4 w-4 text-main" />
                      <span>
                        Add <b>{`"${searchTerm}"`}</b>
                      </span>
                    </button>
                  ) : (
                    <div className="text-base text-red-500 px-3 py-6 text-center">
                      No results found.
                    </div>
                  )}
                </CommandEmpty>
                <CommandGroup>
                  {Array.isArray(optionData) && optionData?.length > 0 && (
                    <CommandItem
                      key="all"
                      onSelect={toggleAll}
                      className="cursor-pointer"
                    >
                      <div
                        className={classNames(
                          "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                          selectedValues?.length === optionData?.length
                            ? "bg-primary text-primary-foreground"
                            : "opacity-50 [&_svg]:invisible"
                        )}
                      >
                        <CheckIcon className="h-4 w-4 text-white" />
                      </div>
                      <span>(Select All)</span>
                    </CommandItem>
                  )}

                  {Array.isArray(optionData) &&
                    optionData?.length > 0 &&
                    optionData?.slice(0, commandListCount)?.map((option) => {
                      const isSelected = selectedValues.includes(
                        getValue(option)
                      );
                      return (
                        <CommandItem
                          key={getValue(option)}
                          onSelect={() => toggleOption(getValue(option))}
                          className="cursor-pointer"
                        >
                          <div
                            className={classNames(
                              "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                              isSelected
                                ? "bg-primary text-primary-foreground"
                                : "opacity-50 [&_svg]:invisible"
                            )}
                          >
                            <CheckIcon className="h-4 w-4 text-white" />
                          </div>
                          {React.isValidElement(option.icon) && (
                            <option.icon className="mr-2 h-4 w-4 text-font/50" />
                          )}
                          <span>{getLabel(option)}</span>
                        </CommandItem>
                      );
                    })}
                </CommandGroup>
                <CommandSeparator />
                <CommandGroup>
                  <div className="flex items-center justify-between">
                    {selectedValues?.length > 0 && (
                      <>
                        <CommandItem
                          onSelect={handleClear}
                          className="flex-1 justify-center cursor-pointer"
                        >
                          Clear
                        </CommandItem>
                        <Separator
                          orientation="vertical"
                          className="flex min-h-6 h-full"
                        />
                      </>
                    )}
                    <CommandItem
                      onSelect={() => setIsPopoverOpen(false)}
                      className="flex-1 justify-center cursor-pointer max-w-full"
                    >
                      Close
                    </CommandItem>
                  </div>
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
          {animation > 0 && selectedValues?.length > 0 && (
            <WandSparkles
              className={classNames(
                "cursor-pointer my-2 text-foreground bg-background w-3 h-3",
                isAnimating ? "" : "text-muted-foreground"
              )}
              onClick={() => setIsAnimating(!isAnimating)}
            />
          )}
        </Popover>
      );
    }
  )
);

MultiSelect.displayName = "MultiSelect";

export { MultiSelect };
export type { MultiSelectTarget };
