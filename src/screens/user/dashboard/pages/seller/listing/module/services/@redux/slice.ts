import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { thunks } from "./thunk";

interface APTStateType {
  servicesList: APIResponseType;
  serviceDetails: APIResponseType;
  dynamicFields: APIResponseType;
  serviceSuggestions: APIResponseType;
}

const APIState: APTStateType = {
  servicesList: {
    data: null,
    loading: false,
    meta: null,
  },
  serviceDetails: {
    data: null,
    loading: false,
    meta: null,
  },
  dynamicFields: {
    data: null,
    loading: false,
    meta: null,
  },
  serviceSuggestions: {
    data: null,
    loading: true,
    meta: null,
  },
};

interface stateType extends APTStateType {
  searchValue: string;
  currentPage: string;
  suggestedService: any;
}

const initialState = {
  searchValue: "",
  currentPage: "1",
  suggestedService: null,
  ...APIState,
} satisfies stateType as stateType;

export const slice = createSlice({
  name: "user_service",
  initialState,
  reducers: {
    setSearchValue: (state, action: PayloadAction<string>) => {
      state.searchValue = action.payload;
    },
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    setServiceDetails: (
      state,
      action: PayloadAction<Record<string, any> | null>
    ) => {
      if (action?.payload) {
        state.serviceDetails.data = {
          ...(state.serviceDetails.data || {}),
          ...action.payload,
        };
      } else {
        state.serviceDetails = { ...initialState.serviceDetails };
      }
    },
    setSuggestedService: (state, action: PayloadAction<any>) => {
      state.suggestedService = action.payload;
    },
    setDynamicValues: (
      state,
      action: PayloadAction<Record<string, any> | null>
    ) => {
      const payload = action?.payload;
      if (!payload) {
        if (state.serviceDetails.data) {
          state.serviceDetails.data.metadata = null;
        }
        return;
      }

      const metadata = state.serviceDetails.data?.metadata || {
        fieldsData: [],
        measurementsData: [],
      };

      const currentFields = metadata.fieldsData ?? [];
      const currentMeasurements = metadata.measurementsData ?? [];

      let updatedFields = currentFields;
      if ("fieldId" in payload) {
        const index = currentFields.findIndex(
          (v: any) => v?.fieldId === payload.fieldId
        );
        if (index !== -1) {
          updatedFields = [...currentFields];
          updatedFields[index] = payload;
        } else {
          updatedFields = [...currentFields, payload];
        }
      }

      let updatedMeasurements = currentMeasurements;
      if ("measurementId" in payload) {
        const index = currentMeasurements.findIndex(
          (v: any) => v?.measurementId === payload.measurementId
        );
        if (index !== -1) {
          updatedMeasurements = [...currentMeasurements];
          updatedMeasurements[index] = payload;
        } else {
          updatedMeasurements = [...currentMeasurements, payload];
        }
      }

      state.serviceDetails.data = {
        ...(state.serviceDetails.data || {}),
        metadata: {
          ...metadata,
          fieldsData: updatedFields,
          measurementsData: updatedMeasurements,
        },
      };
    },
  },
  extraReducers: (builder) => {
    Object.entries(thunks).forEach(([_, thunk]) => {
      createThunkCase(builder, thunk, APIState);
    });
  },
});

export const Actions = slice.actions;
export default slice.reducer;
