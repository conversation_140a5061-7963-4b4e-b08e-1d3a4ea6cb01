import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";
import privateApiClient from "@/services/axios/private-api-client";

const formDataConfig = {
  headers: {
    "Content-Type": "multipart/form-data",
  },
};

export const getProducts = async ({ searchTerm, page, limit }: any = {}) => {
  return await privateApiClient.get(apiRoutes().user.seller.products.fetchAll, {
    params: { searchTerm, page, limit },
  });
};

export const getProductSuggestions = async ({
  searchTerm,
}: {
  searchTerm: string;
}) => {
  return await privateApiClient.get(
    apiRoutes().user.seller.products.suggestions,
    {
      params: { searchTerm },
    }
  );
};

export const getProductPreValues = async ({
  suggestionId,
}: {
  suggestionId: string;
}) => {
  return await privateApiClient.get(
    `${apiRoutes().user.seller.products.preValues}/${suggestionId}`
  );
};

export const getProduct = async (product_id?: string) => {
  return await privateApiClient.get(
    `${apiRoutes().user.seller.products.fetch}/${product_id}`
  );
};

export const updateProductDetails = async (
  payload: FormData,
  product_id?: string
) => {
  return await formDataClient.post(
    apiRoutes().user.seller.products.addUpdate,
    payload,
    {
      ...formDataConfig,
      params: { productId: product_id },
    }
  );
};

export const deleteProduct = async (product_id?: string) => {
  return await privateApiClient.delete(
    `${apiRoutes().user.seller.products.delete}/${product_id}`
  );
};
