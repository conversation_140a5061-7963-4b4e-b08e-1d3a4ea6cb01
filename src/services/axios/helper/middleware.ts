import { AxiosInstance } from "axios";

export const interceptor = (apiClient: AxiosInstance) => {
  try {
    apiClient.interceptors.request.use(
      (config) => {
        if (typeof window !== "undefined") {
          const token = localStorage.getItem("user_access_token");
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }

        return config;
      },
      (err) => {
        return Promise.reject(err);
      }
    );

    let isRefreshing = false;

    apiClient.interceptors.response.use(
      (response) => {
        if (response?.status >= 200 && response?.status < 300) {
          response = {
            ...response?.data,
            type: "success",
          };
        }

        return response;
      },
      async (error) => {
        const prevRequest = error?.config;
        error = {
          ...error,
          error: true,
        };

        if (
          error?.response?.status === 403 &&
          !prevRequest?.sent &&
          !isRefreshing
        ) {
          isRefreshing = true;
          prevRequest.sent = true;

          try {
            return apiClient(prevRequest);
          } catch (err) {
            console.error(err);
          } finally {
            isRefreshing = false;
          }
        }
        error = {
          error: true,
          type: "error",
          meta: error?.response?.data?.meta ?? {
            message: error?.message ?? "something went wrong",
          },
          ...error,
        };
        return Promise.reject(error);
      }
    );
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
