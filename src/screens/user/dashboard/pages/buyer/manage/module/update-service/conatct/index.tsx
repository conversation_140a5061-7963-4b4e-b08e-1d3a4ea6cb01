'use client';

import { useDispatch, useSelector } from "react-redux";
import { ChangeEvent } from "react";
import { Input } from "@/components/ui/input/text";
import { TextEditor } from "@/components/ui/input/texteditor";
import { updateRFQDetailsSelector } from "../../../@redux/selector";
import { Actions, RFQUpdateValuesType } from "@/screens/user/dashboard/pages/buyer/manage/@redux/slice";
import { RootDispatch } from "@/redux/store";

const CONTACT_INFO_FIELDS = ["name", "email", "phone", "companyName", "notes", "phoneCode"];

const Contact = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(updateRFQDetailsSelector);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | { target: { name: string; value: string } }
  ) => {
    try {
      const payload: any = {};
      const fieldName = e?.target?.name;
      const fieldValue = e?.target?.value;

      if (CONTACT_INFO_FIELDS.includes(fieldName)) {
        payload.contactInfo = {
          ...(typeof values?.contactInfo === "object" && values?.contactInfo !== null
            ? values.contactInfo
            : {}),
          [fieldName]: fieldValue,
        };
      } else {
        payload[fieldName] = fieldValue;
      }

      dispatch(Actions.setRFQDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  // For TextEditor (rich text) which may not send an event
  const handleNotesChange = (val: string) => {
    const payload: any = {
      contactInfo: {
        ...(typeof values?.contactInfo === "object" && values?.contactInfo !== null
          ? values.contactInfo
          : {}),
        notes: val,
      },
    };
    dispatch(Actions.setRFQDetails(payload));
  };

// Fixed handlePhoneCodeChange function with type assertion
  const handlePhoneCodeChange = (e: any) => {
    try {
      const payload = {
        contactInfo: {
          ...(typeof values?.contactInfo === "object" && values?.contactInfo !== null
            ? values.contactInfo
            : {}),
          phoneCode: e.target.value
        }
      };
      // Type assertion to bypass TypeScript error
      dispatch(Actions.setRFQDetails(payload as any));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Input
          type="text"
          name="name"
          label="Contact Name"
          onChange={handleChange}
          value={values?.contactInfo?.name ?? ""}
          placeholder="Representative's full name"
          containerClassName="flex-1"
        />
        <Input
          type="text"
          name="email"
          label="Email Address"
          onChange={handleChange}
          value={values?.contactInfo?.email ?? ""}
          placeholder="Business email ID"
          containerClassName="flex-1"
        />
        <Input
          type="text"
          name="phone"
          label="Phone Number"
          onChange={handleChange}
          value={values?.contactInfo?.phone ?? ""}
          placeholder="Phone number"
          containerClassName="flex-1"
          countrySelector={{
                value: values?.contactInfo?.phoneCode ?? "",
                onChange: handlePhoneCodeChange
              }}
        />
        <Input
          type="text"
          name="companyName"
          label="Company Name"
          onChange={handleChange}
          value={values?.contactInfo?.companyName ?? ""}
          placeholder="Company Name"
          containerClassName="flex-1"
        />
      </div>
      <div className="flex flex-col md:flex-row items-start gap-6 mt-4">
        <TextEditor
          name="notes"
          value={values?.contactInfo?.notes ?? ""}
          onChange={handleChange}
          containerClassName="flex-1"
          className="flex-1 rounded-md"
          label="Additional Notes"
        />
      </div>
    </>
  );
};

export default Contact;
