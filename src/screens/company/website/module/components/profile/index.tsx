import RATING_STAR from "/assets/pages/rating-star.svg";
import VERIFIES_GRAY from "/assets/pages/verified-gray.svg";
import { ListingData } from "@/constants/listing";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const company = ListingData?.[0];

const CompanyProfile = () => {
  return (
    <div className="w-full bg-gradient-to-r from-[#FFFCFC] to-[#FFF7F5] pt-8 pb-6">
      <div className="container">
        <div className="flex flex-col sm:flex-row items-start gap-5">
          <div className="w-full h-full sm:w-max flex items-start justify-between gap-4">
            <img
              src={company?.images?.[0]}
              alt="flag"
              className="flex-1 aspect-square w-full max-w-28 h-full rounded-sm object-cover"
            />
          </div>
          <div className="flex-1 flex flex-col gap-5">
            <div className="w-full flex-1 flex flex-col sm:flex-row items-start justify-between gap-4">
              <div className="w-full order-2 sm:order-1 flex items-start flex-col gap-2">
                <div className="flex items-center justify-start gap-1.5">
                  <h2
                    role="button"
                    className="text-xl md:text-3xl font-bold text-black hover:drop-shadow"
                  >
                    {company?.name}
                  </h2>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <img
                        src={VERIFIES_GRAY}
                        alt="flag"
                        className="flex-1 aspect-auto w-5 h-auto object-contain"
                      />
                    </TooltipTrigger>
                    <TooltipContent>Verified</TooltipContent>
                  </Tooltip>
                </div>

                <div className="flex flex-col md:flex-row items-start gap-3 text-wrap [&>*:not(:last-child)]:border-r [&>*:not(:last-child)]:border-slate-300 [&>*:not(:last-child)]:pr-4">
                  {/* Location */}
                  <div className="flex items-center gap-1.5 mt-1 w-max">
                    <img
                      src={company?.location?.flag}
                      alt="flag"
                      className="flex-1 aspect-auto max-w-6 w-6 h-auto object-contain"
                    />
                    <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 whitespace-nowrap">
                      <span>
                        {company?.location?.address}
                        {", "}
                      </span>
                      <span>{company?.location?.label}</span>
                    </div>
                  </div>

                  {/* Reviews */}
                  <div className="flex items-center gap-2.5 mt-0.5 w-max">
                    <img
                      src={RATING_STAR}
                      alt="flag"
                      className="aspect-auto w-5 h-5 object-contain"
                    />
                    <div className="flex item flex-wrap gap-2.5 [&>*]:text-sm [&>*]:font-medium [&>*]:text-font [&>*]:leading-4">
                      <span className="border-r border-slate-300 pr-2.5">
                        {company?.rating?.stars}/5
                      </span>
                      <span>{company?.rating?.reviewsCount} Reviews</span>
                    </div>
                  </div>
                </div>

                {/* short intro  */}
                <div className="text-sm text-stone-600 mt-0.5">
                  <p style={{ color: "#29100C" }}>{company.shortIntro}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyProfile;
