import { But<PERSON> } from "@/components/ui/button";
import classNames from "classnames";
import { Check, X } from "lucide-react";
import { FC } from "react";

export interface ChecklistType {
  label: string;
  available: boolean;
}

type ClassNameType = {
  className?: string;
};

export interface PriceType {
  currency?: string;
  price?: string;
  period?: keyof typeof periodAbbreviation;
}

interface StartPriceCardType extends PriceType, ClassNameType {
  icon?: string;
  title: string;
  description?: string;
  buttonTitle?: string;
  onSelect?: () => void;
  checklist?: ChecklistType[];
}

export const periodAbbreviation = {
  month: "mo",
  year: "yr",
};

const StartPriceCard: FC<StartPriceCardType> = ({
  className,
  icon = "/assets/pages/rating-star.svg",
  title,
  description,
  price,
  currency,
  period,
  buttonTitle = "Get Started for Free",
  onSelect,
  checklist,
}) => {
  return (
    <div
      className={classNames(
        "h-full bg-white shadow-md hover:shadow-lg rounded-md px-7 py-10",
        className
      )}
    >
      <div className="w-full h-full flex flex-col items-center gap-2.5">
        <div className="w-full flex flex-col items-center gap-5">
          <img
            src={icon}
            alt="flag"
            className="aspect-auto w-7 h-7 object-contain"
          />

          <h5 className="font-bold text-sm">{title}</h5>

          {description && (
            <p className="text-xs text-font/70 max-w-full md:max-w-xs text-center m-auto">
              {description}
            </p>
          )}

          <PriceRender
            price={price}
            currency={currency}
            period={period}
            className="mt-4"
          />

          <Button variant="main-revert" onClick={() => onSelect?.()}>
            {buttonTitle}
          </Button>
        </div>
        <CheckList data={checklist} className="mt-8" />
      </div>
    </div>
  );
};

export const PriceRender: FC<PriceType & ClassNameType> = ({
  className,
  price,
  currency = "$",
  period,
}) => {
  return (
    <div
      className={classNames(
        "flex items-start gap-[0.2] text-4xl leading-5 tracking-wide font-semibold",
        className
      )}
    >
      <sup className="text-sm -translate-y-1/3">{currency}</sup>
      <h2 className="">
        {price}
        {period && "/"}
      </h2>
      <sub className="text-lg">{period && periodAbbreviation[period]}</sub>
    </div>
  );
};

export const CheckList = ({
  data,
  className,
}: {
  data?: ChecklistType[];
  className?: string;
}) => {
  return (
    <>
      {Array.isArray(data) && data?.length > 0 && (
        <div
          className={classNames(
            "w-full flex flex-col items-start justify-start gap-8",
            className
          )}
        >
          {data?.map((check, index) => {
            return (
              <div
                key={index}
                className="flex items-start justify-start gap-2 text-xs text-slate-700/90 font-semibold"
              >
                {check?.available ? (
                  <Check className="w-4 h-4 mt-[0.05rem] text-green-500" />
                ) : (
                  <X className="w-4 h-4 mt-[0.05rem] text-red-500" />
                )}
                <span>{check?.label}</span>
              </div>
            );
          })}
        </div>
      )}
    </>
  );
};

export default StartPriceCard;
