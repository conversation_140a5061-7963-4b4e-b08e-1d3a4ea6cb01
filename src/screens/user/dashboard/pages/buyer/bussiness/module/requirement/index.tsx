'use client';

import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import { ChangeEvent, useEffect } from "react";
import { businessDetails } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/slice";
import { useDispatch, useSelector } from "react-redux";
import { getBusinessDetails } from "../../@redux/thunk";
import { RootDispatch } from "@/redux/store";

interface SourcingInfoProps {
  onValidationChange?: (isValid: boolean) => void;
  isSubmitted?: boolean;
}

const SourcingInfo = ({ onValidationChange }: SourcingInfoProps) => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values = {} } = useSelector(businessDetails);

  // Fetch business details when component mounts
  useEffect(() => {
    dispatch(getBusinessDetails())
      .unwrap()
      .then((response) => {
        if (response?.data) {
          const formData = {
            sourcing_regions: response.data.sourcing_regions || [],
            annual_purchasing_volume: response.data.annual_purchasing_volume || '',
            primary_sourcing_purpose: response.data.primary_sourcing_purpose || '',
            sourcing_frequency: response.data.sourcing_frequency || ''
          };
          dispatch(Actions.setFormValues(formData));
        }
      })
      .catch((error) => {
        console.error('Error fetching business details:', error);
      });
  }, [dispatch]);

  useEffect(() => {
    const isValid = Boolean(
      values?.annual_purchasing_volume &&
      values?.primary_sourcing_purpose &&
      values?.sourcing_frequency &&
      values?.sourcing_regions?.length
    );
    onValidationChange?.(isValid);
  }, [values, onValidationChange]);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
  ) => {
    try {
      const payload = {
        [e?.target?.name]: e?.target?.value,
      };
      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const getDisplayValue = (value: any) => {
    if (!value) return [];
    if (Array.isArray(value)) {
      return value.map(item => {
        if (typeof item === 'object' && item !== null) {
          return item.name || item.value || item._id || '';
        }
        return item;
      });
    }
    return [];
  };

  const sourcingPurposeOptions = [
    { label: "Resale", value: "Resale" },
    { label: "Personal Use", value: "Personal Use" },
    { label: "Business Operations", value: "Business Operations" },
    { label: "Manufacturing", value: "Manufacturing" },
  ];

  const sourcingFrequencyOptions = [
    { label: "One-time", value: "One-time" },
    { label: "Weekly", value: "Weekly" },
    { label: "Monthly", value: "Monthly" },
    { label: "Quarterly", value: "Quarterly" },
    { label: "Yearly", value: "Yearly" },
  ];

  const regionOptions = [
    { label: "Local", value: "Local" },
    { label: "Asia", value: "Asia" },
    { label: "Europe", value: "Europe" },
    { label: "North America", value: "North America" },
    { label: "South America", value: "South America" },
    { label: "Africa", value: "Africa" },
    { label: "Australia", value: "Australia" },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Annual Purchasing Volume
          </label>
          <div className="relative">
            <Input
              type="text"
              name="annual_purchasing_volume"
              onChange={handleChange}
              value={values?.annual_purchasing_volume || ''}
              placeholder="Enter volume"
              className="w-full rounded-md"
            />
          </div>
        </div>

        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Primary Sourcing Purpose
          </label>
          <Select
            name="primary_sourcing_purpose"
            onChange={handleChange}
            value={values?.primary_sourcing_purpose || ''}
            placeholder="Select Purpose"
            options={sourcingPurposeOptions}
            className="p-2 border rounded-md"
          />
        </div>

        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Sourcing Frequency
          </label>
          <Select
            name="sourcing_frequency"
            onChange={handleChange}
            value={values?.sourcing_frequency || ''}
            placeholder="Select Frequency"
            options={sourcingFrequencyOptions}
            className="p-2 border rounded-md"
          />
        </div>

        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Preferred Sourcing Regions
          </label>
          <MultiSelect
            onValueChange={(e) => {
              dispatch(Actions.setFormValues({
                sourcing_regions: e.target.value
              }));
            }}
            name="sourcing_regions"
            placeholder="Select Regions"
            variant="inverted"
            maxCount={5}
            options={regionOptions}
            defaultValue={getDisplayValue(values?.sourcing_regions)}
            value={getDisplayValue(values?.sourcing_regions)}
          />
        </div>
      </div>
    </div>
  );
};

export default SourcingInfo;
