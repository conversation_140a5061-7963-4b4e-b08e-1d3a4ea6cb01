const socialData: any[] = [
  {
    title: "Facebook",
    icon: "/assets/footer/social/facebook.svg",
    url: "http://facebook.com",
  },
  {
    title: "Instagram",
    icon: "/assets/footer/social/insta.svg",
    url: "http://instagram.com",
  },
  {
    title: "Linkedln",
    icon: "/assets/footer/social/linkedln.svg",
    url: "http://linkedin.com",
  },
  {
    title: "X",
    icon: "/assets/footer/social/x.svg",
    url: "http://x.com",
  },
  {
    title: "Youtube",
    icon: "/assets/footer/social/youtube.svg",
    url: "http://youtube.com",
  },
];

const handleNavigate = (url: string) => {
  try {
    window.open(url, "_blank");
  } catch (error) {
    console.error(error);
  }
};

const FooterSocial = () => {
  return (
    <div className="flex items-center flex-wrap gap-2">
      {socialData?.map((sc, index) => (
        <button
          key={index}
          className="flex items-center justify-center"
          onClick={() => handleNavigate(sc?.url)}
        >
          <img
            src={sc?.icon}
            alt={sc?.title}
            className="w-11 h-11 object-contain"
          />
        </button>
      ))}
    </div>
  );
};

export default FooterSocial;
