"use client";

import <PERSON><PERSON><PERSON> from "@/components/elements/chart/pie-chart";
import { chartColors } from "@/components/ui/chart";
import classNames from "classnames";
import { FC } from "react";

interface PieReportType {
  className?: string;
}

const colors = chartColors();
const PieReport: FC<PieReportType> = ({ className }) => {
  const matrix = [
    {
      color: colors?.[0],
      title: "Open",
      count: 6348,
    },
    {
      color: colors?.[1],
      title: "New",
      count: 6348,
    },
    {
      color: colors?.[2],
      title: "Won",
      count: 4634,
    },
  ];

  return (
    <main className={classNames("bg-white shadow-box p-4", className)}>
      <div className="flex items-center gap-4 justify-between mb-3">
        <span className="text-sm md:text-base font-semibold">RFQ Received</span>
        <button className="text-xs hover:text-main transition-all">
          See all
        </button>
      </div>
      <PieChart height={180} width="auto" />
      <div className="flex items-center flex-wrap gap-4 justify-between mt-6">
        {matrix?.map((mt, index) => {
          return (
            <div
              key={index}
              className="flex items-center gap-1.5 text-sm md:text-base"
            >
              <span
                className="w-2 h-2 rounded-full"
                style={{ background: mt?.color }}
              />
              <span>{mt?.title}</span>
              <span className="font-bold">{mt?.count}</span>
            </div>
          );
        })}
      </div>
    </main>
  );
};

export default PieReport;
