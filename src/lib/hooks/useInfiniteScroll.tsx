import { useCallback, useEffect, useRef, useState } from "react";

type PaginationType = {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
};

type DataActionType = (args: {
  page?: number;
  limit?: number;
  search?: string;
}) => Promise<any>;

interface UseInfiniteDropdownProps<T = any> {
  action?: DataActionType;
  pagination?: PaginationType;
  search?: string;
  resetAction?: () => any;
}

function useInfiniteScroll<T = any>({
  action,
  pagination = {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    hasNextPage: true,
    hasPrevPage: false,
    limit: 10,
  },
  search = "",
  resetAction,
}: UseInfiniteDropdownProps<T>) {
  const loaderRef = useRef<HTMLDivElement | null>(null);
  const [page, setPage] = useState(pagination?.currentPage);
  const [loading, setLoading] = useState(false);
  const [prevSearch, setPrevSearch] = useState(search);

  useEffect(() => {
    if (search !== prevSearch) {
      setPage(1);
      setPrevSearch(search);
      if (resetAction) resetAction();
    }
  }, [search, prevSearch, resetAction]);

  useEffect(() => {
    if (pagination?.currentPage) {
      setPage(pagination?.currentPage);
    }
  }, [pagination?.currentPage]);

  const loadMore = useCallback(async () => {
    if (loading || !pagination || !pagination?.hasNextPage) return;

    setLoading(true);

    try {
      if (action && typeof action === "function") {
        await action({ page, limit: pagination?.limit, search });
        setPage((prev) => prev + 1);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [loading, pagination, action, page, search]);

  const current = loaderRef.current;
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && pagination?.hasNextPage && !loading) {
          loadMore();
        }
      },
      { threshold: 1.0 }
    );

    if (current) observer.observe(current);

    return () => {
      if (current) observer.unobserve(current);
    };
  }, [loadMore, pagination?.hasNextPage, loading, loaderRef, current]);

  return {
    loaderRef,
    loading,
  };
}

export { useInfiniteScroll };
export type { DataActionType, PaginationType, UseInfiniteDropdownProps };
