'use client';

import * as React from "react";
import { Input } from "@/components/ui/input/text";
import { ChevronDown } from "lucide-react";
import { Dropzone, DropzoneInputTargetType } from "@/components/ui/input/dropzone";
import { Select } from "@/components/ui/input/select";
import { Checkbox } from "@/components/ui/input/checkbox";
import { Button } from "@/components/ui/button";
import CountrySelect from "@/components/ui/input/country-select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/input/radio";
import { Switch } from "@/components/ui/input/switch";

const complaintTypeOptions = [
  { label: "General Feedback", value: "General Feedback" },
  { label: "Technical Issue", value: "Technical Issue" },
  { label: "Billing Problem", value: "Billing Problem" },
  { label: "Other", value: "Other" }
];

const urgencyOptions = [
  { label: "Low Priority", value: "Low Priority" },
  { label: "Medium Priority", value: "Medium Priority" },
  { label: "High Priority", value: "High Priority" }
];

const FeedbackForm = () => {
  const [formData, setFormData] = React.useState({
    complaintType: "General Feedback",
    feedback: "",
    description: "",
    fullName: "",
    email: "",
    countryCode: "+91",
    phone: "7903752028",
    hasInteracted: false,
    urgency: "Low Priority",
    confirmAccurate: false
  });
  
  const [files, setFiles] = React.useState<File[]>([]);

  const handleChange = (e: any) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });
  };
  
  const handleSwitchChange = (checked: boolean) => {
    setFormData({
      ...formData,
      hasInteracted: checked
    });
  };
  
  const handleCountryChange = (value: string) => {
    setFormData({
      ...formData,
      countryCode: value
    });
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement> | DropzoneInputTargetType) => {
    if (e.target.files && e.target.files.length > 0) {
      setFiles(Array.from(e.target.files));
    }
  };
  
  const handleFileDelete = (fileToDelete: File) => {
    setFiles(files.filter(file => file !== fileToDelete));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    console.log("Files:", files);
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-white rounded-lg">
      <h1 className="text-2xl font-bold mb-6">Feedback</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Complaint Type */}
          <div className="mt-1">
            <Select
              name="complaintType"
              value={formData.complaintType}
              onChange={handleChange}
              label="Complaint Type"
              options={complaintTypeOptions}
              className="w-full"
              icon={<ChevronDown className="h-4 w-4 text-gray-500" />}
            />
          </div>

          {/* What is your feedback about */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">What is your feedback or concern about?</label>
            <Input
              name="feedback"
              placeholder="E.G., Problem With A Listing, Website Navigation Issue."
              value={formData.feedback}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Please Provide Details About Your Feedback Or Complaint."
            rows={5}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>

        {/* File Upload using Dropzone */}
        <div className="mb-6">
          <Dropzone
            name="files"
            onChange={handleFileChange}
            value={files}
            preview={true}
            onFileDelete={handleFileDelete}
            formats="JPEG, PNG, PDF"
            containerClassName="bg-white"
            contentClassName="p-6"
          />
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Full Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
            <Input
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-md"
              placeholder="Enter your name"
            />
          </div>

          {/* Email Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
            <Input
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-md"
              placeholder="Enter your email"
            />
          </div>
        </div>

        {/* Phone with Country Selector with decreased width */}
       <div className="flex flex-col w-full max-w-md mb-3">
                   <label className="text-gray-700 text-sm font-medium mb-2">
                      Phone 
                   </label>
                   <Input
                     type="text"
                     name="phone"
                     onChange={handleChange}
                     // value={values?.phone}
                     placeholder="Enter Your Phone Number"
                     className="p-2 border rounded-md"
                     countrySelector={{
                       defaultValue: "+91",
                       name: "countryCode",
                       onChange: handleChange,
                       // value: values?.countryCode,
                     }}
                   />
                 </div>

        {/* Have you interacted - Using built-in Switch component */}
        <div className="mb-6 flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Have you interacted with a seller/buyer on the platform regarding this issue?</span>
          <div className="flex items-center">
            <Switch 
              checked={formData.hasInteracted}
              onCheckedChange={handleSwitchChange}
              name="hasInteracted"
              className="mr-2"
            />
            {/* <span className="text-sm font-medium text-gray-700">{formData.hasInteracted ? '' : 'No'}</span> */}
          </div>
        </div>

        {/* Urgency using the provided RadioGroup and RadioGroupItem components */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">How urgent is this issue?</label>
          <RadioGroup 
            name="urgency" 
            value={formData.urgency} 
            onChange={handleChange}
            className="flex flex-row space-x-6"
          >
            {urgencyOptions.map((option) => (
              <label key={option.value} className="flex items-center">
                <RadioGroupItem value={option.value} className="mr-2" />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </RadioGroup>
        </div>

        {/* Confirmation Checkbox - Styled like reference image */}
        <div className="mb-6 flex items-center">
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              name="confirmAccurate"
              checked={formData.confirmAccurate}
              onChange={handleChange}
              className="sr-only"
            />
            <div className="w-5 h-5 border border-gray-300 mr-2 flex items-center justify-center">
              {formData.confirmAccurate && (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              )}
            </div>
            <span className="text-sm">I confirm that the provided information is accurate.</span>
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-6">
          <Button 
            type="submit" 
            variant="main-revert"
            className="w-full bg-red-500 hover:bg-red-600 text-white py-3 rounded-md"
          >
            Submit Feedback
          </Button>
        </div>
      </form>
    </div>
  );
};

export default FeedbackForm;