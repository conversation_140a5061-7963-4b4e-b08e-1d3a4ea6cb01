"use client";

import { Input } from "@/components/ui/input/text";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useInfiniteScroll } from "@/lib/hooks/useInfiniteScroll";
import useOutsideAlert from "@/lib/hooks/useOutsideAlert";
import { getFilteredData } from "@/lib/utils/data";
import classNames from "classnames";
import { debounce } from "lodash";
import { X } from "lucide-react";
import {
  ChangeEvent,
  forwardRef,
  Fragment,
  Suspense,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { FiSearch } from "react-icons/fi";
import { RiErrorWarningLine, RiResetLeftFill } from "react-icons/ri";
import { Loader } from "../../loader";
import MenuItem from "./list-item";
import { DropdownListDataType, DropdownListType } from "./types";

interface DropdownListRefType {
  scrollTop: () => void;
}

const DropdownList = forwardRef<DropdownListRefType, DropdownListType>(
  (
    {
      data,
      className,
      closeOnSelect,
      search,
      loading,
      checkbox,
      searchPlaceholder,
      onClose,
      menuClassName,
      iconClassName,
      onSelect,
      onSearch,
      value,
      highlightSelected,
      parentRef,
      searchCompareParams,
      optionKeys,
      valueUniqueParam,
      pagination,
      dataAction,
      resetAction,
    },
    ref
  ) => {
    const dropdownRef = useRef<HTMLDivElement>(null);

    const [searchValue, setSearchValue] = useState<string>("");
    const [isScrolled, setIsScrolled] = useState<boolean>(false);
    const [showCount, setShowCount] = useState<number>(100);

    const { loaderRef, loading: infiniteLoading } = useInfiniteScroll({
      action: dataAction,
      pagination,
      search: searchValue,
      resetAction: resetAction,
    });

    const handleClose = () => {
      if (onClose) {
        onClose();
      }
    };

    const handleScroll = () => {
      if (dropdownRef.current) {
        setIsScrolled(dropdownRef.current.scrollTop > 0);
      }
      setShowCount((prev) => prev + 100);
    };

    const handleSelect = (option: DropdownListDataType) => {
      if (onSelect) {
        onSelect(option);
      }
      if (closeOnSelect) {
        handleClose();
      }
    };

    const filteredMenu = getFilteredData({
      data: data,
      compareValue: searchValue,
      compareParams: [
        "label",
        ...(searchCompareParams ?? []).filter(
          (item): item is string => typeof item === "string"
        ),
      ],
    });

    const finalData = Boolean(searchValue) ? filteredMenu : data;
    const isDataValid = Array.isArray(finalData) && finalData?.length > 0;

    const getLabel = useCallback(
      (option: DropdownListDataType) => {
        try {
          if (optionKeys && optionKeys?.label) {
            return option?.[optionKeys?.label];
          }

          return option?.label;
        } catch {
          return option?.label;
        }
      },
      [optionKeys]
    );

    const debouncedSearch = useMemo(
      () =>
        debounce((val: string) => {
          if (
            finalData?.find((item) =>
              getLabel(item)?.toLowerCase()?.includes(val?.toLowerCase())
            )
          ) {
            return;
          }

          if (onSearch) {
            onSearch(val);
          }
        }, 500),
      [finalData, getLabel, onSearch]
    );

    const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
      try {
        setSearchValue(e?.target?.value);
        debouncedSearch(e?.target?.value);
      } catch (error) {
        console.error(error);
      }
    };

    const handleScrollTop = () => {
      try {
        if (dropdownRef.current) {
          dropdownRef.current?.scrollTo({ top: 0, behavior: "auto" });
        }
      } catch (error) {
        console.error(error);
      }
    };

    useImperativeHandle(ref, () => ({
      scrollTop: handleScrollTop,
    }));

    useOutsideAlert(parentRef, handleClose);

    return (
      <div
        ref={dropdownRef}
        role="menu"
        className={classNames(
          "dropdown-menu relative bg-white text-font shadow-xl w-full max-h-[300px] overflow-auto focus:outline-0",
          loading && "min-h-20",
          className
        )}
        aria-orientation="vertical"
        aria-labelledby="menu-button"
        tabIndex={-1}
        onScroll={handleScroll}
      >
        {(search || resetAction) && (
          <div className="sticky -top-2 flex items-center gap-2.5 bg-white px-4 pt-6 pb-2 z-10">
            {search && (
              <div
                className={classNames(
                  "w-full flex-1 mx-auto",
                  isScrolled && "shadow"
                )}
              >
                <Input
                  type="text"
                  placeholder={searchPlaceholder ?? "Search"}
                  onChange={handleSearch}
                  value={searchValue}
                  wrapperClassName="rounded-full"
                  icon={<FiSearch size={6} />}
                  autoFocus
                  action={
                    searchValue && (
                      <button
                        type="button"
                        className="flex items-center justify-center text-inherit"
                        onClick={() => {
                          setSearchValue("");
                        }}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )
                  }
                />
              </div>
            )}
            {resetAction && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      title="reset"
                      className="w-max h-input aspect-square border border-light hover:bg-slate-50 rounded-md flex items-center justify-center p-1.5"
                      onClick={() => {
                        resetAction?.();
                        setSearchValue("");
                        handleScrollTop();
                      }}
                    >
                      <RiResetLeftFill />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>Reset</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        )}

        <div className={classNames("w-full", search && "px-4")}>
          {isDataValid
            ? finalData?.slice(0, showCount).map((menu, index) => {
                return (
                  <Fragment key={index}>
                    <Suspense fallback={<Loader />}>
                      <MenuItem
                        menu={menu}
                        className={menuClassName}
                        checkbox={checkbox}
                        iconClassName={iconClassName}
                        onSelect={handleSelect}
                        value={value}
                        highlightSelected={highlightSelected}
                        optionKeys={optionKeys}
                        valueUniqueParam={valueUniqueParam}
                        getLabel={getLabel}
                      />
                    </Suspense>
                  </Fragment>
                );
              })
            : !loading && (
                <div className="flex items-center gap-1 text-red-600 my-3 px-3">
                  <RiErrorWarningLine />
                  <span className="text-sm">No Options Available</span>
                </div>
              )}

          {(loading || infiniteLoading) && (
            <div className="flex items-center justify-center my-2">
              <Loader
                center
                big={loading && !infiniteLoading}
                box={loading && !infiniteLoading}
              />
            </div>
          )}

          {pagination?.hasNextPage && (
            <div ref={loaderRef} className="h-2 w-full" />
          )}
        </div>
      </div>
    );
  }
);

export type { DropdownListRefType };
export default DropdownList;
