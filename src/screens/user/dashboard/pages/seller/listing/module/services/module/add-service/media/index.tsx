"use client";

import { Badge } from "@/components/ui/badge";
import Input<PERSON>abel from "@/components/ui/input/components/label";
import {
  Dropzone,
  DropzoneInputTargetType,
  InlinePlaceholder,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { InputTargetType } from "@/components/ui/input/types";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import { serviceDetails } from "../../../@redux/selectors";

const Media = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(serviceDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values[e?.target?.name])
          ? Array.from(values[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setServiceDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Main Service Images</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload the primary service image (1000x1000px)
          </span>
        </div>
        <Dropzone
          containerClassName="flex-1"
          onChange={(e) => handleChange(e)}
          onFileDelete={(file) => console.log({ file })}
          name="main_service_images"
          value={values?.main_service_images}
          preview
          filePreviewClassName="grid grid-cols-2 [&>*]:min-w-[100%] [&>*]:min-h-[200px] px-3"
          inputProps={{ multiple: true }}
        >
          {Array.isArray(values?.main_service_images) &&
          values?.main_service_images?.length > 0 ? (
            <InlinePlaceholder />
          ) : null}
        </Dropzone>
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Catalogs Photos</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload the catalog photos (1000x1000px)
          </span>
        </div>
        <Dropzone
          containerClassName="flex-1"
          onChange={(e) => handleChange(e)}
          onFileDelete={(file) => console.log({ file })}
          name="catalogs_images"
          value={values?.catalogs_images}
          preview
          filePreviewClassName="grid grid-cols-2 [&>*]:min-w-[100%] [&>*]:min-h-[200px] px-3"
          inputProps={{ multiple: true }}
        >
          {Array.isArray(values?.catalogs_images) &&
          values?.catalogs_images?.length > 0 ? (
            <InlinePlaceholder className="!h-32 bg-stone-100 rounded-lg shadow-md w-[96%] mx-auto" />
          ) : null}
        </Dropzone>
      </div>

      <div className="w-full flex flex-col md:flex-row items-center gap-6">
        <div className="w-full flex items-center justify-start gap-1.5 md:w-[30%]">
          <InputLabel>Service Video URL</InputLabel>
        </div>
        <Input
          type="text"
          placeholder="Service Video Url"
          name="service_video_url"
          value={values?.service_video_url}
          onChange={handleChange}
          containerClassName="flex-1 w-full"
        />
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Portfolio / Work Samples</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload the primary service image (1000x1000px)
          </span>
        </div>
        <Dropzone
          containerClassName="flex-1"
          onChange={(e) => handleChange(e)}
          onFileDelete={(file) => console.log({ file })}
          name="work_sample_images"
          value={values?.work_sample_images}
          preview
          filePreviewClassName="grid grid-cols-2 [&>*]:min-w-[100%] [&>*]:min-h-[200px] px-3"
          inputProps={{ multiple: true }}
        >
          {values?.work_sample_images && <InlinePlaceholder />}
        </Dropzone>
      </div>

      <div className="w-full flex flex-col md:flex-row items-start gap-6">
        <div className="w-full flex flex-col items-start justify-start gap-2 md:w-[30%]">
          <InputLabel>Terms & Conditions</InputLabel>
          <span className="text-stone-500 text-[0.7rem]">
            Brief Item summary
          </span>
        </div>
        <Textarea
          name="terms_conditions"
          label="Brief Terms & Conditions"
          onChange={handleChange}
          value={values?.terms_conditions}
          placeholder="Service terms, privacy policy etc."
          className="min-h-20"
          containerClassName="flex-1 w-full"
          maxLength={150}
          showCharCount
        />
      </div>
    </>
  );
};

export default Media;
