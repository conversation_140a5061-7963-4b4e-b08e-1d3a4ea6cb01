import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { safeCall } from "./lib/utils/api";
import { logoutAction, userAuthAction } from "./screens/user/auth/@actions";

// TODO
// i18n middleware pending


export async function middleware(request: NextRequest) {
  const jwt = request.cookies.get("user_access_token")?.value;
  let isAuthenticated = false;

  if (jwt) {
    try {
      await safeCall(() => userAuthAction(jwt as string))
      isAuthenticated = true;
    } catch (err) {
      console.error("JWT verification failed:", err);
      await logoutAction();
      return NextResponse.redirect(new URL("/signin", request.url));

    }
  }

  const isProtectedRoute = request.nextUrl.pathname.startsWith("/dashboard");
  const authRoutes = ["/signin", "/signup"];

  if (isProtectedRoute && !isAuthenticated) {
    return NextResponse.redirect(new URL("/signin", request.url));
  } else if (isAuthenticated && authRoutes?.includes(request.nextUrl.pathname)) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/dashboard/:path*", "/dashboard", "/signin", "/signup"],
};
