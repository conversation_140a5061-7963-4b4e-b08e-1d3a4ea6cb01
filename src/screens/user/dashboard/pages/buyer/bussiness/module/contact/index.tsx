'use client';

import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import { ChangeEvent, useEffect } from "react";
import { businessDetails } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/slice";
import { useDispatch, useSelector } from "react-redux";
import { getBusinessDetails } from "../../@redux/thunk";
import { RootDispatch } from "@/redux/store";

interface ContactProps {
  onValidationChange?: (isValid: boolean) => void;
  isSubmitted?: boolean;
}

const Contact = ({ onValidationChange }: ContactProps) => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(businessDetails);

  // Fetch business details when component mounts
  useEffect(() => {
    dispatch(getBusinessDetails())
      .unwrap()
      .then((response) => {
        if (response?.data) {
          const formData = {
            buyer_full_name: response.data.buyer_full_name,
            designation: response.data.designation,
            business_email: response.data.business_email,
            business_phone_number: response.data.business_phone_number,
            phone_number_country_code: response.data.phone_number_country_code,
          };
          dispatch(Actions.setFormValues(formData));
        }
      })
      .catch((error) => {
        console.error('Error fetching business details:', error);
      });
  }, [dispatch]);

  // For inputs that send native events
 const handleChange = (
  e:
    | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    | InputTargetType
    | MultiSelectTarget
) => {
  try {
    const payload = {
      [e?.target?.name]: e?.target?.value,
    };
    dispatch(Actions.setFormValues(payload));
  } catch (error) {
    console.error(error);
  }
};

const handleDesignationChange = (e: InputTargetType) => {
  const { name, value } = e.target;
  dispatch(Actions.setFormValues({ [name]: value }));
};


  useEffect(() => {
    const isValid = Boolean(
      values?.buyer_full_name &&
      values?.designation &&
      values?.business_email &&
      values?.business_phone_number
    );
    onValidationChange?.(isValid);
  }, [values, onValidationChange]);

  const designationOptions = [
    { label: "Procurement Manager", value: "Procurement Manager" },
    { label: "CEO", value: "CEO" },
    { label: "Director", value: "Director" },
    { label: "Manager", value: "Manager" },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
        {/* Buyer's Full Name */}
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            {`Buyer's Full Name`}
          </label>
          <Input
            type="text"
            name="buyer_full_name"
            onChange={handleChange}
            value={values?.buyer_full_name || ''}
            placeholder="Enter your full name"
            className="p-2 border rounded-md"
          />
          <span className="text-gray-500 text-xs mt-1">(Max 50 characters)</span>
        </div>

        {/* Designation */}
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Designation
          </label>
          <Select
            name="designation"
            onChange={handleDesignationChange}  // <-- use separate handler
            value={values?.designation || ''}
            placeholder="Select Designation"
            options={designationOptions}
            className="p-2 border rounded-md"
          />
        </div>

        {/* Business Email */}
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Business Email
          </label>
          <Input
            type="email"
            name="business_email"
            onChange={handleChange}
            value={values?.business_email || ''}
            placeholder="<EMAIL>"
            className="p-2 border rounded-md"
          />
        </div>

        {/* Business Phone Number */}
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Business Phone Number
          </label>
          <Input
            type="text"
            name="business_phone_number"
            onChange={handleChange}
            value={values?.business_phone_number || ''}
            placeholder="Enter Phone"
            className="p-2 border rounded-md"
            countrySelector={{
              defaultValue: values?.phone_number_country_code || "+91",
              name: "phone_number_country_code",
              onChange: handleChange,
              value: values?.phone_number_country_code || "+91",
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default Contact;
