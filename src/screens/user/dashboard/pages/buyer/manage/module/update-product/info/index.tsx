'use client';

import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import { RootDispatch } from "@/redux/store";
import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateRFQDetailsSelector } from "@/screens/user/dashboard/pages/buyer/manage/@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/manage/@redux/slice";
import { TextEditor } from "@/components/ui/input/texteditor";

const PRODUCT_DETAILS_FIELDS = [
  "targetCountries",
  "quantity",
  "volume",
  "preferredProviderType",
  "requirementUrgency",
  "detailedDescription",
  "min",
  "max",
];

const ProductInformation = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(updateRFQDetailsSelector);
  const [ready, setReady] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setReady(true), 100); // Delay of 50ms

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
      | { target: { name: string; value: any } }
  ) => {
    try {
      const payload: any = {};

      // Handle Dropzone (file upload) fields if any
      if (isDropzoneInputTargetType(e)) {
      } else {
        const { name, value } = e.target;

        // Handle all productDetails fields
        if (PRODUCT_DETAILS_FIELDS.includes(name)) {
          if (name === "min" || name === "max") {
            payload.productDetails = {
              ...values?.productDetails,
              budgetRange: {
                ...values?.productDetails?.budgetRange,
                [name]: value,
                currency: "USD",
              },
            };
          }
          else if (name === "detailedDescription") {
            payload.productDetails = {
              ...values?.productDetails,
              detailedDescription: value,
            };
          }
          else {
            payload.productDetails = {
              ...values?.productDetails,
              [name]: value,
            };
          }
        }
        else {
          payload[name] = value;
        }
      }

      dispatch(Actions.setRFQDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  // MultiSelect needs a different handler
  const handleMultiSelect = (name: string, value: any) => {
    dispatch(
      Actions.setRFQDetails({
        productDetails: {
          ...values?.productDetails,
          [name]: value,
        },
      })
    );
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6 [&>*]:w-full md:[&>*]:w-auto">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[25%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Product Title</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Required, Text (Max 100 chars)
          </span>
        </div>
        <Input
          type="text"
          name="title"
          label="Product Title"
          onChange={handleChange}
          value={values?.title ?? ""}
          placeholder="Product Name"
          containerClassName="flex-1"
          labelClassName="hidden md:block"
          maxLength={100}
          showCharCount
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <MultiSelect
          onValueChange={(e: any) =>
            handleMultiSelect("targetCountries", e.target.value)
          }
          value={values?.productDetails?.targetCountries ?? []}
          name="targetCountries"
          label="Where should it be delivered?"
          placeholder="Select countries"
          variant="inverted"
          options={[
            { label: "India", value: "india" },
            { label: "New Zealand", value: "new_zealand" },
            { label: "America", value: "america" },
            { label: "Austria", value: "austria" },
            { label: "Canada", value: "canada" },
            { label: "Australia", value: "australia" },
          ]}
        />

        <div className="flex items-center gap-1">
          <Input
            type="number"
            label="How many units do you need?"
            placeholder="Enter unit"
            name="quantity"
            value={values?.productDetails?.quantity ?? ""}
            onChange={handleChange}
            containerClassName="flex-1"
            min="0"
          />
          <Select
            name="volume"
            label="&nbsp;"
            onChange={handleChange}
            value={values?.productDetails?.volume ?? ""}
            placeholder="unit"
            options={[
              { label: "kg", value: "kg" },
              { label: "Unit", value: "unit" },
              { label: "Gram", value: "g" },
              { label: "Pieces", value: "piece" },
              { label: "Boxes", value: "box" },
              { label: "Liters", value: "litre" },
              { label: "Custom", value: "custom" },
            ]}
          />
        </div>

        <Select
          name="preferredProviderType"
          label="Select service provider"
          onChange={handleChange}
          value={values?.productDetails?.preferredProviderType ?? ""}
          placeholder="Select provider types"
          options={[
            { label: "Manufacturer", value: "manufacturer" },
            { label: "Wholesaler", value: "wholesaler" },
            { label: "Distributor", value: "distributor" },
            { label: "Any", value: "any" },
          ]}
        />

        <Select
          name="requirementUrgency"
          label="Select Urgency"
          onChange={handleChange}
          value={values?.productDetails?.requirementUrgency ?? ""}
          placeholder="Select Urgency"
          options={[
            { label: "immediately", value: "immediately" },
            { label: "1 Day", value: "1D" },
            { label: "1 Week", value: "1W" },
            { label: "1 Month", value: "1M" },
            { label: "3 Months", value: "3M" },
            { label: "6 Months", value: "6M" },
            { label: "1 Year", value: "1y" },
          ]}
        />
      </div>

      <div className="flex flex-col">
        <label htmlFor="minBudget" className="mb-2 text-sm font-medium text-gray-700">
          Minimum Budget (USD)
        </label>
        <Input
          id="minBudget"
          name="min"
          type="number"
          placeholder="Enter minimum budget"
          value={values?.productDetails?.budgetRange?.min ?? ""}
          onChange={handleChange}
          min="0"
        />
      </div>
      <div className="flex flex-col">
        <label htmlFor="maxBudget" className="mb-2 text-sm font-medium text-gray-700">
          Maximum Budget (USD)
        </label>
        <Input
          id="maxBudget"
          name="max"
          type="number"
          placeholder="Enter maximum budget"
          value={values?.productDetails?.budgetRange?.max ?? ""}
          onChange={handleChange}
          min="0"
        />
      </div>
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Any specific features?</InputLabel>
          </div>
        </div>
        {ready && (

          <TextEditor
            name="detailedDescription"
            value={values?.productDetails?.detailedDescription ?? ""}
            onChange={handleChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            key={'detailedDescription'}
            label="Brief product warranty"
          />
        )}
      </div>
    </>
  );
};

export default ProductInformation;
