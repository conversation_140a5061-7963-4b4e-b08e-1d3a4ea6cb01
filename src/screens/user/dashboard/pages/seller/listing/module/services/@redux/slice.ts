import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { merge } from "lodash";
import { thunks } from "./thunk";
import { ServiceUpdateStep1Type } from "../module/add-service/types";

interface APTStateType {
  servicesList: APIResponseType;
  serviceDetails: APIResponseType;
}

const APIState: APTStateType = {
  servicesList: {
    data: null,
    loading: false,
    meta: null,
  },
  serviceDetails: {
    data: null,
    loading: false,
    meta: null,
  },
};

interface stateType extends APTStateType {
  searchValue: string;
  currentPage: string;
}

const initialState = {
  searchValue: "",
  currentPage: "1",
  ...APIState,
} satisfies stateType as stateType;

export const slice = createSlice({
  name: "user_service",
  initialState,
  reducers: {
    setSearchValue: (state, action: PayloadAction<string>) => {
      state.searchValue = action.payload;
    },
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    setServiceDetails: (
      state,
      action: PayloadAction<ServiceUpdateStep1Type | null>
    ) => {
      if (action?.payload) {
        state.serviceDetails.data = merge(
          {},
          state.serviceDetails.data,
          action.payload
        );
      } else {
        state.serviceDetails = { ...initialState.serviceDetails };
      }
    },
  },
  extraReducers: (builder) => {
    Object.entries(thunks).forEach(([_, thunk]) => {
      createThunkCase(builder, thunk, APIState);
    });
  },
});

export const Actions = slice.actions;
export default slice.reducer;
