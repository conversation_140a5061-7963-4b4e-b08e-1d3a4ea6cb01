import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import classNames from "classnames";
import { CheckIcon } from "lucide-react";
import { FC, Fragment } from "react";
import { FaRegHeart } from "react-icons/fa6";

interface ProductSelectionsType {
  className?: string;
}

const ProductSelections: FC<ProductSelectionsType> = ({ className }) => {
  return (
    <div
      className={classNames(
        "relative w-full py-6 flex flex-col gap-5 [&>*]:w-full]",
        className
      )}
    >
      <div className="flex flex-row justify-between gap-3 order-1">
        <div className="flex flex-wrap items-center gap-2.5 text-base lg:text-lg font-bold">
          <span
            aria-label="offer-price"
            className="text-font font-extrabold text-xl"
          >
            $381.99
          </span>
          <span
            aria-label="mrp"
            className="line-through text-stone-400 font-extrabold text-xl"
          >
            $381.99
          </span>
        </div>
        <button className="bg-stone-50 hover:bg-main hover:text-white transition-all h-10 w-10 rounded-full flex items-center justify-center shadow-md">
          <FaRegHeart size={20} className="mt-0.5" />
        </button>
      </div>

      <div className="flex flex-col gap-2.5 order-3 md:order-2">
        <h5 className="font-semibold text0-sm">Key Features</h5>
        <div className="flex items-center flex-wrap gap-2.5 mb-1">
          {[...Array(3)].fill("Check Features").map((item, index) => {
            return (
              <div key={index} className="flex items-center gap-2">
                <CheckIcon className="w-3 h-3" />
                <span className="text-xs">{item}</span>
              </div>
            );
          })}
        </div>
        <div className="flex items-center flex-wrap gap-2.5">
          {[...Array(10)].fill("Features").map((item, index) => {
            return (
              <Badge
                key={index}
                variant="outline"
                className="text-xs !font-normal !rounded-full"
              >
                {item}
              </Badge>
            );
          })}
        </div>
      </div>

      <div className="flex flex-col gap-2.5 order-2 md:order-3">
        <h5 className="font-semibold text0-sm hidden md:block">Colors</h5>
        <div className="flex items-center flex-wrap gap-2.5 mb-1">
          {[
            "red",
            "green",
            "blue",
            "yellow",
            "black",
            "white",
            "gray",
            "pink",
          ].map((item, index) => {
            const active = index === 0;
            return (
              <Fragment key={index}>
                <TooltipProvider delayDuration={1}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex flex-col items-center gap-2">
                        <button
                          className={classNames(
                            "h-max w-max p-[1px] border rounded-sm shadow-sm hover:shadow-box hover:scale-125 overflow-hidden transition-all",
                            {
                              "border-stone-500": active,
                              "border-stone-100": !active,
                            }
                          )}
                        >
                          <span
                            className="h-10 w-10 block"
                            style={{ backgroundColor: item }}
                          />
                        </button>
                        <span className="capitalize text-xs block md:hidden">
                          {item}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>{item}</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Fragment>
            );
          })}
        </div>
      </div>

      <div className="flex items-center justify-start flex-col md:flex-row gap-2.5 order-4">
        <h5 className="font-semibold text0-sm text-start w-full md:w-max">
          Capacity
        </h5>
        <div className="flex items-center justify-start flex-wrap gap-2.5 mb-1 w-full md:w-max">
          {[...Array(2)].fill("Capacity").map((item, index) => {
            return (
              <Badge key={index} variant="destructive" className="text-xs">
                {item + " " + index}
              </Badge>
            );
          })}
        </div>
      </div>

      <div className="flex items-center gap-2.5 order-5">
        <h5 className="font-semibold text0-sm">Min Order:</h5>
        <span className="text-sm">500 Pieces</span>
      </div>

      <div className="hidden md:flex flex-col items-center gap-2.5 [&>*]:w-full order-6">
        <h5 className="font-semibold text0-sm">Product Description</h5>
        <p className="text-sm">
          Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
          nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat,
          sed diam voluptua. At vero eos et accusam et justo duo dolores et ea
          rebum. Stet clita read more..
        </p>
      </div>

      <div className="flex flex-col sm:flex-row items-center gap-2.5 [&>*]:flex-1 [&>*]:w-full [&>*]:sm:w-max order-7">
        {/* TODO */}
        {/* Open modal form */}
        <Button variant="main-revert" className="!rounded-full">
          Get Quote
        </Button>

        <Checkbox
          label="Add to Compare"
          containerClassName="cursor-pointer h-auto px-4 py-2.5 flex-1 rounded-full border border-stone-200 hover:border-main flex items-center justify-center transition-all"
        />
      </div>
    </div>
  );
};

export default ProductSelections;
