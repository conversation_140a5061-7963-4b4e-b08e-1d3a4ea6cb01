"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import useEmblaCarousel from "embla-carousel-react";
import { useEffect, useState } from "react";

interface SliderProps {
  images: string[];
}

const Slider: React.FC<SliderProps> = ({ images }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [embRef, embApi] = useEmblaCarousel({
    loop: false,
    slidesToScroll: 1,
    skipSnaps: false,
  });

  const [_, previewApi] = useEmblaCarousel({
    loop: false,
    slidesToScroll: 1,
    skipSnaps: false,
    dragFree: true,
  });

  useEffect(() => {
    if (embApi && previewApi) {
      embApi.on("select", () => {
        const selectedIndex = embApi.selectedScrollSnap();
        setActiveIndex(selectedIndex);
        previewApi?.scrollTo(selectedIndex);
      });
    }
  }, [embApi, previewApi]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      const newIndex = (activeIndex + 1) % images.length;
      setActiveIndex(newIndex);
      previewApi?.scrollTo(newIndex);
      embApi?.scrollTo(newIndex);
    }, 3000);

    return () => clearInterval(intervalId);
  }, [activeIndex, images.length, embApi, previewApi]);

  const handlePreviewClick = (index: number) => {
    setActiveIndex(index);
    embApi?.scrollTo(index);
  };

  return (
    <div className="slider-container flex flex-col gap-6">
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full static"
      >
        <CarouselContent className="-ml-6">
          {images.map((img, index) => (
            <CarouselItem
              key={index}
              className="pl-6 basis-auto max-w-max my-4"
            >
              <div
                key={index}
                onClick={() => handlePreviewClick(index)}
                className={`preview-slide cursor-pointer overflow-hidden rounded-lg ${
                  index === activeIndex ? "" : "grayscale"
                }`}
                style={{
                  width: "150px",
                  height: "80px",
                }}
              >
                <img
                  src={img}
                  alt={`Preview ${index + 1}`}
                  className="object-cover w-full h-full"
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="-left-6 shadow-xl bg-white" />
        <CarouselNext className="-right-6 shadow-xl bg-white" />
      </Carousel>

      {/* Main active image section (bottom) */}
      <div className="active-slide-container flex justify-center w-full relative">
        <div ref={embRef} className="main-carousel w-full overflow-hidden">
          <div className="flex">
            {images.map((image, index) => (
              <div
                key={index}
                className={`carousel-item flex justify-center`}
                style={{
                  flex: "0 0 100%",
                  maxWidth: "100%",
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <img
                  src={image}
                  alt={`Main ${index + 1}`}
                  className="object-cover w-full h-128 rounded-sm"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Slider;
