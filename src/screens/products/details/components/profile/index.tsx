"use client";

import BreadcrumbRender from "@/components/ui/breadcrumb/render";
import classNames from "classnames";
import { FC } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface ProductProfileType {
  className?: string;
}

const ProductProfile: FC<ProductProfileType> = ({ className }) => {
const router = useRouter();

  return (
    <div className={classNames("relative w-full", className)}>
      <BreadcrumbRender
        className="mb-4 font-medium hidden md:block"
        data={[
          {
            title: "Home",
            pathname: "/",
          },
          {
            title: "Category Name",
            pathname: "/",
          },
          {
            title: "Mug",
            pathname: "",
          },
        ]}
      />

      <h2 className="text-xl md:text-2xl font-extrabold my-4">
        Stainless Steel Vacuum Insulated Car tumbler Travel Mugs 500ml cup
      </h2>

      <div className="flex flex-col md:flex-row items-center gap-4 [&>*]:w-full [&>*]:md:w-max flex-wrap justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-3">
            <img
              src={"/assets/pages/rating-star.svg"}
              alt="rating star"
              className="aspect-auto w-3.5 md:w-6 h-3.5 md:h-6 object-contain"
            />

            <span className="text-base md:text-sm">4.5/5</span>
          </div>
          <Link
            href="#"
            className="text-primary text-base md:text-sm underline border-l border-slate-200 pl-3 hover:drop-shadow"
          >
            150 Reviews
          </Link>
        </div>

        <div className="hidden md:flex items-center gap-2">
          <img
            src="https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg"
            alt="supplier"
            className="aspect-square w-8 h-8 object-cover rounded-md"
          />

          <button
            onClick={() => router.push("/companies/details")}
            className="underline text-sm"
          >
            Lorem Ipsum Lorem Ipsum Lorem Ipsum
          </button>
        </div>

        <div className="flex items-center gap-1.5 mt-1">
          <img
            src={
              "https://upload.wikimedia.org/wikipedia/en/thumb/4/41/Flag_of_India.svg/1200px-Flag_of_India.svg.png"
            }
            alt="flag"
            className="aspect-auto max-w-5 w-5 h-auto object-contain"
          />
          <div className="flex items-center flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700">
            <span className="text-base md:">{"India, Mumbai"}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductProfile;
