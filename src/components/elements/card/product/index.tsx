import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import classNames from "classnames";
import { FC } from "react";
import { FaRegHeart } from "react-icons/fa";

interface ProductCardType {
  className?: string;
  compare?: boolean;
}

const ProductCard: FC<ProductCardType> = ({ className, compare }) => {
  return (
    <div
      role="button"
      className={classNames(
        "bg-white rounded-2xl shadow-md hover:shadow-lg h-full flex flex-col overflow-hidden",
        className
      )}
    >
      <div className="relative overflow-hidden w-full h-48 select-none">
        <img
          src="https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg"
          alt="card-image"
          className="w-full h-full object-cover"
        />
        <button className="absolute right-4 top-4 bg-white hover:bg-main hover:text-white transition-all h-8 w-8 rounded-full flex items-center justify-center">
          <FaRegHeart size={16} className="mt-0.5" />
        </button>
      </div>
      <div className="flex flex-col gap-4 [&>*]:w-full p-4">
        <h5 className="text-base font-bold leading-5">
          Wholesale Borosilicate Heat Resistant Double Wall Glass
        </h5>

        <div className="flex items-center gap-2.5">
          <img
            src={"/assets/pages/rating-star.svg"}
            alt="flag"
            className="aspect-auto w-5 h-5 object-contain"
          />
          <div className="flex item flex-wrap gap-2.5 [&>*]:text-xs [&>*]:font-semibold [&>*]:text-font [&>*]:leading-4">
            <span className="border-r border-slate-300 pr-2.5">4.5/5</span>
            <span>150 Reviews</span>
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-4 text-base lg:text-lg font-bold">
          <span aria-label="offer-price" className="text-font">
            $381.99
          </span>
          <span aria-label="mrp" className="line-through text-stone-400">
            $381.99
          </span>
        </div>

        <div className="flex flex-col items-start gap-3 text-xs">
          <div className="flex items-center gap-1 text-xs">
            <span className="font-semibold">Min Order:</span>
            <span>500 Pieces</span>
          </div>
        </div>

        <div className="mt-2 flex flex-row gap-2.5 [&>*]:flex-1">
          <Button variant="main-revert">Get Quote</Button>
          {compare && (
            <Checkbox label="Compare" containerClassName="!gap-1.5" />
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
