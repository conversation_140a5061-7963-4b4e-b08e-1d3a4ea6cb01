'use client';

import { Select } from "@/components/ui/input/select";
import React, { useState } from 'react';
import { InputTargetType } from "@/components/ui/input/select";

interface UrgencyProps {
  onUrgencyChange?: (details: UrgencyDetails) => void;
  initialDetails?: UrgencyDetails;
}

interface UrgencyDetails {
  urgency: string;
}

const Urgency: React.FC<UrgencyProps> = ({ 
  onUrgencyChange, 
  initialDetails = {
    urgency: ""
  }
}) => {
  const [urgencyDetails, setUrgencyDetails] = useState<UrgencyDetails>(initialDetails);
  
  const handleUrgencyChange = (e: InputTargetType) => {
    const updatedDetails = {
      ...urgencyDetails,
      urgency: e.target.value
    };
    setUrgencyDetails(updatedDetails);
    if (onUrgencyChange) {
      onUrgencyChange(updatedDetails);
    }
  };
  
  const urgencyOptions = [
    { label: "immediately", value: "immediately" },
    { label: "1 Day", value: "1D" },
    { label: "1 Week", value: "1W" },
    { label: "1 Month", value: "1M" },
    { label: "3 Months", value: "3M" },
    { label: "6 Months", value: "6M" },
    { label: "1 Year", value: "1y" },
  ];

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">When do you need it?</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <Select
            placeholder="Select Urgency"
            options={urgencyOptions}
            value={urgencyDetails.urgency}
            onChange={handleUrgencyChange}
          />
        </div>
      </div>
    </div>
  );
};

export default Urgency;