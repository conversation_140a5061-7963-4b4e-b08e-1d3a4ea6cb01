"use client";

import { useCallback } from "react";

interface OpenUrlOptions {
  target?: "_blank" | "_self" | "_parent" | "_top";
  rel?: string;
  referrerPolicy?: ReferrerPolicy;

  delay?: number;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

const useOpenExternalUrl = () => {
  const openUrl = useCallback((url: string, options: OpenUrlOptions = {}) => {
    try {
      if (!url) throw new Error("URL is required");

      const {
        target = "_blank",
        rel = "noopener noreferrer",
        referrerPolicy = "no-referrer",

        delay = 0,
        onSuccess,
      } = options;

      setTimeout(() => {
        try {
          window.open(
            url,
            target,
            `rel=${rel}, referrerpolicy=${referrerPolicy}`
          );

          if (onSuccess) {
            onSuccess();
          }
        } catch (error) {
          console.error(error);
        }
      }, delay);
    } catch (error) {
      options.onError?.(error as Error);
      console.error(error);
    }
  }, []);

  return openUrl;
};

export default useOpenExternalUrl;
