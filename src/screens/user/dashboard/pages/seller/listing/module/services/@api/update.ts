import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";
import privateApiClient from "@/services/axios/private-api-client";

const formDataConfig = {
  headers: {
    "Content-Type": "multipart/form-data",
  },
};

export const getServices = async ({ searchTerm, page, limit }: any = {}) => {
  const endpoint = apiRoutes().user.seller.service.fetchAll;
  return await privateApiClient.get(endpoint, {
    params: { searchTerm, page, limit },
  });
};

export const getServiceSuggestions = async ({
  searchTerm,
}: {
  searchTerm: string;
}) => {
  return await privateApiClient.get(
    apiRoutes().user.seller.service.suggestions,
    {
      params: { searchTerm },
    }
  );
};

export const getServicePreValues = async ({
  suggestionId,
}: {
  suggestionId: string;
}) => {
  return await privateApiClient.get(
    `${apiRoutes().user.seller.service.preValues}/${suggestionId}`
  );
};

export const getService = async (service_id?: string) => {
  return await privateApiClient.get(
    `${apiRoutes().user.seller.service.fetch}/${service_id}`
  );
};

export const updateServiceDetails = async (
  payload: FormData,
  service_id?: string
) => {
  return await formDataClient.post(
    apiRoutes().user.seller.service.addUpdate,
    payload,
    {
      ...formDataConfig,
      params: { serviceId: service_id },
    }
  );
};

export const deleteService = async (service_id?: string) => {
  return await privateApiClient.delete(
    `${apiRoutes().user.seller.service.delete}/${service_id}`
  );
};
