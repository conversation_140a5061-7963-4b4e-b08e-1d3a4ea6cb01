"use client";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useState } from "react";

const TabsData = [
  {
    key: "kew",
    title: "New",
  },
  {
    key: "open",
    title: "Open",
  },
  {
    key: "won",
    title: "Won",
  },
  {
    key: "lost",
    title: "Lost",
  },
  {
    key: "preference",
    title: "Preference",
  },
];

const PreferenceTypeTabs = () => {
  const initialActive = TabsData?.[0]?.key;
  const [active, setActive] = useState<string>(initialActive);

  return (
    <Tabs
      defaultValue={initialActive}
      value={active}
      className="overflow-auto no-scrollbar py-1.5 border-b border-stone-300 mb-5"
    >
      <TabsList className="flex-nowrap">
        {TabsData?.map((tab, index) => {
          return (
            <TabsTrigger
              key={index}
              value={tab?.key}
              className="flex-1 uppercase before:data-[state=active]:-bottom-2.5"
              onClick={() => setActive(tab?.key)}
            >
              {tab?.title}
            </TabsTrigger>
          );
        })}
      </TabsList>
    </Tabs>
  );
};

export default PreferenceTypeTabs;
