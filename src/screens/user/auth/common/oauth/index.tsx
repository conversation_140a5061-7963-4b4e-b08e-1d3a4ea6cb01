"use client";

import useAuth from "@/screens/user/auth/@context/useAuth";
import React, { FC, ReactNode } from "react";

const Oauth = ({ title }: { title?: string }) => {
  const {
    googleAuth,
    facebookAuth,
    linkedinAuth,
    // loading,
    // response,
  } = useAuth();

  const handleGoogle = async () => {
    try {
      await googleAuth();
    } catch (error) {
      console.error(error);
    }
  };

  const handleFacebook = async () => {
    try {
      await facebookAuth();
    } catch (error) {
      console.error(error);
    }
  };

  const handleLinkedin = async () => {
    try {
      await linkedinAuth();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex items-center justify-center gap-4 text-center">
      {title && <p className="text-sm">{title}</p>}
      <div className="flex items-center gap-3">
        <ButtonRender onClick={() => handleGoogle()}>
          <img
            src={"/assets/pages/auth/google.svg"}
            alt="google"
            className="object-contain w-8 h-8"
          />
        </ButtonRender>
        <ButtonRender onClick={() => handleFacebook()}>
          <img
            src={"/assets/pages/auth/facebook.svg"}
            alt="facebook"
            className="object-contain w-8 h-8"
          />
        </ButtonRender>
        <ButtonRender onClick={() => handleLinkedin()}>
          <img
            src={"/assets/pages/auth/linkedin.svg"}
            alt="linkedin"
            className="object-contain w-8 h-8"
          />
        </ButtonRender>
      </div>
    </div>
  );
};

interface ButtonRenderType
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
}

const ButtonRender: FC<ButtonRenderType> = ({ children, ...rest }) => (
  <button
    className="flex items-center justify-center shadow hover:shadow-md rounded-sm bg-slate-100 text-sm font-semibold [&>*]:hover:shadow-box"
    {...rest}
  >
    {children}
  </button>
);
export default Oauth;
