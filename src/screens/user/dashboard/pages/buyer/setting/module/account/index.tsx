'use client';

// src/components/account-settings.tsx
import * as React from "react";
import { Switch } from "@/components/ui/input/switch";
import { Button } from "@/components/ui/button";
import { MultiSelect } from "@/components/ui/input/multi-select";
import { X } from "lucide-react";

export interface AccountSettingsProps {
  className?: string;
}

const AccountSettings: React.FC<AccountSettingsProps> = ({ className }) => {
  // State for language options
  const languageOptions = [
    { label: "English", value: "english" },
    { label: "Hindi", value: "hindi" },
    { label: "Spanish", value: "spanish" },
    { label: "French", value: "french" },
    { label: "German", value: "german" },
    { label: "Japanese", value: "japanese" },
    { label: "Chinese", value: "chinese" },
    { label: "Portuguese", value: "portuguese" },
  ];

  // State management for account settings
  const [accountDisabled, setAccountDisabled] = React.useState(false);
  const [allowPasswordLogin, setAllowPasswordLogin] = React.useState(true);
  const [selectedLanguages, setSelectedLanguages] = React.useState<string[]>([
    "english",
    "hindi",
  ]);

  const handleLanguageChange = (event: { target: { value: string[]; name: string } }) => {
    setSelectedLanguages(event.target.value);
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-white rounded-lg shadow-sm mt-8 ">
      {/* Disable Account Section */}
      <div className="border-b pb-6 mb-6">
        <div className="flex items-center gap-4 mb-2">
          <h3 className="font-medium text-gray-800">Disable Account</h3>
          <Switch 
            checked={accountDisabled} 
            onCheckedChange={setAccountDisabled} 
          />
        </div>
        <p className="text-sm text-gray-500">
          Deactivate your profile and remove your product listing and catalog from Aalyana. By disabling your account, you will stop receiving business enquiries from Aalyana.
        </p>
      </div>

      {/* Additional Language Section */}
      <div className="border-b pb-6 mb-6">
        <h3 className="font-medium text-gray-800 mb-4">Additional Language for Communication</h3>
        <div className="mb-2">
          <p className="text-sm text-gray-500 mb-2">Choose language</p>
          <div className="mb-4">
            <MultiSelect
              options={languageOptions}
              onValueChange={handleLanguageChange}
              defaultValue={selectedLanguages}
              placeholder="Select languages"
              className="w-full"
              variant="default"
            />
          </div>
          <Button variant="main-revert" size="sm" className="w-16 rounded-md">
            Save
          </Button>
        </div>
      </div>

      {/* Secure Account Section */}
      <div className="border-b pb-6 mb-6">
        <h3 className="font-medium text-gray-800 mb-2">Secure your Account</h3>
        <p className="text-sm text-gray-500 mb-4">
          This option will allow you to logout your Aalayan account from all the devices where it is logged in Sign Out from all devices
        </p>
        <Button variant="main-revert" className="rounded-md">
          Sign Out from all devices
        </Button>
      </div>

      {/* Allow Login Through Password Section */}
      <div>
        <div className="flex items-center gap-4 mb-2">
          <h3 className="font-medium text-gray-800">Allow login through Password</h3>
          <Switch 
            checked={allowPasswordLogin} 
            onCheckedChange={setAllowPasswordLogin}
          />
        </div>
        <p className="text-sm text-gray-500">
          This option will allow to use your password in order to login into your Aalayana account.
        </p>
      </div>
    </div>
  );
};

export default AccountSettings;