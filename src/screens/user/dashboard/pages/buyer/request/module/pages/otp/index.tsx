'use client';

import {OTPBox} from '@/components/elements/form/otp'
import React from 'react'

const Index = () => {
  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col items-center mb-16 max-w-md w-full mt-0 sm:mt-0 md:mt-0">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-6 text-center">Verification Code</h1>
        <p className="text-sm sm:text-base text-center mb-4 sm:mb-6 px-2 sm:px-4">Enter the verification code sent to your registered device.</p>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <OTPBox
            className="border-gray-300 py-6 px-4 sm:px-6 md:px-8"
          />
          <div className="text-right text-gray-500 text-xs sm:text-sm mt-2">
           {`Didn't receive code?`} <span className="text-blue-600 cursor-pointer">Resend</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Index