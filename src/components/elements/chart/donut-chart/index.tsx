"use client";

import Chart from "@/components/ui/chart";
import { ChartData, ChartOptions } from "chart.js/auto";
import { useMemo } from "react";
import colors from "tailwindcss/colors";

interface MainProps extends React.ComponentPropsWithoutRef<"canvas"> {
  width?: number | "auto";
  height?: number | "auto";
}

const DonutChart = ({
  width = "auto",
  height = "auto",
  className = "",
}: MainProps) => {
  const props = {
    width: width,
    height: height,
    className: className,
  };
  const colorScheme = "default";
  const darkMode = false;

  const chartData = useMemo(() => {
    return [15, 10, 65];
  }, []);

  const chartColors = () => ["#14AA1F", "#9F9694", "#ED4125"];

  const data: ChartData = useMemo(() => {
    return {
      labels: ["31 - 50 Years old", ">= 50 Years old", "17 - 30 Years old"],
      datasets: [
        {
          data: chartData,
          backgroundColor: colorScheme ? chartColors() : "",
          hoverBackgroundColor: colorScheme ? chartColors() : "",
          borderWidth: 5,
          borderColor: darkMode ? colors.stone[700] : colors.white,
        },
      ],
    };
  }, [chartData, darkMode]);

  const options: ChartOptions = useMemo(() => {
    return {
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
      },
      cutout: "80%",
    };
  }, []);

  return (
    <Chart
      type="doughnut"
      width={props.width}
      height={props.height}
      data={data}
      options={options}
      className={props.className}
    />
  );
};

export default DonutChart;
