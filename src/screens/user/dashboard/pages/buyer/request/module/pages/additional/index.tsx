'use client';

import React, { useState, useEffect } from "react";
import { TextEditor } from "@/components/ui/input/texteditor";
import { Input } from "@/components/ui/input/text";
import CountrySelect from "@/components/ui/input/country-select";

interface AdditionalInfoProps {
  onAdditionalInfoChange?: (details: AdditionalInfoDetails) => void;
  initialDetails?: AdditionalInfoDetails;
}

interface AdditionalInfoDetails {
  contactName: string;
  email: string;
  phoneCode: string;
  phoneNumber: string;
  companyName: string;
  notes: string;
}

const AdditionalInfo: React.FC<AdditionalInfoProps> = ({
  onAdditionalInfoChange,
  initialDetails = {
    contactName: "",
    email: "",
    phoneCode: "+91",
    phoneNumber: "",
    companyName: "",
    notes: ""
  }
}) => {
  const [additionalInfo, setAdditionalInfo] = useState<AdditionalInfoDetails>(initialDetails);

  const handleInputChange = (e: any) => {
    const { name, value } = e.target;
    const updatedDetails = {
      ...additionalInfo,
      [name]: value
    };
    setAdditionalInfo(updatedDetails);
    if (onAdditionalInfoChange) {
      onAdditionalInfoChange(updatedDetails);
    }
  };

  const handleNotesChange = (text: string) => {
    const updatedDetails = {
      ...additionalInfo,
      notes: text
    };
    setAdditionalInfo(updatedDetails);
    if (onAdditionalInfoChange) {
      onAdditionalInfoChange(updatedDetails);
    }
  };

  const handlePhoneCodeChange = (e: any) => {
    const updatedDetails = {
      ...additionalInfo,
      phoneCode: e.target.value
    };
    setAdditionalInfo(updatedDetails);
    if (onAdditionalInfoChange) {
      onAdditionalInfoChange(updatedDetails);
    }
  };

  return (
    <div className="flex flex-col items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 sm:mb-4 text-center">Additional information</h1>
      <div className="w-full max-w-md sm:max-w-xl md:max-w-2xl lg:max-w-3xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 mt-2">
        <div>
          <Input
            label="Contact Name"
            placeholder="Representative's full name"
            name="contactName"
            value={additionalInfo.contactName}
            onChange={handleInputChange}
          />
        </div>
        <div>
          <Input
            label="Email Address"
            placeholder="Business email ID"
            name="email"
            type="email"
            value={additionalInfo.email}
            onChange={handleInputChange}
          />
        </div>
        <div>
          <Input
            label="Phone Number"
            placeholder="Phone number"
            name="phoneNumber"
            value={additionalInfo.phoneNumber}
            onChange={handleInputChange}
            countrySelector={{
              value: additionalInfo.phoneCode,
              onChange: handlePhoneCodeChange
            }}
          />
        </div>
        <div>
          <Input
            label="Company Name"
            placeholder="Company Name"
            name="companyName"
            type="text"
            value={additionalInfo.companyName}
            onChange={handleInputChange}
          />
        </div>
      </div>
      <div className="w-full max-w-md sm:max-w-xl md:max-w-2xl lg:max-w-3xl mt-3 sm:mt-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Additional Notes</label>
        <div className="h-20 sm:h-24">
          <TextEditor 
            // initialValue={additionalInfo.notes}
            // onChange={handleNotesChange}
          />
        </div>
      </div>
    </div>
  );
};

export default AdditionalInfo;