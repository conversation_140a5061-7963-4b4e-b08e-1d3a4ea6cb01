import { ActionReducerMapBuilder, AsyncThunk } from "@reduxjs/toolkit";

export interface APIResponseType {
  data: any;
  loading: boolean;
  meta: any;
  pagination?: {
    limit?: number;
    page?: number;
    totalData?: number;
    totalPages?: number;
  };
}

const extractStateName = (typePrefix: string, stateMap: Record<string, any>): string => {
  const parts = typePrefix.split("/");
  return stateMap[parts[1]] ? parts[1] : parts[0];
};

export const createThunkCase = (
  builder: ActionReducerMapBuilder<any>,
  thunk: AsyncThunk<any, any, any>,
  stateMap: Record<string, any>
) => {
  try {
    return builder
      .addCase(thunk.pending, (state: typeof stateMap) => {
        const stateName = extractStateName(thunk?.typePrefix, state);
        const slice = state[stateName as keyof typeof stateMap] || (state[stateName as keyof typeof stateMap] = {
          data: null,
          loading: false,
          meta: null,
          pagination: null
        });
        if (typeof slice === "object") {
          slice.meta = null;
          slice.loading = true;
        }
      })
      .addCase(thunk.fulfilled, (state, action) => {
        const stateName = extractStateName(thunk?.typePrefix, state);
        const slice = state[stateName as keyof typeof stateMap] || (state[stateName as keyof typeof stateMap] = {
          data: null,
          loading: false,
          meta: null,
          pagination: null
        });
        if (typeof slice === "object") {
          slice.data = action.payload?.data ?? action.payload?.results ?? action.payload ?? null;
          const pagination = action.payload?.meta?.pagination ?? action.payload?.pagination;
          if (pagination) {
            slice.pagination = {
              limit: pagination.limit ?? undefined,
              page: pagination.page ?? undefined,
              totalData: pagination.totalData ?? pagination.total ?? undefined,
              totalPages: pagination.totalPages ?? undefined
            };
          } else {
            slice.pagination = null;
          }
          slice.loading = false;
          slice.meta = action.payload?.meta ?? null;
        }
      })
      .addCase(thunk.rejected, (state, action: any) => {
        const stateName = extractStateName(thunk?.typePrefix, state);
        const slice = state[stateName as keyof typeof stateMap] || (state[stateName as keyof typeof stateMap] = {
          data: null,
          loading: false,
          meta: null,
          pagination: null
        });
        if (typeof slice === "object") {
          slice.meta = typeof action?.payload === "string"
            ? action.payload
            : action.payload?.meta ?? action.error?.message ?? null;
          slice.loading = false;
          slice.data = null;
          slice.pagination = null;
        }
      });
  } catch (error) {
    return null;
  }
};
