"use client";

import { Check, ChevronsUpDown } from "lucide-react";
import * as React from "react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { SelectProps } from "@radix-ui/react-select";
import classNames from "classnames";
import InputLabel from "../components/label";
import { InputWrapper } from "../components/wrapper";
import { InputTargetType } from "../types";
import { countryCodes } from "./country-codes";

export type FinalTextType = {
  title: string;
  value: string;
};

export type DataType = typeof countryCodes[0];

export interface CountrySelectType extends SelectProps {
  icon?: React.ReactNode | string;
  onChange?: (e: InputTargetType) => void;
  containerClassName?: string;
  labelClassName?: string;
  wrapperClassName?: string;
  id?: string;
  label?: string;
  className?: string;
  placeholder?: string;
  dialCode?: boolean;
  comboInput?: boolean;
}

const CountrySelect: React.FC<CountrySelectType> = ({
  id,
  label,
  icon,
  className,
  labelClassName,
  containerClassName,
  wrapperClassName,
  placeholder,
  value,
  defaultValue,
  onChange,
  name = "countrySelect",
  dialCode,
  comboInput,
}) => {
  const [open, setOpen] = React.useState(false);
  const uniqueId = React.useId();

  const handleChange = (val: DataType) => {
    try {
      const finalValue = dialCode ? val?.dialCode : val?.name;
      if (onChange) {
        onChange({
          target: {
            value: finalValue,
            name: name,
          },
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  // const finalText = (country: typeof countryCodes[0]): FinalTextType => {
  //   let title = "";
  //   let value = "";

  //   try {
  //     if (dialCode) {
  //       title = `${country?.dialCode}-${country?.name}`;
  //       value = country?.dialCode;
  //     } else {
  //       title = country?.name;
  //       value = country?.name;
  //     }

  //     return { title, value };
  //   } catch (error) {
  //     console.error(error);
  //     return { title, value };
  //   }
  // };

  const current = countryCodes?.find(
    (country) =>
      country?.dialCode === (value || defaultValue) ||
      country?.name === (value || defaultValue)
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <div
        className={classNames(
          "flex flex-col gap-1.5 items-center [&>*]:w-full",
          containerClassName
        )}
      >
        {label && (
          <InputLabel htmlFor={id ?? uniqueId} className={labelClassName}>
            {label}
          </InputLabel>
        )}
        <PopoverTrigger asChild>
          <InputWrapper
            icon={icon}
            className={classNames(
              "w-full !h-max",
              comboInput &&
                "!p-0 !w-max border-none !shadow-none !rounded-none",
              wrapperClassName
            )}
          >
            <button
              role="combobox"
              aria-expanded={open}
              className={classNames(
                "w-full !shadow-none border-light !ring-0 flex items-center gap-3 justify-between",
                comboInput &&
                  "!h-max !border-none overflow-hidden !rounded-none !shadow-none !p-0",
                className
              )}
            >
              {value ? (
                <div className="flex items-center justify-start gap-1.5">
                  <img
                    src={current?.flag}
                    alt="flag"
                    className="max-w-4 w-4 h-auto object-contain"
                  />
                  <span className="w-full text-sm whitespace-nowrap text-ellipsis overflow-hidden">
                    {dialCode ? current?.dialCode : current?.name}
                  </span>
                </div>
              ) : (
                <span className="text-sm text-stone-500">
                  {placeholder || "Select"}
                </span>
              )}

              <ChevronsUpDown className="opacity-50 w-4 h-4" />
            </button>
          </InputWrapper>
        </PopoverTrigger>
      </div>
      <PopoverContent className="w-[240px] p-0 bg-white" align="start">
        <Command>
          <CommandInput placeholder="Search..." className="h-9" />
          <CommandList>
            <CommandEmpty>No Result found.</CommandEmpty>
            <CommandGroup>
              {countryCodes?.map((option, index) => (
                <CommandItem
                  key={index}
                  value={option.isoCode}
                  onSelect={() => {
                    handleChange(option);
                    setOpen(false);
                  }}
                  className="cursor-pointer py-2 hover:bg-slate-100"
                  title={option?.name}
                >
                  <img
                    src={option?.flag}
                    alt="flag"
                    className="max-w-4 w-4 h-auto object-contain"
                  />
                  <span className="w-full text-sm whitespace-nowrap text-ellipsis overflow-hidden">
                    {option?.name}
                  </span>
                  <Check
                    className={classNames(
                      "ml-auto",
                      current?.isoCode === option.isoCode
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default CountrySelect;
