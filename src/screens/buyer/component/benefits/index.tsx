"use client";

import React from "react";
import Benefit from "./benefit-card";
import FAQ from "../../../products/details/components/faq";
import { useRouter } from "next/navigation";

const Index = () => {
const router = useRouter();

  return (
    <div className="bg-[#FFFBFA] justify-center mb-10 px-4 sm:px-6 md:px-1 mx-auto w-full max-w-full md:max-w-[1500px] lg:max-w-[1500px] xl:max-w-full py-6">
      <Benefit />
      {/* Centered Button */}
      <div className="flex justify-center my-6">
        <button
          type="submit"
          className="w-52 xl:w-64 bg-[#29100C]  text-white py-3 rounded-3xl"
          onClick={() => router.push("/free-listing")}
        >
          Join Now For Free
        </button>
      </div>

      <FAQ />
    </div>
  );
};

export default Index;
