"use client";

import DashboardReviewsCard from "@/components/elements/card/user/dashboard/reviews";
import ReviewListFilter from "@/components/elements/filters/reviews/list";

const ProductsReviews = () => {
  return (
    <div className="grid grid-cols-1 gap-6">
      <ReviewListFilter
        rating={"3"}
        sort={"3"}
        actions={{
          onAddClick: () => {},
          onMediaClick: () => {},
          onRatingsChange: () => {},
          onSortChange: () => {},
        }}
      />

      {[...Array(+10).keys()]?.map((_, index) => {
        return <DashboardReviewsCard key={index} />;
      })}
    </div>
  );
};

export default ProductsReviews;
