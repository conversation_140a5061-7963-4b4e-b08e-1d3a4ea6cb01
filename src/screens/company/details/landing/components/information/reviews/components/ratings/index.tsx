import { Progress } from "@/components/ui/progress";
import { SquareProgress } from "@/components/ui/progress/square";

const Ratings = () => {
  const squareProgressData = [
    { value: 76, count: "3.8", label: "Supplier service" },
    { value: 84, count: "4.2", label: "On-time shipment" },
    { value: 66, count: "3.3", label: "Product quality" },
    { value: 96, count: "4.8", label: "Supplier service" }
  ];

  return (
    <div className="flex flex-wrap items-center gap-6">
      {/* Main Rating Section */}
      <div className="flex-1 flex flex-col items-center gap-3 min-w-24">
        <div className="flex items-center gap-2">
          <h5 className="text-4xl font-normal">4.5</h5>
          <img
            src={"/assets/pages/rating-star.svg"}
            alt="rating star"
            className="aspect-auto w-7 h-7 object-contain"
          />
        </div>
        <span className="text-center text-xs sm:text-sm">25,116 Reviews</span>
      </div>

      {/* Progress Bars Section */}
      <div className="flex-[2] flex flex-col items-start gap-2 [&>*]:w-full">
        {Array.from({ length: 5 })
          .map((_, index) => {
            const starNumber = index + 1;
            // Define progress values and colors for each star rating
            const getProgressValue = (star) => {
              switch(star) {
                case 5: return 85;
                case 4: return 25;
                case 3: return 15;
                case 2: return 10;
                case 1: return 8;
                default: return 10;
              }
            };

            const getProgressColor = (star) => {
              if (star === 1) return "#F7474F"; // Red
              if (star === 2) return "#FA8B18"; // Orange
              return "#347E29"; // Green for 3, 4, 5
            };

            return (
              <div key={index} className="flex items-center gap-5">
                <div className="flex items-center gap-1">
                  <span className="font-semibold min-w-2.5">{starNumber}</span>
                  <img
                    src={"/assets/pages/rating-star.svg"}
                    alt="star"
                    className="aspect-auto w-3.5 h-3.5 object-contain"
                  />
                </div>
                <Progress
                  value={getProgressValue(starNumber)}
                  className="w-full min-w-28"
                  progressBg={getProgressColor(starNumber)}
                />
              </div>
            );
          })
          ?.reverse()}
      </div>

      {/* Square Progress Section */}
      <div className="flex-3 grid items-center flex-wrap grid-cols-2 md:grid-cols-2 gap-3 min-w-52">
        {squareProgressData.map((item, index) => (
          <div key={index} className="flex-1 flex items-center flex-col gap-2">
            <SquareProgress value={item.value} count={item.count} />
            <span className="text-xs font-semibold text-center">
              {item.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Ratings;