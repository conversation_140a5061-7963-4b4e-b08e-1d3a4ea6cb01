"use client";

import React from 'react';
import ProductCard from "@/screens/user/dashboard/pages/buyer/favourite/module/product/product-card";
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from '@/components/ui/carousel';

const Product = () => {
  // Array of product cards for demonstration
  const productCards = Array(8).fill(null);

  return (
    <div className="flex justify-center w-full mt-7 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-screen-2xl 2xl:max-w-[90%]">
        {/* Product grid for larger screens - Hidden on small screens */}
        <div className="hidden sm:block bg-white w-full">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {productCards.map((_, index) => (
              <ProductCard key={index} />
            ))}
          </div>
        </div>

        {/* Mobile Carousel - Visible only on small screens */}
        <div className="block sm:hidden bg-white w-full">
          <Carousel
            opts={{
              align: "start",
              dragFree: true
            }}
            className="w-full"
          >
            <CarouselContent className="">
              {productCards.map((_, index) => (
                <CarouselItem 
                  key={index} 
                  className=" basis-[81%]"
                >
                  <ProductCard />
                </CarouselItem>
              ))}
            </CarouselContent>
            {/* <CarouselPrevious className="bg-white shadow-lg border border-gray-100 rounded-full h-10 w-10 left-0 focus:ring-1 focus:ring-gray-200 focus:outline-none absolute" />
            <CarouselNext className="bg-white shadow-lg border border-gray-100 rounded-full h-10 w-10 right-0 focus:ring-1 focus:ring-gray-200 focus:outline-none absolute" /> */}
          </Carousel>
        </div>
      </div>
    </div>
  );
}

export default Product;