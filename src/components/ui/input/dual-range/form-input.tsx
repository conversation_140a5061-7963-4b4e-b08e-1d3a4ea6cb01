import classNames from "classnames";
import React, { FC } from "react";
import { InputLabel } from "../components/label";
import { InputWrapper } from "../components/wrapper";
import { DualRangeSlider, DualRangeSliderType } from "./";

interface DualRangeSliderFormInputType extends DualRangeSliderType {
  id?: string;
  icon?: React.ReactNode | string;
  containerClassName?: string;
  wrapperClassName?: string;
  labelClassName?: string;
  label?: string;
  required?: boolean;
  error?: string;
}

const DualRangeSliderFormInput: FC<DualRangeSliderFormInputType> = ({
  id,
  containerClassName,
  wrapperClassName,
  labelClassName,
  label,
  required,
  icon,
  error,
  ...rest
}) => {
  const uniqueId = React.useId();

  return (
    <div
      className={classNames(
        "flex flex-col gap-1.5 items-center [&>*]:w-full",
        containerClassName
      )}
    >
      {label && (
        <InputLabel
          htmlFor={id ?? uniqueId}
          className={labelClassName}
          required={required}
        >
          {label}
        </InputLabel>
      )}
      <InputWrapper
        icon={icon}
        className={wrapperClassName}
        error={Boolean(error)}
      >
        <DualRangeSlider {...rest} />
      </InputWrapper>
    </div>
  );
};

export { DualRangeSliderFormInput };
