"use client";

import StartPriceCard from "@/components/elements/card/price/start";
import FrequentlyAskedQuestions from "@/components/elements/list/faq";
import ReactMarkdown from "react-markdown";

const faqData = Array.from({ length: 4 })?.map(() => ({
  title: "Is listing my business free of charge?",
  description:
    "Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and.",
}));

const featuresData = Array.from({ length: 4 })?.map(() => ({
  title: "Is listing my business free of charge?",
  description:
    "Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply [learn more](/example.com/details).",
}));

const BuyLeadsPlan = () => {
  return (
    <div className="w-full">
      <div className="grid grid-cols-2 gap-6">
        {[...Array(+4).keys()]?.map((_, index) => {
          return (
            <StartPriceCard
              key={index}
              title="Free Plan"
              description="Lorem Ipsum is simply dummy text of the printing and typesetting
            industry."
              price="00"
              checklist={[
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: true,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: true,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: false,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: false,
                },
              ]}
            />
          );
        })}
      </div>

      <div className="w-full bg-white shadow-md rounded-md px-7 py-10 mt-8">
        <h3 className="font-bold mb-4">Key Features</h3>

        <ul className="list-disc pl-4">
          {featuresData?.map((fet, index) => {
            return (
              <li key={index} className="mb-3">
                <h4 className="text-sm font-medium mb-1">{fet?.title} </h4>
                <span className="text-sm">
                  <ReactMarkdown
                    components={{
                      p: ({ ...props }) => (
                        <p className="text-font mb-4" {...props} />
                      ),
                      a: ({ ...props }) => {
                        return (
                          <button
                            className="text-main/70 underline hover:text-main"
                            onClick={() => window.open(props?.href)}
                          >
                            {props?.children}
                          </button>
                        );
                      },
                    }}
                  >
                    {fet?.description}
                  </ReactMarkdown>
                </span>
              </li>
            );
          })}
        </ul>
      </div>

      <div className="w-full bg-white shadow-md rounded-md px-7 py-10 mt-8">
        <h3 className="font-bold mb-4">Frequently Asked Questions</h3>
        <FrequentlyAskedQuestions type="single" data={faqData} />
      </div>
    </div>
  );
};

export default BuyLeadsPlan;
