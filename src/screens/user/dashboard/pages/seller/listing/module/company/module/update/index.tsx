"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { Stepper } from "@/components/ui/stepper";
import { RootDispatch } from "@/redux/store";
import {
  companyDetails,
  currentFormStep,
} from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import {
  getCompany,
  getSubSubCategories,
  updateCompanyDetails,
} from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/thunk";
import { map } from "lodash";
import { AlertCircle } from "lucide-react";
import { useParams, usePathname } from "next/navigation";
import { Fragment, lazy, Suspense, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

export const companyFormStepData = [
  {
    title: "Basic Info",
    component: lazy(() => import("./info")),
  },
  {
    title: "Contact Info",
    component: lazy(() => import("./contact")),
  },
  {
    title: "Business Info",
    component: lazy(() => import("./details")),
  },
  {
    title: "Verification",
    component: lazy(() => import("./verification")),
  },
  {
    title: "Additional Info",
    component: lazy(() => import("./additional")),
  },
  // {
  //   title: "Product & Service & Portfolio",
  //   element: <NavigateTo path="/dashboard/portfolio" />,
  // },
];

const UpdateCompany = () => {
  const dispatch = useDispatch<RootDispatch>();

  const currentStep = useSelector(currentFormStep);
  const { data: values, meta, loading } = useSelector(companyDetails);
  const { company_id } = useParams();
  const pathname = usePathname();

  useEffect(() => {
    dispatch(getCompany());
    dispatch(getSubSubCategories());
  }, [dispatch]);

  useEffect(() => {
    dispatch(Actions.setCurrentFormStep(0));
  }, [dispatch]);

  useEffect(() => {
    if (company_id) {
      dispatch(getCompany());
    } else {
      dispatch(Actions.setFormValues(null));
    }

    return () => {
      dispatch(Actions.setFormValues(null));
    };
  }, [dispatch, company_id, pathname]);

  const handleSkip = (step: number) => {
    try {
      if (step < companyFormStepData?.length - 1) {
        dispatch(Actions.setCurrentFormStep(step + 1));
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleSave = (step: number) => {
    try {
      if (values) {
        const { _id, ...other } = values;
        const payload = {
          ...other,
          industry_category: map(
            other?.industry_category,
            (cat: any) => cat._id
          ),
          sub_category: map(
            other?.industry_category,
            (cat: any) => cat._id
          ),
        };
        const data = new FormData();
        Object.entries(payload).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (
              Array.isArray(value) &&
              value.some((fl) => fl instanceof File)
            ) {
              value.forEach((file: any) => {
                if (file instanceof File) {
                  data.append(key, file);
                }
              });
            } else if (value instanceof File || value instanceof Blob) {
              data.append(key, value);
            } else if (
              Array.isArray(value) &&
              value.some((fl) => typeof fl === "string")
            ) {
              value.forEach((str) => {
                data.append(key, str);
              });
            } else {
              data.append(key, String(value));
            }
          }
        });

        dispatch(updateCompanyDetails(data))
          .unwrap()
          .then((res) => {
            if (
              res.type === "success" &&
              currentStep < companyFormStepData?.length - 1
            ) {
              handleShift(step + 1);
            }
          })
          .catch((error) => {
            console.error(error);
          });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleShift = (step: number) => {
    try {
      dispatch(Actions.setCurrentFormStep(step));
      handleNavigate(step);
    } catch (error) {
      console.error(error);
    }
  };

  const handleNavigate = (step: number) => {
    try {
      // if (step === companyFormStepData?.length - 1) {
      //   router.push("/dashboard/portfolio", { state: { menuNavigate: false } });
      // }
    } catch (error) {
      console.error(error);
    }
  };

  console.log({ meta });

  if (!loading && !values) {
    return (
      <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
        <AlertCircle /> {meta?.message || "Something went Wrong, try again"}
      </div>
    );
  }

  return (
    <main className="relative bg-white rounded-lg md:shadow-lg md:p-4 w-full h-full">
      <Stepper
        data={companyFormStepData}
        active={currentStep}
        onSelect={(step) => handleShift(step)}
        className="mb-7 md:mb-10"
      />

      {loading && <Loader box big center />}

      {companyFormStepData?.map((step, index) => {
        if (currentStep !== index) return null;

        // if (!step?.component && !step?.element) return null;
        // if (step?.element) {
        //   return <Fragment key={index}>{step?.element}</Fragment>;
        // }

        return (
          <Fragment key={index}>
            {"element" in step ? (
              <>{step?.component}</>
            ) : (
              <Suspense fallback={<Loader big center className="h-[80vh]" />}>
                <div className="flex flex-col gap-9 [&>*]:w-full">
                  <step.component />
                </div>
              </Suspense>
            )}
          </Fragment>
        );
      })}

      <div className="flex items-center justify-end gap-3 mt-7 md:mt-10">
        {currentStep < companyFormStepData?.length - 1 && (
          <Button
            className="rounded-md"
            variant="destructive"
            onClick={() => handleSkip(currentStep)}
          >
            Skip
          </Button>
        )}
        <Button
          className="rounded-md"
          variant="main-revert"
          onClick={() => handleSave(currentStep)}
        >
          Save & Next
        </Button>
      </div>
    </main>
  );
};

export default UpdateCompany;
