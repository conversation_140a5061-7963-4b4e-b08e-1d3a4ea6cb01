"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import { Loader } from "@/components/ui/loader";
import { PRODUCTS_DATA } from "@/constants/mock/products";
import { productsList } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import { deleteProduct } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/thunk";
import { RootDispatch } from "@/redux/store";
import classNames from "classnames";
import { AlertCircle } from "lucide-react";
import { FC } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";

interface ListingType {
  className?: string;
}
const products = PRODUCTS_DATA;

const Listing: FC<ListingType> = ({ className }) => {
  const router = useRouter();
  const { loading } = useSelector(productsList);
  const dispatch = useDispatch<RootDispatch>();

  const handleEdit = (product_id: string) => {
    router.push(`/dashboard/seller/listing/products/${product_id}`);
  };

  const handleDelete = (product_id: string) => {
    dispatch(deleteProduct(product_id));
  };

  if (loading) {
    return <Loader big center />;
  }

  return (
    <main className={classNames("bg-white shadow-box p-4", className)}>
      <div className="space-y-4">
        {Array.isArray(products) && products?.length > 0 ? (
          <>
            <ListCardHeader />
            {products?.slice(0, 4).map((product) => (
              <ListCard
                key={product._id}
                image={product?.main_product_images?.[0]}
                title={product?.product_name}
                category={product?.category?.name ?? "Category"}
                views={"345"}
                status={product?.product_status}
                onEdit={() => handleEdit(product._id)}
                onDelete={() => handleDelete(product._id)}
              />
            ))}
          </>
        ) : (
          <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
            <AlertCircle /> No Products Available
          </div>
        )}
      </div>
    </main>
  );
};

export default Listing;
