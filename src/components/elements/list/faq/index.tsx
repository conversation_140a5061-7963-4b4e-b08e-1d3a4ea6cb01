import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  AccordionContentProps,
  AccordionItemProps,
  AccordionMultipleProps,
  AccordionSingleProps,
  AccordionTriggerProps,
} from "@radix-ui/react-accordion";
import { ComponentRef, forwardRef, ReactNode } from "react";

interface FAQItem {
  title: string;
  description: string | ReactNode;
}

interface CommonFAQProps {
  data: FAQItem[];
  itemProps?: AccordionItemProps;
  triggerProps?: AccordionTriggerProps;
  contentProps?: AccordionContentProps;
}

type FrequentlyAskedQuestionsProps = CommonFAQProps &
  (AccordionSingleProps | AccordionMultipleProps);

const FrequentlyAskedQuestions = forwardRef<
  ComponentRef<typeof Accordion>,
  FrequentlyAskedQuestionsProps
>(({ data, itemProps, triggerProps, contentProps, ...rest }, ref) => {
  return (
    <Accordion ref={ref} className="w-full" {...rest}>
      {Array.isArray(data) &&
        data.length > 0 &&
        data.map((faq, index) => (
          <AccordionItem
            key={index}
            value={faq.title + index}
            className="px-6"
            {...itemProps}
          >
            <AccordionTrigger {...triggerProps}>{faq.title}</AccordionTrigger>
            <AccordionContent {...contentProps}>
              {faq.description}
            </AccordionContent>
          </AccordionItem>
        ))}
    </Accordion>
  );
});

FrequentlyAskedQuestions.displayName = "FrequentlyAskedQuestions";

export default FrequentlyAskedQuestions;
