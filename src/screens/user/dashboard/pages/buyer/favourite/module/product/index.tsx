'use client';

import ProductCard from "./product-card";
import { getFavourites, deleteFavourite } from "../../@redux/thunk"; // ✅ make sure deleteFavourite is defined
import { getFavouritesSelector } from "../../@redux/selector";
import { useDispatch, useSelector } from "react-redux";
import { RootDispatch } from "@/redux/store";
import { useEffect } from "react";

interface ProductCardProps {
  [key: string]: any;
}

const Products = () => {
  const dispatch = useDispatch<RootDispatch>();

  const { data: favouriteProducts, loading } = useSelector(getFavouritesSelector);

  // Fetch on mount
  useEffect(() => {
    dispatch(
      getFavourites({
        favoriteType: "product",
        priority: 1,
      })
    );
  }, [dispatch]);

  return (
    <div className="bg-white">
      <div className="container mx-auto p-6">
        <h2 className="text-lg font-medium text-gray-800">
          {favouriteProducts?.length || 0} results found
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-6">
          {favouriteProducts?.map((product: ProductCardProps, idx: number) => (
            <ProductCard
              key={idx}
              product={product}
              onUnfavourite={() =>
                dispatch(
                  deleteFavourite({
                    favouriteId: product._id, // ✅ correct ID now
                    filters: {
                      favoriteType: "product",
                      priority: 1,
                    },
                  })
                )
              }
            />
          ))}
        </div>

        {loading && (
          <div className="flex items-center justify-center mt-8 mb-4">
            <span className="flex items-center justify-center gap-2 bg-brown text-white rounded-full px-4 py-2.5 text-sm font-medium whitespace-nowrap max-w-max">
              Loading more...
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default Products;
