"use client";
import classNames from "classnames";
import { X, Search, Mic } from "lucide-react";
import { ChangeEvent, FC, useRef, useState, useEffect } from "react";
import { FiSearch } from "react-icons/fi";
import { IoMicOutline } from "react-icons/io5";
import { useRouter } from "next/navigation";

interface SearchInputProps {
  className?: string;
  placeholder?: string;
  onSearch?: (searchTerm: string) => void;
  onExpandedChange?: (isExpanded: boolean) => void;
  showBranding?: boolean;
}

const suggestions = [
  { id: 1, category: "Electric", item: "Iron", type: "product" },
  { id: 2, category: "Electric", item: "Refrigerator", type: "product" },
  { id: 3, category: "Electric", item: "Cars", type: "product" },
  { id: 4, category: "Electric", item: "Scooters", type: "product" },
  { id: 5, category: "Electric", item: "Cameras", type: "product" },
  { id: 6, category: "Electric", item: "LED Bulbs", type: "product" },
  { id: 7, category: "Electric", item: "Power Drills", type: "product" },
  { id: 8, category: "Electric", item: "Hair Dryers", type: "product" },
  { id: 9, category: "Electric", item: "Power Banks", type: "product" },
];

import Branding from "../branding";

const ExpandableSearchInput: FC<SearchInputProps> = ({
  className = "",
  placeholder = "Search for products, services, companies",
  onSearch,
  onExpandedChange,
  showBranding = true,
}) => {
  const router = useRouter();
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState<string>("");
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>("Products");
  const [isFocused, setFocused] = useState(false);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isUltraSmall, setIsUltraSmall] = useState<boolean>(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
      setIsUltraSmall(window.innerWidth < 350);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    if (onExpandedChange) {
      onExpandedChange(isExpanded);
    }
  }, [isExpanded, onExpandedChange]);

  // Add/remove backdrop when search expands
  useEffect(() => {
    if (isExpanded) {
      // Add backdrop overlay
      const backdrop = document.createElement('div');
      backdrop.id = 'search-backdrop';
      backdrop.className = 'fixed inset-0 bg-black/50 z-40 transition-opacity duration-300';
      backdrop.style.opacity = '0';
      document.body.appendChild(backdrop);
      
      // Trigger fade-in animation
      requestAnimationFrame(() => {
        backdrop.style.opacity = '1';
      });

      // Prevent body scroll when search is expanded
      document.body.style.overflow = 'hidden';
      
      return () => {
        // Clean up backdrop and restore scroll
        const existingBackdrop = document.getElementById('search-backdrop');
        if (existingBackdrop) {
          existingBackdrop.style.opacity = '0';
          setTimeout(() => {
            existingBackdrop.remove();
          }, 300);
        }
        document.body.style.overflow = '';
      };
    }
  }, [isExpanded]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsExpanded(false);
        setShowDropdown(false);
        setFocused(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const navigateToResults = (searchTerm: string) => {
    if (!searchTerm.trim()) return;

    const encodedSearchTerm = encodeURIComponent(searchTerm.trim());
    
    switch (activeTab.toLowerCase()) {
      case "products":
        router.push('/products');
        break;
      case "companies":
        router.push('/companies');
        break;
      case "services":
        router.push('/services');
        break;
      default:
        router.push('/products');
    }
  };

  const handleInputClick = () => {
    setIsExpanded(true);
    setFocused(true);
    if (value.length > 0) {
      setShowDropdown(true);
    }
  };

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    const searchValue = e?.target?.value;
    setValue(searchValue);

    if (searchValue.length > 0) {
      setIsExpanded(true);
      setShowDropdown(true);
    }

    if (onSearch) {
      onSearch(searchValue);
    }
  };

 const handleSelect = (suggestion: any) => {
  const searchTerm = `${suggestion.category} ${suggestion.item}`;
  setValue(searchTerm);
  setShowDropdown(false);
  // Remove this line to prevent immediate navigation:
  // navigateToResults(searchTerm);
  
  // Keep focus on input so user can modify or click search
  inputRef?.current?.focus?.();
};

  const handleSubmit = (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    
    if (value.trim()) {
      navigateToResults(value);
      
      if (onSearch) {
        onSearch(value);
      }
    }
    
    setShowDropdown(false);
    setIsExpanded(false);
    setFocused(false);
  };

  const clearSearch = () => {
    setValue("");
    setShowDropdown(false);
    inputRef?.current?.focus?.();
  };

  const handleClose = () => {
    setIsExpanded(false);
    setShowDropdown(false);
    setFocused(false);
  };

  const filteredSuggestions = suggestions.filter(
    (suggestion) =>
      suggestion.item.toLowerCase().includes(value.toLowerCase()) ||
      suggestion.category.toLowerCase().includes(value.toLowerCase())
  );

  const tabs = ["Companies", "Products", "Services"];

  if (!isExpanded) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div
          className={classNames(
            "flex-1 min-h-min w-full flex items-center gap-1 border bg-white border-slate-400 rounded-full overflow-hidden p-0.5 relative",
            isUltraSmall && "gap-0.5"
          )}
        >
          <button
            type="button"
            onClick={handleInputClick}
            className={`sm:hidden flex items-center justify-center text-stone-700 hover:text-gray-900 flex-shrink-0 ${
              isUltraSmall ? "w-6 min-w-6" : "w-8 min-w-8"
            }`}
          >
            <FiSearch size={isUltraSmall ? 14 : 16} />
          </button>
          <input
            ref={inputRef}
            type="text"
            className={`flex-1 bg-transparent text-sm placeholder-gray-500 border-0 outline-0 rounded-l-full min-w-0 ${
              isUltraSmall
                ? "px-2 py-1.5 text-xs placeholder:text-xs"
                : "px-4 py-2"
            }`}
            placeholder={isUltraSmall ? "Search..." : placeholder}
            value={value}
            onChange={handleSearch}
            onClick={handleInputClick}
            onFocus={() => setFocused(true)}
            onKeyPress={(e) => {
              if (e.key === "Enter") {
                handleSubmit();
              }
            }}
          />
          {value && (
            <button
              type="button"
              className={`text-stone-700 hover:text-gray-900 flex items-center justify-center flex-shrink-0 ${
                isUltraSmall ? "w-5 min-w-5" : "w-6 min-w-6"
              }`}
              onClick={clearSearch}
            >
              <X size={isUltraSmall ? 16 : 20} />
            </button>
          )}
          {!isUltraSmall && (
            <button
              type="button"
              className="text-stone-700 hover:text-gray-900 flex items-center justify-center w-6 min-w-6 flex-shrink-0"
            >
              <IoMicOutline size={20} />
            </button>
          )}
          <button
            type="submit"
            onClick={handleSubmit}
            className={`bg-main text-white flex items-center justify-center gap-1 h-full rounded-full outline-0 border-none hover:bg-orange-600 transition-colors flex-shrink-0 ${
              isUltraSmall
                ? "px-2 py-1.5 hidden"
                : "hidden sm:flex px-3.5 py-2"
            }`}
          >
            <FiSearch size={14} />
            {!isUltraSmall && (
              <span className="font-semibold text-sm">Search</span>
            )}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`w-full ${isMobile ? "fixed inset-0 z-50 bg-white overflow-y-auto" : "bg-white p-6 relative z-50"} ${className}`}
    >
      {isMobile && (
        <div className="flex items-center justify-between p-3 border-b border-gray-200 min-h-[56px]">
          <h3
            className={`font-semibold text-gray-900 ${isUltraSmall ? "text-sm" : "text-base"}`}
          >
            Search
          </h3>
          <button
            type="button"
            onClick={handleClose}
            className="p-1.5 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-colors flex-shrink-0"
          >
            <X size={16} />
          </button>
        </div>
      )}

      <div className={isMobile ? "p-3 pb-safe" : ""}>
        {isMobile && (
          <div className={`flex ${isMobile ? "justify-start overflow-x-auto scrollbar-hide" : "justify-center"} ${isMobile ? "mb-3" : "mb-2"}`}>
            <div className={`flex ${isMobile ? "space-x-4 min-w-max px-1" : "space-x-8"} ${isUltraSmall && "space-x-2"}`}>
              {tabs.map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`pb-2 transition-colors whitespace-nowrap font-medium text-2xl ${
                    isUltraSmall ? "px-1 text-sm" : "px-2"
                  } ${
                    activeTab === tab
                      ? "text-gray-900 border-b-2 border-orange-500"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>
        )}

        {!isMobile && (
          <div className="w-full">
            <div className="flex items-center justify-between -mt-8">
              {showBranding && (
                <div className="flex-shrink-0">
                  <Branding />
                </div>
              )}

              <div className="flex-1 flex justify-center">
                <div className="flex space-x-8">
                  {tabs.map((tab) => (
                    <button
                      key={tab}
                      onClick={() => setActiveTab(tab)}
                      className={`pb-2 transition-colors whitespace-nowrap font-medium px-3 text-xl ${
                        activeTab === tab
                          ? "text-gray-900 border-b-2 border-orange-500"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      {tab}
                    </button>
                  ))}
                </div>
              </div>

              {showBranding && <div className="flex-shrink-0 w-48"></div>}
            </div>
          </div>
        )}

        <div className="relative max-w-4xl mx-auto">
          <div
            className={`flex items-center bg-gray-50 border border-gray-300 rounded-full shadow-sm ${
              isUltraSmall ? "p-0.5 gap-0.5" : "p-1 gap-1"
            }`}
          >
            <input
              ref={inputRef}
              type="text"
              className={`flex-1 bg-transparent placeholder-gray-500 border-0 outline-0 rounded-l-full min-w-0 ${
                isUltraSmall
                  ? "px-3 py-2 text-sm"
                  : isMobile
                    ? "px-4 py-2.5 text-base"
                    : "px-6 py-3 text-base"
              }`}
              placeholder={
                isUltraSmall
                  ? "Search..."
                  : isMobile
                    ? `Search ${activeTab.toLowerCase()}`
                    : `Search for ${activeTab.toLowerCase()}`
              }
              value={value}
              onChange={handleSearch}
              onFocus={() => {
                if (value.length > 0) {
                  setShowDropdown(true);
                }
              }}
              onKeyPress={(e) => {
                if (e.key === "Enter") {
                  handleSubmit();
                }
              }}
              autoFocus
            />

            {value && (
              <button
                type="button"
                onClick={clearSearch}
                className={`text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0 ${
                  isUltraSmall ? "p-1" : "p-2"
                }`}
              >
                <X size={isUltraSmall ? 14 : 16} />
              </button>
            )}

            {!isUltraSmall && (
              <button
                type="button"
                className={`text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0 ${
                  isUltraSmall ? "p-1" : "p-2"
                }`}
              >
                <Mic size={isUltraSmall ? 14 : 16} />
              </button>
            )}

            <button
              type="button"
              onClick={handleSubmit}
              className={`flex items-center gap-1 bg-main hover:bg-orange-600 text-white rounded-full font-medium transition-colors flex-shrink-0 ${
                isUltraSmall
                  ? "px-2 py-2 text-xs"
                  : isMobile
                    ? "px-3 py-2.5 text-sm gap-2"
                    : "px-6 py-3 gap-2"
              }`}
            >
              <Search size={isUltraSmall ? 12 : 14} />
              <span className={isUltraSmall ? "hidden" : ""}>Search</span>
            </button>
          </div>

          {isExpanded &&
            (value.length > 0
              ? filteredSuggestions.length > 0
              : suggestions.length > 0) && (
              <div
                className={`${isMobile ? "mt-4" : "absolute top-full left-0 right-0 mt-2"} bg-white border border-gray-200 rounded-lg shadow-lg z-50 ${isMobile ? "max-h-96" : "max-h-80"} overflow-y-auto`}
              >
                {(value.length > 0 ? filteredSuggestions : suggestions).map(
                  (suggestion) => (
                    <button
                      key={suggestion.id}
                      onClick={() => handleSelect(suggestion)}
                      className={`w-full text-left hover:bg-gray-50 flex items-center gap-2 border-b border-gray-100 last:border-b-0 transition-colors ${
                        isUltraSmall
                          ? "px-3 py-2 gap-1"
                          : isMobile
                            ? "px-4 py-3 gap-3"
                            : "px-6 py-3 gap-3"
                      }`}
                    >
                      <span
                        className={`text-gray-500 flex-shrink-0 ${
                          isUltraSmall ? "text-xs" : "text-sm"
                        }`}
                      >
                        {suggestion.category}
                      </span>
                      <span
                        className={`font-medium text-gray-900 truncate ${
                          isUltraSmall ? "text-sm" : ""
                        }`}
                      >
                        {suggestion.item}
                      </span>
                    </button>
                  )
                )}
              </div>
            )}
        </div>
      </div>
    </div>
  );
};

export default ExpandableSearchInput;