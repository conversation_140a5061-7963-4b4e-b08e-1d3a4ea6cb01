"use client";

import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { InputTargetType } from "@/components/ui/input/types";
import { languages, regions } from "@/constants/user/seller/company/update";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import { serviceDetails } from "../../../@redux/selectors";

const ServiceDetails = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(serviceDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values[e?.target?.name])
          ? Array.from(values[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setServiceDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <MultiSelect
        onValueChange={handleChange}
        name="usp"
        defaultValue={values?.usp}
        value={values?.usp}
        label="USP (Unique Selling price)"
        placeholder="Select Sub-Category"
        variant="inverted"
        maxCount={3}
        options={[]}
        addNew
      />
      <MultiSelect
        onValueChange={handleChange}
        name="location"
        defaultValue={values?.location}
        value={values?.location}
        label="Service Area/Location"
        placeholder="Select Locations"
        variant="inverted"
        maxCount={3}
        options={regions}
      />
      <MultiSelect
        onValueChange={handleChange}
        name="language"
        defaultValue={values?.language}
        value={values?.language}
        label="Languages Supported"
        placeholder="Select Languages"
        variant="inverted"
        maxCount={3}
        options={languages}
      />
      <Input
        type="text"
        name="meta_title"
        label="Meta Title"
        placeholder="Enter title"
        value={values?.meta_title}
        onChange={handleChange}
      />
      <Textarea
        name="meta_description"
        label="Meta Description"
        onChange={handleChange}
        value={values?.meta_description}
        placeholder="Type here"
        className="min-h-20"
        containerClassName="flex-1"
        maxLength={500}
        showCharCount
      />
      <MultiSelect
        label="Meta Keywords"
        placeholder="Add Keywords"
        name="meta_keywords"
        defaultValue={values?.meta_keywords ?? []}
        onValueChange={handleChange}
        options={[]}
        maxCount={3}
        addNew
      />
      <Select
        name="service_status"
        label="Service Status"
        onChange={handleChange}
        value={values?.service_status}
        placeholder="Select Status"
        options={[
          { label: "Active", value: true },
          { label: "Inactive", value: false },
        ]}
      />
    </>
  );
};

export default ServiceDetails;
