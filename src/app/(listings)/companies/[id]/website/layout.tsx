"use client";

import BreadcrumbRender from "@/components/ui/breadcrumb/render";
import { useDOMOperator } from "@/lib/hooks/useDOMOperator";
import {
  CompanyWebsiteTabs,
  TabsData,
} from "@/screens/company/details/website/module";
import WebsiteFooter from "@/screens/company/details/website/module/components/footer";
import CompanyProfile from "@/screens/company/details/website/module/components/profile";
import { useParams, usePathname } from "next/navigation";

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const params = useParams();
  const company_id = params.id as string;

  useDOMOperator({
    selectors: ["#global-header", "#global-footer"],
    operation: "hide",
  });

  return (
    <>
      <CompanyProfile />
      <CompanyWebsiteTabs company_id={company_id} />

      {!(
        pathname?.toLowerCase()?.endsWith("website") ||
        pathname?.toLowerCase()?.endsWith("website/")
      ) && (
        <div className="container my-6">
          <BreadcrumbRender
            className="mb-4 font-medium"
            data={[
              {
                title: "Home",
                pathname: pathname
                  .split("/")
                  .filter(Boolean)
                  .slice(0, 2)
                  .join("/"),
              },
              {
                title:
                  TabsData(company_id).find((tab) => tab.path === pathname)
                    ?.label ?? pathname.split("/")[3],
                pathname: pathname,
              },
            ]}
          />
        </div>
      )}

      {children}

      <WebsiteFooter />
    </>
  );
}
