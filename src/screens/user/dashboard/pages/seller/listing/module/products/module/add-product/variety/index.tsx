import {
  MultiOptionInput,
  MultiOptionInputTargetType,
} from "@/components/elements/form/multi-options";
import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import { InputTargetType } from "@/components/ui/input/types";
import { productDetails } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import { ChangeEvent, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";

const Variety = () => {
  const dispatch = useDispatch();
  const { data: values } = useSelector(productDetails);

  const handleChange = useCallback(
    (
      e:
        | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
        | InputTargetType
        | MultiSelectTarget
        | DropzoneInputTargetType
        | MultiOptionInputTargetType
    ) => {
      try {
        const payload: any = {};
        if (isDropzoneInputTargetType(e)) {
          const fileArray = Array.from(e.target.files);
          let fileList = Array.isArray(values[e?.target?.name])
            ? Array.from(values[e?.target?.name])
            : [];
          fileList = fileList.concat(fileArray);
          payload[e?.target?.name] = fileList;
        } else if ("option" in e?.target) {
          payload[e?.target?.name] = {
            display_name: e?.target?.value,
            option: e?.target?.option,
          };
        } else {
          payload[e?.target?.name] = e?.target?.value;
        }
        dispatch(Actions.setProductDetails(payload));
      } catch (error) {
        console.error(error);
      }
    },
    [dispatch, values]
  );

  return (
    <>
      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Material</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your product
          </span>
        </div>

        <MultiOptionInput
          value={{
            name: values?.material?.display_name,
            option: values?.material?.option,
          }}
          onChange={handleChange}
          name="material"
        />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Size</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your product
          </span>
        </div>

        <MultiOptionInput
          value={{
            name: values?.size?.display_name,
            option: values?.size?.option,
          }}
          onChange={handleChange}
          name="size"
        />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Color Variants</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your product
          </span>
        </div>

        <MultiOptionInput
          value={{
            name: values?.color_variants?.display_name,
            option: values?.color_variants?.option,
          }}
          onChange={handleChange}
          name="color_variants"
        />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Shape & Design</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your product
          </span>
        </div>

        <MultiOptionInput
          value={{
            name: values?.shape_design?.display_name,
            option: values?.shape_design?.option,
          }}
          onChange={handleChange}
          name="shape_design"
        />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Packaging Details</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your product
          </span>
        </div>

        <MultiOptionInput
          value={{
            name: values?.packaging_details?.display_name,
            option: values?.packaging_details?.option,
          }}
          onChange={handleChange}
          name="packaging_details"
        />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Shelf Life / Expiry date</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your product
          </span>
        </div>

        <MultiOptionInput
          value={{
            name: values?.shelf_Life?.display_name,
            option: values?.shelf_Life?.option,
          }}
          onChange={handleChange}
          name="shelf_Life"
        />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Product Functionality</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your product
          </span>
        </div>

        <MultiOptionInput
          value={{
            name: values?.product_functionality?.display_name,
            option: values?.product_functionality?.option,
          }}
          onChange={handleChange}
          name="product_functionality"
        />
      </div>
    </>
  );
};

export default Variety;
