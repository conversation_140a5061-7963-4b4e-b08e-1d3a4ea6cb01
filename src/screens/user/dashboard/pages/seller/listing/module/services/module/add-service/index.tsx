"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { RootDispatch } from "@/redux/store";
import { serviceDetails } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import {
  getService,
  updateServiceDetails,
} from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/thunk";
import { AlertCircle } from "lucide-react";
import { useParams, usePathname, useRouter } from "next/navigation";
import { lazy, Suspense, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getSubSubCategories } from "../../../company/@redux/thunk";
import {
  normalizeToStrings,
  removeEmptyValues,
  removeKeysFromObject,
} from "@/lib/utils/helper";

const formData = [
  {
    title: "Basic Service Information",
    component: lazy(() => import("./info")),
  },
  {
    title: "Service Details",
    component: lazy(() => import("./details")),
  },
  {
    title: "Pricing Order Details",
    component: lazy(() => import("./pricing")),
  },
  {
    title: "Service Specifications",
    component: lazy(() => import("./specifications")),
  },
  {
    title: "Visual Media & additional Services",
    component: lazy(() => import("./media")),
  },
  // {
  //   title: "Service Specifications",
  //   component: lazy(() => import("./variety")),
  // },
];

const AddService = () => {
  const dispatch = useDispatch<RootDispatch>();
  const router = useRouter();
  const { data: values, meta, loading } = useSelector(serviceDetails);
  const pathname = usePathname();
  const params = useParams();
  const service_id = params?.id;

  useEffect(() => {
    dispatch(getSubSubCategories());
  }, [dispatch]);

  useEffect(() => {
    if (service_id) {
      dispatch(getService(service_id as string));
    } else {
      dispatch(Actions.setServiceDetails(null));
    }

    return () => {
      dispatch(Actions.setServiceDetails(null));
    };
  }, [dispatch, service_id, pathname]);

  const handleSave = (addNew?: boolean) => {
    try {
      if (values) {
        let payload = {
          ...values,
          sub_sub_category: normalizeToStrings(
            values?.sub_sub_category,
            "string"
          ),
        };

        payload = removeEmptyValues(
          removeKeysFromObject(payload, [
            "_id",
            "business_id",
            "created_by",
            "createdAt",
            "updatedAt",
            "sub_category",
            "industry_category",
          ])
        );

        const data = new FormData();
        Object.entries(payload).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (
              Array.isArray(value) &&
              value.some((fl) => fl instanceof File)
            ) {
              value.forEach((file: any) => {
                if (file instanceof File) {
                  data.append(key, file);
                }
              });
            } else if (value instanceof File || value instanceof Blob) {
              data.append(key, value);
            } else if (
              Array.isArray(value) &&
              value.some((fl) => typeof fl === "string")
            ) {
              value.forEach((str) => {
                data.append(key, String(str));
              });
            } else {
              data.append(key, String(value));
            }
          }
        });

        dispatch(
          updateServiceDetails({
            service_id: service_id as string,
            data,
          })
        )
          .unwrap()
          .then((res) => {
            if (res?.type === "success") {
              if (addNew) {
                router.push("/dashboard/seller/listing/services/add");
                dispatch(Actions.setServiceDetails(null));
              } else {
                router.push("/dashboard/seller/listing/services");
              }
            }
          })
          .catch((error) => {
            console.error(error);
          });
      }
    } catch (error) {
      console.error(error);
    }
  };

  if (service_id && !loading && !values && meta?.status === 404) {
    return (
      <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
        <AlertCircle /> {meta?.message}
      </div>
    );
  }

  return (
    <main className="w-full h-full">
      <h2 className="text-base font-semibold mb-6">
        {service_id ? "Update" : "Add"} Service
      </h2>

      {loading && <Loader box big center />}
      <Accordion
        type="multiple"
        className="w-full"
        defaultValue={formData?.map((_, i) => String(i))}
      >
        {formData?.map((form, index) => {
          return (
            <AccordionItem
              key={index}
              value={String(index)}
              className="bg-white rounded-lg md:shadow-box md:p-4 [&:not(:last-child)]:mb-3"
            >
              <AccordionTrigger>{form?.title}</AccordionTrigger>
              <AccordionContent
                className="!py-6 px-7 flex flex-col gap-8 [&>*]:w-full"
                disabled={loading}
                loading={loading}
              >
                <Suspense fallback={<Loader center big />}>
                  <form.component />
                </Suspense>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>

      <div className="flex items-center justify-start md:justify-end flex-wrap gap-2 mt-7 [&>*]:rounded-md">
        <Button variant="destructive" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button
          variant="destructive"
          onClick={() => handleSave(true)}
          disabled={!values}
        >
          Save & Add New Service
        </Button>
        <Button
          variant="main-revert"
          onClick={() => handleSave()}
          disabled={!values}
        >
          Save
        </Button>
      </div>
    </main>
  );
};

export default AddService;
