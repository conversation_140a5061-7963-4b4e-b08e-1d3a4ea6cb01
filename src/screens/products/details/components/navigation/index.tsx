import { Package } from "lucide-react";

const MenuData = [
  {
    path: "#1",
    name: "Products",
    icon: <Package />,
  },
  {
    path: "#1",
    name: "Projects",
    icon: Package,
  },
  {
    path: "#1",
    name: "Contact",
    icon: Package,
  },
  {
    path: "#1",
    name: "More",
    icon: Package,
  },
];

const ProductNavigation = () => {
  return (
    <div className="bg-white flex md:hidden items-center justify-between gap-3 shadow-box px-2 py-3 [&_svg]:w-6 [&_svg]:h-6">
      {MenuData?.map((item) => (
        <a key={item?.path} href={item?.path} className="flex-1 flex flex-col items-center gap-2 px-1.5">
          <Package />
          <span className="text-sm">{item.name}</span>
        </a>
      ))}
    </div>
  );
};

export default ProductNavigation;
