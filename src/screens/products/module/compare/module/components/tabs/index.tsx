import classNames from "classnames";
import { useState } from "react";

const tabsData = [
  {
    title: "Companies",
  },
  {
    title: "Products",
  },
  {
    title: "Services",
  },
];

const CompareTabs = () => {
  const [selected, setSelected] = useState<number>(0);

  return (
    <div className="flex items-center justify-center gap-4 md:gap-8 text-white pt-12 md:pt-24">
      {tabsData?.map((tab, index) => {
        const isActive = index === selected;
        return (
          <button
            key={index}
            className={classNames(
              "text-xl md:text-4xl pb-1.5 md:pb-3 transition-[border] border-b-2",
              {
                "font-semibold  border-white": isActive,
                "font-normal px-1 border-transparent": !isActive,
              }
            )}
            onClick={() => setSelected(index)}
          >
            {tab?.title}
          </button>
        );
      })}
    </div>
  );
};

export default CompareTabs;
