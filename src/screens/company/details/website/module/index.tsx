"use client";

import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { Skeleton } from "@/components/ui/loader/skeleton";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import useScrolled from "@/lib/hooks/useScrolled";
import classNames from "classnames";
import { Fragment, lazy, Suspense, useRef } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import Link from "next/link";

export const TabsData = (company_id: string) => [
  {
    label: "Home",
    path: `/companies/${company_id}/website`,
  },
  {
    label: "About Us",
    path: `/companies/${company_id}/website/about`,
  },
  {
    label: "Products",
    path: `/companies/${company_id}/website/products`,
    menu: [
      {
        label: "Product 1",
        path: `/companies/${company_id}/website/products/1`,
      },
      {
        label: "Product 2",
        path: `/companies/${company_id}/website/products/2`,
      },
      {
        label: "Product 3",
        path: `/companies/${company_id}/website/products/3`,
      },
      {
        label: "Product 4",
        path: `/companies/${company_id}/website/products/4`,
      },
    ],
  },
  {
    label: "Services",
    path: `/companies/${company_id}/website/services`,
    menu: [
      {
        label: "Services 1",
        path: `/companies/${company_id}/website/services/1`,
      },
      {
        label: "Services 2",
        path: `/companies/${company_id}/website/services/2`,
      },
      {
        label: "Services 3",
        path: `/companies/${company_id}/website/services/3`,
      },
      {
        label: "Services 4",
        path: `/companies/${company_id}/website/services/4`,
      },
    ],
  },
  {
    label: "Projects",
    path: `/companies/${company_id}/website/projects`,
  },
  {
    label: "Reviews",
    path: `/companies/${company_id}/website/reviews`,
  },
  {
    label: "Faq",
    path: `/companies/${company_id}/website/faq`,
  },
  {
    label: "Contact Us",
    path: `/companies/${company_id}/website/contact`,
    hideTab: false,
  },
];

export const CompanyWebsiteTabs = ({ company_id }: { company_id: string }) => {
  const router = useRouter();
  const pathname = usePathname();
  const tabsData = TabsData(company_id);
  const initialActive = tabsData?.[0]?.path;
  const isScrolled = useScrolled();

  return (
    <div
      className={classNames(
        "bg-white border-b border-stone-300 hidden md:block",
        isScrolled && "shadow-md sticky top-0 z-20"
      )}
    >
      <div className="container">
        <Tabs
          defaultValue={initialActive}
          value={pathname ?? undefined}
          className="no-scrollbar"
        >
          <TabsList className="flex-nowrap !py-0">
            {tabsData?.map((tab: any, index) => {
              if (!tab?.label || tab?.hideTab) return null;

              if (tab?.menu) {
                return (
                  <Dropdown
                    key={index}
                    inlineAlign="left"
                    content={
                      <DropdownList
                        data={tab?.menu}
                        onSelect={(option) => router.push(option?.path)}
                        className="min-w-min w-full"
                        closeOnSelect
                      />
                    }
                    hover
                    contentClassName="z-20 !w-full"
                  >
                    <TabsTrigger
                      key={index}
                      value={tab?.path}
                      className="flex-1 py-2 !text-sm !font-bold capitalize before:data-[state=active]:bottom-0 hover:bg-main/10"
                    >
                      <Link href={tab?.path}>{tab?.label}</Link>
                    </TabsTrigger>
                  </Dropdown>
                );
              }

              return (
                <TabsTrigger
                  key={index}
                  value={tab?.path}
                  className={classNames(
                    "flex-1 py-2 !text-sm !font-bold capitalize before:data-[state=active]:bottom-0 hover:bg-main/10"
                  )}
                >
                  <Link href={tab?.path}>{tab?.label}</Link>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>
      </div>
    </div>
  );
};

const homeRenderData = [
  {
    label: "Company Banner",
    path: "banner",
    component: lazy(() => import("./components/information/banner")),
    screenWidth: true,
  },
  {
    label: "About Company",
    path: "company",
    component: lazy(() => import("../../landing/components/information/about")),
  },
  {
    path: "company",
    component: lazy(
      () => import("../../landing/components/information/photos")
    ),
  },
  {
    label: "products & Services",
    path: "products",
    component: lazy(
      () => import("../../landing/components/information/products")
    ),
  },
  {
    label: "Portfolio/Case Studies",
    path: "portfolios",
    component: lazy(
      () => import("../../landing/components/information/portfolios")
    ),
  },
  // {
  //   label: "Clientele",
  //   path: "clientele",
  //   component: lazy(() =>
  //     import("../../details/module/components/information/clientele")
  //   ),
  // },
  {
    label: "Key Features",
    path: "features",
    component: lazy(
      () => import("../../landing/components/information/features")
    ),
  },
  {
    label: "Key Features",
    path: "features",
    hideTab: true,
    ad: true,
    component: lazy(() => import("@/components/layout/ads/banner")),
  },
  {
    label: "Reviews",
    path: "reviews",
    component: lazy(
      () => import("../../landing/components/information/reviews")
    ),
  },
  {
    label: "Contact",
    path: "contact",
    hideTab: true,
    component: lazy(
      () => import("../../landing/components/information/contact")
    ),
  },
  {
    label: "Get a Quote",
    path: "quote",
    component: lazy(() => import("../../landing/components/information/quote")),
  },
];

const CompanyWebsite = () => {
  const sectionRefs = useRef<Record<string, HTMLElement>>({});

  return (
    <div className="mb-8">
      <div className="relative p-0">
        <div className="grid grid-cols-1 items-start gap-6 w-full max-w-full">
          <div className="flex flex-col items-start gap-6 w-full max-w-full">
            {homeRenderData?.map((tab, index) => {
              if (!tab.component) return null;

              return (
                <Fragment key={index}>
                  <ErrorBoundary fallback={<div>Failed to load section</div>}>
                    <div
                      className={classNames(
                        "w-full",
                        !tab?.screenWidth && "sm:container"
                      )}
                    >
                      <Suspense fallback={<Skeleton className="w-full h-44" />}>
                        <section
                          id={tab.path}
                          ref={(el) => {
                            if (el) sectionRefs.current[tab.path] = el;
                          }}
                          className={classNames(
                            "w-full sm:scroll-mt-44 sm:shadow-md rounded-md",
                            {
                              "bg-white": ["company"]?.includes(tab?.path),
                              "bg-[#FAFAFA]": [
                                "products",
                                "features",
                                "reviews",
                                "contact",
                                "related",
                                "clientele",
                                "portfolios",
                              ]?.includes(tab?.path),
                              "bg-gradient-to-l from-[#FFF5F6] via-[#FDFDFD]  to-[#FFFAF4]":
                                ["quote"]?.includes(tab?.path),
                              "px-6 md:px-8 py-7 ":
                                !tab?.ad && !tab?.screenWidth,
                            }
                          )}
                        >
                          <tab.component />
                        </section>
                      </Suspense>
                    </div>
                  </ErrorBoundary>
                </Fragment>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyWebsite;
