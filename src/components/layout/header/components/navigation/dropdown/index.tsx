"use client";

import useOutsideAlert from "@/lib/hooks/useOutsideAlert";
import classNames from "classnames";
import { Dispatch, SetStateAction, useRef, useState } from "react";
import { MdChevronRight } from "react-icons/md";
import Link from "next/link";

type NavigationDataType = any;

const NavigationDropdown = ({
  onClose,
  data,
}: {
  onClose: () => void;
  data: NavigationDataType;
}) => {
  const navRef = useRef<HTMLDivElement>(null);
  const [isHidden, setIsHidden] = useState(false);

  const [current, setCurrent] = useState<NavigationDataType | null>(data[0]);
  const [subCurrent, setSubCurrent] = useState<NavigationDataType | null>(
    current?.menus?.[0]
  );

  useOutsideAlert(navRef, onClose);

  const handleCategoryClick = () => {
    setIsHidden(true);
    onClose();
  };

  return (
    <div
      className={classNames(
        "w-full h-full bg-black bg-opacity-70 absolute left-0 top-full border-t border-t-gray-300 z-40",
        isHidden ? "hidden" : "hidden group-hover:block"
      )}
    >
      <div className="w-full h-max bg-white shadow-lg">
        <div ref={navRef} className="w-full h-max bg-slate-50 bg-opacity-50">
          <div className="container">
            <div className="py-6 flex items-start gap-6">
              <div className="relative flex items-start flex-col gap-2">
                <ListBox
                  data={data}
                  className="flex-1 min-w-[20vw] max-w-[25vw]"
                  current={current}
                  setCurrent={setCurrent}
                  onClose={handleCategoryClick}
                />
                <Link
                  href="/categories"
                  onClick={handleCategoryClick}
                  className={classNames(
                    "!text-main !underline cursor-pointer text-sm px-4 pt-2"
                  )}
                >
                  Explore All Categories
                </Link>
              </div>
              {current && (
                <>
                  <ListBox
                    subMenu
                    data={current?.menus}
                    className="min-w-[20vw] max-w-[25vw]"
                    current={subCurrent}
                    setCurrent={setSubCurrent}
                    setSubCurrent={setSubCurrent}
                    onClose={handleCategoryClick}
                  />
                  {subCurrent && (
                    <GridBox
                      data={subCurrent?.menus}
                      onClose={handleCategoryClick}
                    />
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface ListBoxType {
  className: string;
  data: any;
  current: NavigationDataType | null;
  setCurrent: Dispatch<SetStateAction<NavigationDataType | null>>;
  setSubCurrent?: Dispatch<SetStateAction<NavigationDataType | null>>;
  subMenu?: boolean;
  onClose: () => void;
}

const ListBox = ({
  data,
  className,
  current,
  setCurrent,
  subMenu,
  setSubCurrent,
  onClose,
}: ListBoxType) => (
  <div className={classNames("p-1 bg-white", className)}>
    <ul className="list-none">
      {Array.isArray(data) &&
        data?.length > 0 &&
        data?.map((menu, index) => {
          const isACtive = menu?.label === current?.label;
          return (
            <li
              role="button"
              key={index}
              className={classNames(
                "flex items-center justify-between text-sm px-3 py-2 hover:bg-zinc-200 hover:bg-opacity-75",
                isACtive && "bg-zinc-200 bg-opacity-75"
              )}
              onClick={() => {
                if (setCurrent) {
                  setCurrent(menu?.label);
                }
                // This will trigger the close
                onClose();
              }}
              onMouseEnter={() => {
                if (subMenu) {
                  if (setSubCurrent) {
                    setSubCurrent(menu);
                  }
                } else {
                  setCurrent(menu);
                }
              }}
            >
              <span
                title={menu?.label}
                className="whitespace-nowrap overflow-hidden text-ellipsis"
              >
                {menu?.label}
              </span>
              <MdChevronRight />
            </li>
          );
        })}
    </ul>
  </div>
);

const GridBox = ({ data, onClose }: { data: any; onClose: () => void }) => (
  <div className="flex items-center flex-wrap gap-5">
    {Array.isArray(data) &&
      data?.length > 0 &&
      data?.map((menu, index) => {
        return (
          <Link
            href="/companies"
            key={index}
            onClick={onClose}
            className={classNames(
              "flex items-center flex-col gap-3 text-sm p-2 hover:bg-zinc-200 hover:bg-opacity-75"
            )}
          >
            <img
              src={menu?.icon}
              alt="menu icon"
              className="w-10 h-10 object-cover"
            />
            <span className="text-xs">{menu?.label}</span>
          </Link>
        );
      })}
  </div>
);

export default NavigationDropdown;
