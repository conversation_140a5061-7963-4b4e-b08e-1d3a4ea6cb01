'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { Stepper } from "@/components/ui/stepper";
import {
  businessDetails,
  currentFormStep,
  sub_sub_categories,
} from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/slice";
import {
  updateBusinessDetails,
  getBusinessDetails,
  getSubSubCategories,
} from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/thunk";
import { RootDispatch } from "@/redux/store";
import { AlertCircle } from "lucide-react";
import { Fragment, lazy, ReactNode, Suspense, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, usePathname, useRouter } from "next/navigation";

const NavigateTo = ({ path }: { path: string }) => {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!pathname?.includes(path)) {
      router.push(path);
    }
  }, [router, path, pathname]);

  return <div className="w-full min-h-[80vh] bg-white" />;
};

interface StepComponentProps {
  onValidationChange?: (isValid: boolean) => void;
  isSubmitted?: boolean;
}

export const businessFormStepData: {
  title: string;
  component: React.LazyExoticComponent<React.ComponentType<StepComponentProps>>;
}[] = [
    {
      title: "Basic Info",
      component: lazy(() => import("./info")),
    },
    {
      title: "Contact Info",
      component: lazy(() => import("./contact")),
    },
    {
      title: "Requirement",
      component: lazy(() => import("./requirement")),
    },
    {
      title: "Source Details",
      component: lazy(() => import("./source")),
    },
    {
      title: "Verification",
      component: lazy(() => import("./verification")),
    },
  ];

const AddBusiness = () => {
  const dispatch = useDispatch<RootDispatch>();

  const currentStep = useSelector(currentFormStep) || 0;
  const businessDetailsState = useSelector(businessDetails);
  const subSubCategoriesState = useSelector(sub_sub_categories);

  const values = businessDetailsState?.data || null;
  const meta = businessDetailsState?.meta || {};
  const loading = businessDetailsState?.loading || false;
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isCurrentStepValid, setIsCurrentStepValid] = useState(false);
  const businessDataFetchedRef = useRef(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const { business_id } = useParams();
  const pathname = usePathname();

  useEffect(() => {
    const fetchInitialData = async () => {
      if (businessDataFetchedRef.current) return;

      try {
        await dispatch(getSubSubCategories()).unwrap();
        const response = await dispatch(getBusinessDetails()).unwrap();
        businessDataFetchedRef.current = true;

        if (response?.data) {
          setIsSubmitted(true);
        }
      } catch (error) {
        console.error('Failed to fetch initial data:', error);
        businessDataFetchedRef.current = true;
      }
    };

    fetchInitialData();
  }, [dispatch]);

  useEffect(() => {
    dispatch(Actions.setCurrentFormStep(0));
  }, [dispatch]);

  useEffect(() => {

    if (values) {
      console.log('Current business_address:', values.business_address);
      console.log('Current industry_category:', values.industry_category);
    }
  }, [values]);

  // Fetch dynamic fields when a category is selected
  useEffect(() => {
    if (values?.industry_category && Array.isArray(values.industry_category) && values.industry_category.length > 0) {
      const categoryId = values.industry_category[0]._id || values.industry_category[0];
    }
  }, [values?.industry_category, dispatch]);


  const handleSkip = (step: number) => {
    try {
      if (step < businessFormStepData?.length - 1) {
        dispatch(Actions.setCurrentFormStep(step + 1));
      }
      handleNavigate(step);
    } catch (error) {
      console.error(error);
    }
  };

  const handleSave = (step: number) => {
    try {
      dispatch(Actions.logState());
      setIsUpdating(true);

      const isLastStep = step === businessFormStepData.length - 1;

      if (values) {
        const formData = new FormData();

        //  Handle industry_category
        if (values.industry_category && Array.isArray(values.industry_category)) {
          const industryCategories = values.industry_category.map((cat: any) => {
            if (typeof cat === 'object' && cat._id) {
              return {
                _id: cat._id,
                name: cat.name || cat.sub_sub_category_name,
                sub_category_id: cat.sub_category_id,
              };
            } else if (typeof cat === 'string') {
              const fullCategory = subSubCategoriesState?.data?.find((c: any) => c._id === cat);
              return {
                _id: cat,
                name: fullCategory?.sub_sub_category_name || fullCategory?.name || cat,
                sub_category_id: fullCategory?.sub_category_id,
              };
            }
            return cat;
          });
          formData.append('industry_category', JSON.stringify(industryCategories));
        }

        //  Handle product_categories - this block is newly added
        if (values.product_categories && Array.isArray(values.product_categories)) {
          const productCategoryIds = values.product_categories
            .map((cat: any) => {
              if (typeof cat === 'object' && cat._id) {
                return cat._id;
              }

              if (typeof cat === 'string' && /^[a-f0-9]{24}$/.test(cat)) {
                return cat;
              }

              const matched = subSubCategoriesState?.data?.find((c: any) =>
                c.sub_sub_category_name === cat || c.name === cat
              );
              if (matched?._id) {
                return matched._id;
              }

              return null;
            })
            .filter((id: any) => typeof id === 'string');

          productCategoryIds.forEach((id: string) => {
            formData.append('product_categories', id);
          });

        } else {
          console.log(' product_categories missing or not array:', values.product_categories);
        }

        //  Handle shipping_terms explicitly
        if (values.shipping_terms && Array.isArray(values.shipping_terms)) {

          const validShippingTerms = values.shipping_terms.filter((term: any) =>
            term && term !== 'na' && typeof term === 'string' && term.trim() !== ''
          );

          if (validShippingTerms.length > 0) {

            validShippingTerms.forEach((term: string) => {
              formData.append('shipping_terms', term);
            });

          }
        }

        //  Handle payment_methods explicitly  
        if (values.payment_methods && Array.isArray(values.payment_methods)) {

          const validPaymentMethods = values.payment_methods.filter((method: any) =>
            method && method !== 'na' && typeof method === 'string' && method.trim() !== ''
          );


          if (validPaymentMethods.length > 0) {
            validPaymentMethods.forEach((method: string) => {
              formData.append('payment_methods', method);
            });
          }
        }

        if (values.business_type && Array.isArray(values.business_type)) {

          const validBusinessTypes = values.business_type.filter((type: any) =>
            type && type !== 'na' && typeof type === 'string' && type.trim() !== ''
          );

          if (validBusinessTypes.length > 0) {
            validBusinessTypes.forEach((type: string) => {
              formData.append('business_type', type);
            });
          }
        }

        if (values.delete_business_logo?.length) {
          values.delete_business_logo.forEach((mediaId: string) => {
            formData.append('delete_business_logo', mediaId);
          });
        }
        // 🔁 Handle other fields
        Object.entries(values).forEach(([key, value]: [string, any]) => {
          if (['industry_category', 'product_categories', 'shipping_terms', 'payment_methods', 'business_type', 'delete_business_logo'].includes(key)) return;
          if (value === null || value === undefined) return;

          if (value instanceof File) {
            formData.append(key, value);
            return;
          }

          if (Array.isArray(value)) {
            value.forEach((item) => {
              if (item instanceof File) {
                formData.append(key, item);
              } else if (item?.file instanceof File) {
                formData.append(key, item.file);
              }
            });
            return;
          }

          if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, String(value));
          }
        });

        dispatch(updateBusinessDetails(formData))
          .unwrap()
          .then((response) => {
            console.log('API Response:', response);

            dispatch(getBusinessDetails())
              .unwrap()
              .then((freshData) => {
                console.log('Fresh data after save:', freshData);
                setIsUpdating(false);

                if (!isLastStep) {
                  dispatch(Actions.setCurrentFormStep(step + 1));
                  handleNavigate(step);
                }
              })
              .catch((fetchError) => {
                console.error('Error fetching fresh data:', fetchError);
                setIsUpdating(false);

                if (!isLastStep) {
                  dispatch(Actions.setCurrentFormStep(step + 1));
                  handleNavigate(step);
                }
              });
          })
          .catch((error) => {
            console.error("API submission error:", error);
            setIsUpdating(false);
          });
      } else {
        setIsUpdating(false);
        if (!isLastStep) {
          dispatch(Actions.setCurrentFormStep(step + 1));
          handleNavigate(step);
        }
      }
    } catch (error) {
      console.error(error);
      setIsUpdating(false);
    }
  };


  const handleShift = (step: number) => {
    try {
      dispatch(Actions.setCurrentFormStep(step));
      handleNavigate(step);
    } catch (error) {
      console.error(error);
    }
  };

  const handleNavigate = (step: number) => {
    // Add navigation logic if needed
    console.log('Navigation after step:', step);
  };

  const isLastStep = currentStep === businessFormStepData.length - 1;

  // Show loader while fetching initial business data
  if (!businessDataFetchedRef.current && loading) {
    return (
      <div className="flex items-center justify-center p-10">
        <Loader big center />
      </div>
    );
  }

  if (business_id && !loading && !values) {
    return (
      <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
        <AlertCircle /> {meta?.message || "Business not found"}
      </div>
    );
  }

  return (
    <main className="bg-white rounded-lg md:shadow-lg md:p-4 w-full h-full">
      <Stepper
        data={businessFormStepData}
        active={currentStep}
        onSelect={(step) => handleShift(step)}
        className="mb-7 md:mb-10"
      />

      {businessFormStepData?.map((step, index) => {
        if (currentStep !== index) return null;

        return (
          <Fragment key={index}>
            {"element" in step ? (
              // Type safe check for element property
              <>{step.element as ReactNode}</>
            ) : (
              <Suspense fallback={<Loader big center />}>
                <div className="flex flex-col gap-9 [&>*]:w-full">
                  <step.component
                    onValidationChange={setIsCurrentStepValid}
                    isSubmitted={isSubmitted}
                  />
                </div>
              </Suspense>
            )}
          </Fragment>
        );
      })}

      <div className="flex items-center justify-end gap-3 mt-7 md:mt-10">
        {/* Only show Skip button on non-final steps */}
        {!isLastStep && (
          <Button
            className="rounded-md"
            variant="destructive"
            onClick={() => handleSkip(currentStep)}
          >
            Skip
          </Button>
        )}

        <Button
          className="rounded-md"
          variant="main-revert"
          onClick={() => handleSave(currentStep)}
          disabled={loading || isUpdating}
        >
          {isLastStep ? 'Submit' : 'Save & Next'}
          {(loading || isUpdating) && <span className="ml-2"><Loader small /></span>}
        </Button>
      </div>
    </main>
  );
};

export default AddBusiness;