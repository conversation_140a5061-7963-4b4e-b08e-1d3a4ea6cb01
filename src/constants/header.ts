export const staticPageUrl = (path: string): string =>
  `${process.env.NEXT_PUBLIC_STATIC_PAGES_BASE_URL}/${path}`;

export const resources = [
  {
    title: "Industry News & Insights",
    path: staticPageUrl(""),
  },
  {
    title: "Tips for sellers",
    path: staticPageUrl("tips-for-sellers"),
  },
  {
    title: "Guides for buyers",
    path: staticPageUrl("tips-for-buyers"),
  },
  {
    title: "Articles",
    path: staticPageUrl("articles"),
  },
  {
    title: "Blog",
    path: staticPageUrl("blog"),
  },
];

export const helpCenter = [
  {
    title: "FAQs",
    path: staticPageUrl("frequently-asked-questions"),
  },
  {
    title: "User Guides",
    path: staticPageUrl("tips-for-buyers"),
  },
  {
    title: "How to Use the Platform",
    path: staticPageUrl(""),
  },
  {
    title: "Feedback",
    path: staticPageUrl("feedback"),
  },
  {
    title: "Complaints",
    path: staticPageUrl("feedback"),
  },
];

export const navigation = [
  {
    title: "Discover",
  },
];

export const navigationCategory = [
  {
    title: "Agriculture",
  },
  {
    title: "Manufacturing",
  },
  {
    title: "Construction & Real Estate",
  },
  {
    title: "IT & Technology",
  },
  {
    title: "Retail & Wholesale",
  },
  {
    title: "Transport & Logistics",
  },
  {
    title: "Healthcare & Pharmaceuticals",
  },
  {
    title: "Energy & Environment",
  },
];

export const navigationSubCategory = [
  {
    title: "Crops & Farming",
  },
  {
    title: "Livestock & Poultry",
  },
  {
    title: "Agricultural Machinery",
  },
];

export const navigationMenu = [
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Feed Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Fiber Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Fuel Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Rabi Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Zaid Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Cash Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Food Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Tuber Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Pulses",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Aquatic Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Nut Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Fallow Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    title: "Extra Crops",
  },
];

export const navigationData = navigation?.map((itemMain) => ({
  ...itemMain,
  menus: navigationCategory?.map((itemPar, index) => ({
    ...itemPar,
    menus: navigationSubCategory?.map((itemSub, indexSub) => ({
      ...itemSub,
      title: itemSub?.title + " " + index,
      menus: navigationMenu?.map((itemNav) => ({
        ...itemNav,
        title: itemNav?.title + " " + indexSub,
      })),
    })),
  })),
}));
