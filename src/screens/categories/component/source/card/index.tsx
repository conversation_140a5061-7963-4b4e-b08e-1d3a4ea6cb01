"use client";

import { FC } from "react";

interface CategoryCardProps {
  image: string;
  label: string;
  onClick?: () => void;
}

const CategoryCard: FC<CategoryCardProps> = ({ image, label, onClick }) => {
  return (
    <div className="flex flex-col items-center cursor-pointer max-w-28" onClick={onClick}>
<div className="bg-gray-50 rounded-lg overflow-hidden w-full aspect-[1/0.85] flex items-center justify-center mb-1 shadow-sm border border-gray-100">
<img 
          src={image} 
          alt={label}
          className="w-full h-full object-cover"
        />
      </div>
      <span className="text-xs text-center font-medium text-gray-800">{label}</span>
    </div>
  );
};

export default CategoryCard;
