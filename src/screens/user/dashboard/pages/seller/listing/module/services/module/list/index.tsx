"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import { Loader } from "@/components/ui/loader";
import {
  serviceDetails,
  servicesList,
} from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/selectors";
import {
  deleteService,
  updateServiceDetails,
} from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/thunk";
import { RootDispatch } from "@/redux/store";
import { AlertCircle } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";

interface ServiceListProps {
  services: any[];
}

const ServiceList: React.FC<ServiceListProps> = ({ services = [] }) => {
  const router = useRouter();
  const { loading } = useSelector(servicesList);
  const { data: serviceData, loading: serviceUpdateLoading } =
    useSelector(serviceDetails);

  const dispatch = useDispatch<RootDispatch>();

  const handleEdit = (service_id: string) => {
    router.push(`/dashboard/seller/listing/services/${service_id}`);
  };

  const handleDelete = (service_id: string) => {
    dispatch(deleteService(service_id));
  };

  const handleStatusUpdate = (status: string, service_id: string) => {
    const data = new FormData();
    data.append("service_status", status);

    dispatch(
      updateServiceDetails({
        service_id,
        data,
      })
    );
  };

  if (loading) {
    return <Loader big center />;
  }

  return (
    <div className="space-y-4">
      {Array.isArray(services) && services?.length > 0 ? (
        <>
          <ListCardHeader />
          {services.map((service, index) => {
            if (
              serviceData &&
              serviceData?._id === service?._id &&
              serviceUpdateLoading
            ) {
              return <Loader key={index} big center />;
            }
            return (
              <ListCard
                key={service._id}
                image={service?.main_service_images?.[0]}
                title={service?.service_name}
                category={service?.category?.[0]?.name ?? "Category"}
                views={"345"}
                status={service?.service_status}
                onEdit={() => handleEdit(service._id)}
                onDelete={() => handleDelete(service._id)}
                onStatusChange={(status) =>
                  handleStatusUpdate(status, service._id)
                }
                statusOptions={[
                  { label: "Active", value: true },
                  { label: "Inactive", value: false },
                ]}
              />
            );
          })}
        </>
      ) : (
        <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
          <AlertCircle /> No Services Available
        </div>
      )}
    </div>
  );
};

export default ServiceList;
