import RATING_STAR from "/assets/pages/rating-star.svg";
import { Progress } from "@/components/ui/progress";
import { SquareProgress } from "@/components/ui/progress/square";

const Ratings = () => {
  return (
    <div className="flex flex-wrap items-center gap-6">
      <div className="flex-1 flex flex-col items-center gap-3 min-w-24">
        <div className="flex items-center gap-2">
          <h5 className="text-4xl font-normal">4.5</h5>
          <img
            src={RATING_STAR}
            alt="flag"
            className="aspect-auto w-7 h-7 object-contain"
          />
        </div>
        <span className="text-center text-xs sm:text-sm">25,116 Reviews</span>
      </div>

      <div className="flex-[2] flex flex-col items-start gap-2 [&>*]:w-full">
        {Array.from({ length: 5 })
          .map((_, index) => {
            return (
              <div key={index} className="flex items-center gap-5">
                <div className="flex items-center gap-1">
                  <span className="font-semibold min-w-2.5">{index + 1}</span>
                  <img
                    src={RATING_STAR}
                    alt="flag"
                    className="aspect-auto w-3.5 h-3.5 object-contain"
                  />
                </div>
                <Progress
                  value={(index + 1) * 10}
                  className="w-full min-w-28"
                  progressBg={
                    index < 1
                      ? "#F7474F"
                      : index < 2
                      ? "#FA8B18"
                      : index > 2
                      ? "#347E29"
                      : "#347E29"
                  }
                />
              </div>
            );
          })
          ?.reverse()}
      </div>

      <div className="flex-[3] grid items-center flex-wrap grid-cols-2 md:grid-cols-4 gap-3 min-w-52 ">
        <div className="flex-1 flex items-center flex-col gap-2">
          <SquareProgress value={65} count="3.8" />
          <span className="text-xs font-semibold text-center">
            Supplier service
          </span>
        </div>

        <div className="flex-1 flex items-center flex-col gap-2">
          <SquareProgress value={85} count="3.8" />
          <span className="text-xs font-semibold text-center">
            On-time shipment
          </span>
        </div>

        <div className="flex-1 flex items-center flex-col gap-2">
          <SquareProgress value={55} count="3.8" />
          <span className="text-xs font-semibold text-center">
            Product quality
          </span>
        </div>

        <div className="flex-1 flex items-center flex-col gap-2">
          <SquareProgress value={35} count="3.8" />

          <span className="text-xs font-semibold text-center">
            Supplier service
          </span>
        </div>
      </div>
    </div>
  );
};

export default Ratings;
