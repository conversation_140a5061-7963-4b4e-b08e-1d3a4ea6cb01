"use client";

import {
  Bread<PERSON>rumb,
  Bread<PERSON><PERSON>b<PERSON><PERSON>,
  Bread<PERSON><PERSON>bLink,
  Breadcrumb<PERSON>ist,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { isIdentificationNumber } from "@/lib/utils/data";
import classNames from "classnames";
import { usePathname, useRouter } from "next/navigation";
import React, { Fragment } from "react";

interface BreadcrumbData {
  title: string;
  pathname: string;
}

interface BreadcrumbProps {
  className?: string;
  data?: BreadcrumbData[];
}

const BreadcrumbRender: React.FC<BreadcrumbProps> = ({ className, data }) => {
  const router = useRouter();
  const pathname = usePathname();

  // Helper to generate breadcrumbs from pathname if no data is provided
  const generateBreadcrumbsFromPath = (): BreadcrumbData[] => {
    const segments = pathname.split("/").filter(Boolean);
    return segments
      .filter((segment) => !isIdentificationNumber(segment))
      .map((_, index) => {
        const path = "/" + segments.slice(0, index + 1).join("/");
        return {
          title: segments[index].replace(/-/g, " "),
          pathname: path,
        };
      });
  };

  const breadcrumbs =
    data && data.length > 0 ? data : generateBreadcrumbsFromPath();

  return (
    <Breadcrumb className={classNames(className)}>
      <BreadcrumbList>
        {breadcrumbs.map((item, index) => (
          <Fragment key={item.pathname}>
            <BreadcrumbItem>
              <BreadcrumbLink
                asChild
                className={classNames(
                  "capitalize hover:text-main",
                  index === breadcrumbs.length - 1 && "text-main"
                )}
              >
                <button onClick={() => router.push(item.pathname)}>
                  {item.title}
                </button>
              </BreadcrumbLink>
            </BreadcrumbItem>
            {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
          </Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadcrumbRender;
