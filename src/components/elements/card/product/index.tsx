'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { FaRegHeart } from "react-icons/fa";
import { useRouter } from "next/navigation";
import { FC, useState } from "react";

interface ProductCardType {
  className?: string;
  compare?: boolean;
}

const ProductCard: FC<ProductCardType> = ({ className, compare }) => {
  const router = useRouter();
  const [isCompareChecked, setIsCompareChecked] = useState(compare || false);

  const handleCompareChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation(); // Prevent card click when clicking checkbox
    setIsCompareChecked(e.target.checked);
  };

  const handleCardClick = () => {
    router.push("/products/details");
  };

  const handleHeartClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card navigation
    // Add wishlist logic here
  };

  const handleGetQuoteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card navigation
    // Add quote logic here
  };

  return (
    <div
      role="button"
      onClick={handleCardClick}
      className={`bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden w-full hover:shadow-md transition-shadow cursor-pointer ${className || ''}`}
    >
      {/* Product Image */}
      <div className="relative w-full aspect-square">
        <img
          src="/assets/pages/buyer/building.jpg"
          alt="Apple HomePod"
          className="w-full h-full object-cover"
        />
        {/* Heart Button */}
        <button 
          onClick={handleHeartClick}
          className="absolute right-4 top-4 bg-white hover:bg-main hover:text-white transition-all h-8 w-8 rounded-full flex items-center justify-center shadow-sm"
        >
          <FaRegHeart size={16} className="mt-0.5" />
        </button>
      </div>

      {/* Product Info */}
      <div className="p-4 flex flex-col gap-2">
        {/* Product Title */}
        <h3 className="text-sm font-bold text-gray-900 line-clamp-2 leading-tight">
          Apple HomePod, 2nd Generation, Smart Speaker, Midnight
        </h3>

        {/* Ratings */}
        <div className="flex items-center gap-2 mt-1">
          <img
            src="/assets/pages/rating-star.svg"
            alt="rating star"
            className="aspect-auto w-4 h-4 object-contain"
          />
          <div className="flex items-center flex-wrap gap-2 text-xs font-semibold text-gray-700">
            <span className="border-r border-slate-300 pr-2">4.5/5</span>
            <span>150 Reviews</span>
          </div>
        </div>

        {/* Price */}
        <div className="flex items-center gap-2 mt-1">
          <span className="text-lg font-bold text-gray-900">$381.99</span>
          <span className="text-sm text-gray-400 line-through">$371.99</span>
        </div>

        {/* Seller Info */}
        <div className="flex items-center gap-2 mt-1">
          <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center text-white text-xs font-medium">
            P&G
          </div>
          <span className="text-xs font-medium text-gray-700">Procter & Gamble</span>
        </div>

        {/* Location */}
        <div className="flex items-center gap-1.5 mt-1">
          <img
            src="https://upload.wikimedia.org/wikipedia/en/thumb/4/41/Flag_of_India.svg/1200px-Flag_of_India.svg.png"
            alt="India flag"
            className="aspect-auto w-4 h-auto object-contain"
          />
          <span className="text-xs text-gray-600">India Mumbai</span>
        </div>

        {/* Minimum Order */}
        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs text-gray-700">Min Order:</span>
          <span className="text-xs text-gray-600 font-medium">500 Pieces</span>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 mt-3">
          {/* Get Quote Button */}
          <Button
            onClick={handleGetQuoteClick}
            variant="ghost-brown"
            className=" flex-1 h-8 rounded-full text-xs px-3 font-medium transition-colors"
          >
            Get Quote
          </Button>

          {/* Add to Compare Section */}
          <div 
            className="flex items-center gap-2 border border-gray-300 hover:border-gray-400 px-2 py-1.5 flex-1 h-8 rounded-full bg-white transition-colors"
            onClick={(e) => e.stopPropagation()}
          >
            <input
              type="checkbox"
              id="compare"
              checked={isCompareChecked}
              onChange={handleCompareChange}
              className="h-3 w-3 text-blue-600 border-gray-400 rounded focus:ring-blue-500 focus:ring-1 cursor-pointer"
            />
            <label
              htmlFor="compare"
              className="text-xs text-gray-700 whitespace-nowrap cursor-pointer select-none font-medium"
            >
              Add to Compare
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;