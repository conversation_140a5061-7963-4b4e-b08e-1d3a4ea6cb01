import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useRouter } from "next/navigation";
import { useState } from "react"; // Import useState for modal state
import ImageGallery from "../images";
import Response from '@/screens/user/dashboard/pages/buyer/manage/module/response/response-details';
import Detail from '@/screens/user/dashboard/pages/buyer/manage/module/response/response-details';

const ListCard = ({ company }: { company: (typeof ListingData)[0] }) => {
const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false); // State to control modal visibility

  // Function to open the modal
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  // Function to close the modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div className="flex-1 w-full h-max bg-white shadow-md hover:shadow-lg">
        <div className="flex flex-col gap-0">
          {/* Main Content */}
          <div className="flex flex-col sm:flex-row gap-6 px-4 sm:px-6 pt-6 pb-4">
            {/* Image Gallery */}
            <div className="w-full sm:w-max flex items-center sm:items-start justify-center sm:justify-between gap-4">
              <ImageGallery images={company?.images} />
            </div>

            {/* Content */}
            <div className="flex-1 flex flex-col items-start [&>*]:w-full">
              {/* Top Section */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4 mb-2">
                <div className="flex flex-col w-full sm:w-auto">
                  {/* Title */}
                  <h2
                    role="button"
                    className="text-xl font-bold hover:underline text-black hover:drop-shadow"
                    onClick={() => router.push("/company")}
                  >
                    {company?.name}
                  </h2>

                  {/* Location */}
                  <div className="flex items-center gap-1.5 mt-1">
                    <img
                      src={company?.location?.flag}
                      alt="flag"
                      className="aspect-auto max-w-5 w-5 h-auto object-contain"
                    />
                    <div className="flex items-center flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700">
                      <span>{company?.location?.address}, </span>
                      <span>{company?.location?.label}</span>
                    </div>
                  </div>
                </div>

                {/* Price - Right Aligned */}
                <div className="flex flex-col items-start sm:items-end mt-2 sm:mt-0">
                  <span className="font-bold text-[#070707] text-2xl">
                    ${company?.offerPrice}
                  </span>
                  <span className="text-sm text-stone-600">Fixed Price</span>
                </div>
              </div>

              {/* Verified and Sponsored */}
              <div className="flex items-center gap-2 mb-2">
                {company?.verified && (
                  <div className="flex items-center gap-2 flex-wrap">
                    <div className="bg-[#9F9795] text-white px-2 py-1 flex items-center justify-start gap-1.5 w-max rounded-tl-sm rounded-bl-sm rounded-tr-md rounded-br-md">
                      <img
                        src={"/assets/pages/verified-white.svg"}
                        alt="verified"
                        className="aspect-auto max-w-3 w-3 h-auto object-contain"
                      />
                      <span className="text-[0.6rem] font-medium">Verified</span>
                    </div>
                    <span className="text-xs text-stone-600">Sponsored</span>
                  </div>
                )}
              </div>

              {/* Rating */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 mb-3">
                <div className="flex items-center">
                  <img
                    src={"/assets/pages/rating-star.svg"}
                    alt="rating star"
                    className="aspect-auto w-5 h-5 object-contain mr-2"
                  />
                  <span className="text-base font-semibold text-font leading-4 border-r border-slate-300 pr-2.5">
                    {company?.rating?.stars}/5
                  </span>
                  <span className="text-base font-semibold text-font leading-4 ml-2.5">
                    {company?.rating?.reviewsCount} Reviews
                  </span>
                </div>

                {/* Add to Compare - Right Aligned */}
                <div className="ml-0 sm:ml-auto mt-2 sm:mt-0">
                  <Checkbox
                    label="Add to Compare"
                    labelClassName="whitespace-nowrap"
                  />
                </div>
              </div>

              {/* Description */}
              <div className="mb-4">
                <p
                  title={company?.description}
                  className="text-[0.92rem] text-stone-700 line-clamp-2"
                >
                  {company?.description}
                </p>
              </div>

              {/* View Response - Modified to open modal */}
              <div className="mb-2">
                <button
                  onClick={handleOpenModal}
                  className="text-orange-500 hover:underline font-medium"
                >
                  View Response
                </button>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between border-t border-slate-200 px-4 sm:px-6 py-4 gap-4">
            {/* Company Stats */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-8 w-full sm:w-auto">
              <p className="text-sm">
                <span className="font-medium">{company?.strengthRange}</span> Employees
              </p>
              <p className="text-sm">
                <span className="font-medium">{company?.businessAge}</span> Years in business
              </p>
              <p className="text-sm">
                <span className="font-medium">Min. order value: </span>
                <span>({company?.minOrderQuantity} USD)</span>
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap sm:flex-nowrap">
              <Button variant="outline" size="sm" className="font-medium text-xs sm:text-sm">
                Make Offer
              </Button>
              <Button variant="outline" size="sm" className="font-medium text-xs sm:text-sm">
                Accept
              </Button>
              <Button variant="outline" size="sm" className="font-medium  text-xs sm:text-sm">
                Reject
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal for Detail Component */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-11/12 max-w-4xl max-h-[90vh] overflow-auto relative">
            {/* Close button */}
            <button
              onClick={handleCloseModal}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Modal content - Detail component */}
            <div className="mt-2">
              <Detail  />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ListCard;