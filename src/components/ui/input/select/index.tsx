"use client";

import { SelectProps } from "@radix-ui/react-select";
import classNames from "classnames";
import { forwardRef, memo, useEffect, useId, useState } from "react";
import InputLabel from "../components/label";
import { InputWrapper, wrapperStyle } from "../components/wrapper";
import {
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Select as ShadSelect,
} from "./core";

export type InputTargetType = {
  target: {
    value: string;
    name: string;
  };
};

export type FinalTextType = {
  title: string;
  value: string;
};

interface OptionType {
  label: string;
  value: string;
  icon?: string;
  [key: string]: any;
}

export interface SelectType extends SelectProps {
  icon?: React.ReactNode | string;
  onChange?: (e: InputTargetType) => void;
  containerClassName?: string;
  labelClassName?: string;
  wrapperClassName?: string;
  id?: string;
  label?: string;
  className?: string;
  placeholder?: string;
  dialCode?: boolean;
  comboInput?: boolean;
  options: OptionType[];
  optionKeys?: {
    label?: string;
    value?: string;
    icon?: string;
  };
}

const Select = memo(
  forwardRef<any, SelectType>(
    (
      {
        className,
        value = "",
        name,
        containerClassName,
        label,
        id,
        labelClassName,
        onChange,
        icon,
        wrapperClassName,
        placeholder,
        options,
        optionKeys,
        ...rest
      },
      ref
    ) => {
      const uniqueId = useId();

      const [optionListCount, setOptionListCount] = useState<number>(20);

      useEffect(() => {
        return () => {
          setOptionListCount(20);
        };
      }, []);

      const handleChange = (val: string) => {
        try {
          if (onChange) {
            onChange({
              target: {
                value: val,
                name: name ?? "select",
              },
            });
          }
        } catch (error) {
          console.error(error);
        }
      };

      const getLabel = (option: OptionType) => {
        try {
          if (optionKeys && optionKeys?.label) {
            return option?.[optionKeys?.label];
          }

          return option?.label;
        } catch {
          return option?.label;
        }
      };

      const getValue = (option: OptionType) => {
        try {
          if (optionKeys && optionKeys?.value) {
            return option?.[optionKeys?.value];
          }

          return option?.value;
        } catch {
          return option?.value;
        }
      };

      const getIcon = (option: OptionType) => {
        try {
          if (optionKeys && optionKeys?.icon) {
            return option?.[optionKeys?.icon];
          }

          return option?.icon;
        } catch {
          return option?.icon;
        }
      };

      return (
        <ShadSelect
          onValueChange={handleChange}
          value={value}
          name={name}
          {...rest}
        >
          <div
            className={classNames(
              "flex flex-col gap-1.5 items-center [&>*]:w-full",
              containerClassName
            )}
          >
            {label && (
              <InputLabel htmlFor={id ?? uniqueId} className={labelClassName}>
                {label}
              </InputLabel>
            )}
            <SelectTrigger
              className={classNames(
                "w-full h-full !shadow-none !ring-0",
                wrapperStyle(),
                className
              )}
            >
              <InputWrapper
                icon={icon}
                className={classNames(
                  "w-full border-none !h-max !p-0",
                  wrapperClassName
                )}
              >
                <SelectValue
                  title={String(value)}
                  placeholder={placeholder ?? "Select"}
                  className="!text-clip"
                />
              </InputWrapper>
            </SelectTrigger>
          </div>

          <SelectContent
            className="bg-white w-full max-w-64 overflow-y-auto"
            onScroll={() => setOptionListCount((prev) => prev + 15)}
          >
            <SelectGroup className="w-full">
              {Array.isArray(options) &&
                options?.length > 0 &&
                options?.slice(0, optionListCount)?.map((option, index) => {
                  return (
                    <SelectItem
                      key={index}
                      title={getLabel(option)}
                      value={getValue(option)}
                      className="w-full cursor-pointer hover:bg-slate-100"
                    >
                      <div className="flex items-center gap-1 w-full">
                        {getIcon(option) && (
                          <img
                            src={getIcon(option)}
                            alt="flag"
                            className="max-w-5 w-5 h-auto object-contain"
                          />
                        )}

                        <span className="w-full whitespace-nowrap text-ellipsis overflow-hidden ">
                          {getLabel(option)}
                        </span>
                      </div>
                    </SelectItem>
                  );
                })}
            </SelectGroup>
          </SelectContent>
        </ShadSelect>
      );
    }
  )
);
Select.displayName = "Select";

export { Select };
