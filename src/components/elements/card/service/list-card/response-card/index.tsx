import RATING_STAR from "/assets/pages/rating-star.svg";
import VERIFIES_GRAY from "/assets/pages/verified-gray.svg";
import VERIFIES_WHITE from "/assets/pages/verified-white.svg";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useRouter } from "next/navigation";
import ImageGallery from "../images";
import Response from '@/screens/user/dashboard/pages/buyer/manage/module/response/response-details'
const ListCard = ({ company }: { company: (typeof ListingData)[0] }) => {
const router = useRouter();

  return (
    <div className="flex-1 w-full h-max bg-white shadow-md hover:shadow-lg">
      <div className="flex flex-col gap-0">
        {/* Main Content */}
        <div className="flex flex-col sm:flex-row gap-6 px-4 sm:px-6 pt-6 pb-4">
          {/* Image Gallery */}
          <div className="w-full sm:w-max flex items-center sm:items-start justify-center sm:justify-between gap-4">
            <ImageGallery images={company?.images} />
          </div>

          {/* Content */}
          <div className="flex-1 flex flex-col items-start [&>*]:w-full">
            {/* Top Section */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4 mb-2">
              <div className="flex flex-col w-full sm:w-auto">
                {/* Title */}
                <h2
                  role="button"
                  className="text-xl font-bold hover:underline text-black hover:drop-shadow"
                  onClick={() => router.push("/company")}
                >
                  {company?.name}
                </h2>
                
                {/* Location */}
                <div className="flex items-center gap-1.5 mt-1">
                  <img
                    src={company?.location?.flag}
                    alt="flag"
                    className="aspect-auto max-w-5 w-5 h-auto object-contain"
                  />
                  <div className="flex items-center flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700">
                    <span>{company?.location?.address}, </span>
                    <span>{company?.location?.label}</span>
                  </div>
                </div>
              </div>
              
              {/* Price - Right Aligned */}
              <div className="flex flex-col items-start sm:items-end mt-2 sm:mt-0">
                <span className="font-bold text-[#070707] text-2xl">
                  ${company?.offerPrice}
                </span>
                <span className="text-sm text-stone-600">Fixed Price</span>
              </div>
            </div>
            
            {/* Verified and Sponsored */}
            <div className="flex items-center gap-2 mb-2">
              {company?.verified && (
                <div className="flex items-center gap-2 flex-wrap">
                  <div className="bg-[#9F9795] text-white px-2 py-1 flex items-center justify-start gap-1.5 w-max rounded-tl-sm rounded-bl-sm rounded-tr-md rounded-br-md">
                    <img
                      src={VERIFIES_WHITE}
                      alt="verified"
                      className="aspect-auto max-w-3 w-3 h-auto object-contain"
                    />
                    <span className="text-[0.6rem] font-medium">Verified</span>
                  </div>
                  <span className="text-xs text-stone-600">Sponsored</span>
                </div>
              )}
            </div>
            
            {/* Rating */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 mb-3">
              <div className="flex items-center">
                <img
                  src={RATING_STAR}
                  alt="rating star"
                  className="aspect-auto w-5 h-5 object-contain mr-2"
                />
                <span className="text-base font-semibold text-font leading-4 border-r border-slate-300 pr-2.5">
                  {company?.rating?.stars}/5
                </span>
                <span className="text-base font-semibold text-font leading-4 ml-2.5">
                  {company?.rating?.reviewsCount} Reviews
                </span>
              </div>
              
              {/* Add to Compare - Right Aligned */}
              <div className="ml-0 sm:ml-auto mt-2 sm:mt-0">
                <Checkbox
                  label="Add to Compare"
                  labelClassName="whitespace-nowrap"
                />
              </div>
            </div>
            
            {/* Description */}
            <div className="mb-4">
              <p
                title={company?.description}
                className="text-[0.92rem] text-stone-700 line-clamp-2"
              >
                {company?.description}
              </p>
            </div>
            
            {/* View Response */}
            <div className="mb-2">
              <a href="#" className="text-orange-500 hover:underline font-medium">
                View Response
              </a>
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between border-t border-slate-200 px-4 sm:px-6 py-4 gap-4">
          {/* Company Stats */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-8 w-full sm:w-auto">
            <p className="text-sm">
              <span className="font-medium">{company?.strengthRange}</span> Employees
            </p>
            <p className="text-sm">
              <span className="font-medium">{company?.businessAge}</span> Years in business
            </p>
            <p className="text-sm">
              <span className="font-medium">Min. order value: </span>
              <span>({company?.minOrderQuantity} USD)</span>
            </p>
          </div>
          
          {/* Action Buttons */}
          <div className="flex items-center gap-2 sm:gap-3 flex-wrap sm:flex-nowrap">
            <Button variant="outline" size="sm" className="font-medium text-xs sm:text-sm">
              Make Offer
            </Button>
            <Button variant="outline" size="sm" className="font-medium text-xs sm:text-sm">
              Accept
            </Button>
            <Button variant="outline" size="sm" className="font-medium  text-xs sm:text-sm">
              Reject
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListCard;