import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { FaStar } from "react-icons/fa";

const CommentCard = () => {
  const ratingCOunt = 3.6;
  return (
    <div className="flex flex-col [&>*]:w-full bg-white rounded-md shadow-md">
      <div className="flex flex-col items-start gap-4 [&>*]:w-full px-6 pt-5 pb-2">
        <div className="flex items-center flex-wrap gap-5">
          <div
            className="rounded-md px-2 py-1 flex items-center gap-1 text-white text-sm"
            style={{
              background:
                ratingCOunt < 2
                  ? "#F7474F"
                  : ratingCOunt < 3
                  ? "#FA8B18"
                  : ratingCOunt > 3
                  ? "#327928"
                  : "#327928",
            }}
          >
            <span className="font-bold">{ratingCOunt} </span>
            <FaStar size={15} />
          </div>

          <div className="flex items-center gap-2.5">
            <img
              src="https://upload.wikimedia.org/wikipedia/en/thumb/4/41/Flag_of_India.svg/1200px-Flag_of_India.svg.png"
              alt="flag"
              className="flex-1 aspect-auto max-w-6 w-6 h-auto object-contain"
            />
            <span className="font-normal text-sm">MD JAMAL</span>
          </div>

          <span className="text-stone-600 text-sm">Oct 16, 2023</span>
        </div>
        <p className="text-sm">
          The supplier is really good and responsive, love dealing with Alice.
          She’s detailed and will double check with me before printing. Just
          that it took some time to receive products
        </p>

        <div className="relative">
          <Carousel
            opts={{
              align: "start",
            }}
            className="w-full static"
          >
            <CarouselContent className="-ml-3">
              {Array.from({ length: 3 }).map((_, index) => (
                <CarouselItem
                  key={index}
                  className="pl-3 basis-1/3 sm:basis-1/4 md:basis-1/5 lg:basis-1/6 xl:basis-[10%]"
                >
                  <div className="rounded-md shadow aspect-square overflow-hidden">
                    <img
                      src="https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg"
                      alt="review img"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
      <div className="flex items-center gap-3 px-6 py-3 border-t border-stone-200 text-xs">
        <div className="flex items-center gap-0.5">
          <span>Color</span>
          <span>:</span>
          <span>Pink</span>
        </div>
        <div className="flex items-center gap-0.5">
          <span>Capacity</span>
          <span>:</span>
          <span>0.5L</span>
        </div>
      </div>
    </div>
  );
};

export default CommentCard;
