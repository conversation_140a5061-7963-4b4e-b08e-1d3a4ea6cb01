import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import { createSlice, PayloadAction, AsyncThunk } from "@reduxjs/toolkit";
import { getBusinessDetails, updateBusinessDetails, getSubSubCategories, companyEmailVerifyInitiate, companyEmailVerify } from "./thunk";

interface APTStateType {
  businessDetails: APIResponseType;
  sub_sub_categories: APIResponseType;
  emailVerificationInitiate: APIResponseType;
  emailVerify: APIResponseType;
}

const APIState: APTStateType = {
  businessDetails: {
    data: null,
    loading: false,
    meta: null,
  },
  sub_sub_categories: {
    data: null,
    loading: false,
    meta: null,
  },
  emailVerificationInitiate: {
    data: null,
    loading: false,
    meta: null,
  },
  emailVerify: {
    data: null,
    loading: false,
    meta: null,
  },
};

interface stateType extends APTStateType {
  getBusinessDetailsSelector: any;
  currentFormStep: number;
  formEdits: {
    [key: string]: any;
  };
}

const initialState: stateType = {
  ...APIState,
  currentFormStep: 0,
  formEdits: {},
  getBusinessDetailsSelector: undefined
};

export const slice = createSlice({
  name: "user_buyer_business",
  initialState,
  reducers: {
    setCurrentFormStep: (state, action: PayloadAction<number>) => {
      state.currentFormStep = action.payload;
    },
    setFormValues: (state, action: PayloadAction<any | null>) => {
      if (action.payload) {
        if (!state.businessDetails.data) {
          state.businessDetails.data = {};
        }

        state.formEdits = {
          ...state.formEdits,
          ...action.payload,
        };

        state.businessDetails.data = {
          ...state.businessDetails.data,
          ...state.formEdits,
        };

        console.log('Redux: Form values updated', {
          payload: action.payload,
          formEdits: state.formEdits,
          businessData: state.businessDetails.data
        });

      } else {
        state.businessDetails = { ...initialState.businessDetails };
        state.formEdits = {};
      }
    },
    clearFormValues: (state) => {
      state.formEdits = {};
    },
    clearNewFiles: (state) => {
      if (state.formEdits.government_registration_proof) {
        // Keep only existing files (objects with media_id), remove File objects
        const existingFiles = state.formEdits.government_registration_proof.filter(
          (file: any) => !(file instanceof File) && file.media_id
        );

        if (existingFiles.length > 0) {
          state.formEdits.government_registration_proof = existingFiles;
        } else {
          delete state.formEdits.government_registration_proof;
        }

        // Update business data as well
        if (state.businessDetails.data) {
          state.businessDetails.data = {
            ...state.businessDetails.data,
            ...state.formEdits,
          };
        }
      }
    },
    // New action to merge API response with form edits
    mergeApiResponse: (state, action: PayloadAction<any>) => {
      if (action.payload) {
        console.log('Redux: Merging API response with form edits', {
          apiData: action.payload,
          currentFormEdits: state.formEdits
        });

        state.businessDetails.data = {
          ...action.payload,
          ...state.formEdits,
        };
      }
    },
    logState: (state) => {
      console.log("Current Redux State:", {
        businessDetails: state.businessDetails,
        formEdits: state.formEdits,
        sub_sub_categories: state.sub_sub_categories
      });
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateBusinessDetails.pending, (state) => {
      })
      .addCase(updateBusinessDetails.fulfilled, (state, action) => {

        state.businessDetails.meta = action.payload?.meta || { message: "Updated successfully" };

        if (action.payload?.data) {
          // This prevents re-uploading the same files
          if (state.formEdits.government_registration_proof) {
            const existingFiles = state.formEdits.government_registration_proof.filter(
              (file: any) => !(file instanceof File)
            );

            if (existingFiles.length > 0) {
              state.formEdits.government_registration_proof = existingFiles;
            } else {
              delete state.formEdits.government_registration_proof;
            }
          }

          state.businessDetails.data = {
            ...action.payload.data,
            ...state.formEdits,
          };
        }
      })
      .addCase(updateBusinessDetails.rejected, (state, action) => {
        state.businessDetails.meta = action.payload as any || { error: "Failed to update business details" };
        console.log('Redux: Update business details rejected', action.payload);
      });

    builder
      .addCase(getBusinessDetails.pending, (state) => {
        state.businessDetails.loading = true;
      })
      .addCase(getBusinessDetails.fulfilled, (state, action) => {
        state.businessDetails.loading = false;
        console.log('Redux: Get business details fulfilled', action.payload);

        if (action.payload?.data) {
          const industryCategory = action.payload.data.industry_category;
          if (industryCategory && Array.isArray(industryCategory) && industryCategory.length > 0) {
            console.log("Industry Category from API:", industryCategory);
          }

          // to prevent showing them as new files
          const cleanedFormEdits = { ...state.formEdits };
          if (cleanedFormEdits.government_registration_proof) {
            cleanedFormEdits.government_registration_proof = cleanedFormEdits.government_registration_proof.filter(
              (file: any) => !(file instanceof File)
            );

            if (cleanedFormEdits.government_registration_proof.length === 0) {
              delete cleanedFormEdits.government_registration_proof;
            }
          }

          state.formEdits = cleanedFormEdits;

          const newBusinessData = {
            ...action.payload.data,
            ...state.formEdits,
          };

          state.businessDetails.data = newBusinessData;

          console.log('Redux: Business data merged with form edits', {
            apiData: action.payload.data,
            formEdits: state.formEdits,
            finalData: newBusinessData
          });

        } else {
          state.businessDetails.data = state.businessDetails.data || {};
        }
        state.businessDetails.meta = action.payload?.meta || null;
      })
      .addCase(getBusinessDetails.rejected, (state, action) => {
        state.businessDetails.loading = false;
        state.businessDetails.meta = action.payload as any || { error: "Failed to fetch business details" };
      });

    builder
      .addCase(getSubSubCategories.pending, (state) => {
        state.sub_sub_categories.loading = true;
      })
      .addCase(getSubSubCategories.fulfilled, (state, action) => {
        state.sub_sub_categories.loading = false;
        state.sub_sub_categories.data = action.payload?.data || action.payload || [];
        state.sub_sub_categories.meta = action.payload?.meta || { message: "Sub categories fetched successfully" };
      })
      .addCase(getSubSubCategories.rejected, (state, action) => {
        state.sub_sub_categories.loading = false;
        state.sub_sub_categories.data = [];
        state.sub_sub_categories.meta = action.payload as any || { error: "Failed to fetch sub categories" };
      });

    builder
      .addCase(companyEmailVerifyInitiate.pending, (state) => {
        state.emailVerificationInitiate.loading = true;
        state.emailVerificationInitiate.meta = null;
      })
      .addCase(companyEmailVerifyInitiate.fulfilled, (state, action) => {
        state.emailVerificationInitiate.loading = false;
        state.emailVerificationInitiate.data = action.payload?.data || null;
        state.emailVerificationInitiate.meta = action.payload?.meta || { message: "Verification email sent" };
      })
      .addCase(companyEmailVerifyInitiate.rejected, (state, action) => {
        state.emailVerificationInitiate.loading = false;
        state.emailVerificationInitiate.data = null;
        state.emailVerificationInitiate.meta = action.payload as any || { error: "Failed to send verification email" };
      });

    builder
      .addCase(companyEmailVerify.pending, (state) => {
        state.emailVerify.loading = true;
        state.emailVerify.meta = null;
      })
      .addCase(companyEmailVerify.fulfilled, (state, action) => {
        state.emailVerify.loading = false;
        state.emailVerify.data = null;
        state.emailVerify.meta = action.payload?.meta || { message: "OTP verified successfully" };
      })
      .addCase(companyEmailVerify.rejected, (state, action) => {
        state.emailVerify.loading = false;
        state.emailVerify.meta = action.payload as any || { error: "OTP verification failed" };
      });

  },
});

export const Actions = slice.actions;
export default slice.reducer;