"use client";

import { ListingData } from "@/constants/listing";
import ImageGallery from "@/screens/company/details/module/components/profile/images";
import React, { FC, useEffect } from "react";
import AboutSection from "./about";
import Details from "./detail";
import Slider from "./slider";

import Card from "@/components/elements/card/portfolio";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import classNames from "classnames";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface PortfolioDetailsType {
  title: string;
  content: string;
  images: string[];
  cardData: {
    image: string;
    title: string;
    content: string;
    labels: string[];
    images: string[];
  }[];
}

const PortfolioDetails: React.FC<PortfolioDetailsType> = ({
  // title,
  // content,
  images,
  cardData,
}) => {
  const company = ListingData[0];

  useEffect(() => {
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, []);

  return (
    <div className="modal-content h-full overflow-auto bg-white p-10 rounded-lg w-full max-w-full md:max-w-[calc(100vw-132px)] mx-auto mb-12 sm:mb-16 sm:mx-8 relative mt-60">
      {/* Company info section */}
      <div className="flex gap-3 mb-8">
        <div className="max-w-16 flex justify-start items-start">
          <ImageGallery />
        </div>
        <div className="flex-1 flex flex-col justify-center gap-2">
          <div className="flex items-center justify-start gap-2">
            <h2 className="text-xl md:text-2xl font-bold text-black hover:drop-shadow">
              {company?.name}
            </h2>
            {company?.verified && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <img
                      src={"/assets/pages/verified-gray.svg"}
                      alt="verified"
                      className="block sm:hidden flex-1 aspect-auto w-5 h-auto object-contain"
                    />
                  </TooltipTrigger>
                  <TooltipContent>Verified</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          <div className="flex items-center gap-1.5 mt-1">
            <img
              src={company?.location?.flag}
              alt="flag"
              className="flex-1 aspect-auto max-w-6 w-6 h-auto object-contain"
            />
            <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700">
              <span>
                {company?.location?.address}
                {", "}
              </span>
              <span>{company?.location?.label}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Modal Content */}
      <div className="modal-main-content flex flex-col md:flex-row gap-10">
        <div className="w-full md:w-[70%]">
          <Slider images={images} />
        </div>
        <div className="w-full md:w-[30%] flex flex-col justify-start">
          <AboutSection />
        </div>
      </div>

      {/* Modal Content for Details Section */}
      <div className="modal-main-content flex gap-8">
        <div className="w-full">
          <Details />
        </div>
      </div>

      {/* Full-Width Carousel for Full Portfolio Cards at the Bottom */}
      <div className="w-full mt-8 pt-14 border-t-2 border-gray-200 relative">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-3xl font-semibold">You might also like</h3>
        </div>

        {/* Carousel Container */}
        <Carousel className="w-full relative">
          <CarouselContent>
            {cardData.map((card, index) => (
              <CarouselItem
                key={index}
                className="w-full sm:basis-1/3 lg:basis-1/4 xl:basis-1/3 my-4"
              >
                <Card
                  image={card.image}
                  title={card.title}
                  content={card.content}
                  labels={card.labels}
                  onReadMore={() => console.log("Read more clicked")}
                />
              </CarouselItem>
            ))}
          </CarouselContent>

          {/* Navigation Buttons - Positioned Inside the Carousel */}
          <div className=" w-[90px] absolute top-0 right-0">
            <CarouselPrevious className="absolute top-[-29px] left-0 transform -translate-y-1/2 bg-white text-black p-2 rounded-full z-10 shadow-lg border-none shadow-[0_0_15px_#ccc]" />
            <CarouselNext className="absolute top-[-29px] right-0 transform -translate-y-1/2 bg-white text-black p-2 rounded-full z-10 shadow-lg border-none shadow-[0_0_15px_#ccc]" />
          </div>
        </Carousel>
      </div>
    </div>
  );
};

export const PortfolioDetailsModal: FC<PortfolioDetailsType & {
  className?: string;
  onClose: () => void;
}> = ({ className, onClose, ...rest }) => {
  return (
    <div
      className={classNames(
        "modal-overlay w-screen h-screen overflow-hidden fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center z-50 pb-20",
        className
      )}
    >
      <button
        className="absolute top-10 right-6 bg-[#ED4225] text-white text-3xl p-0 w-10 h-10 flex items-center justify-center rounded-full -mt-1"
        onClick={onClose}
      >
        <span className="mt-[-4px]">&times;</span>
      </button>
      <PortfolioDetails {...rest} />
    </div>
  );
};
export default PortfolioDetails;
