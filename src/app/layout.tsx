import classNames from "classnames";
import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ClientProvider } from "@/provider/ClientProvider";
import { Toaster } from "@/components/ui/toast";
import GlobalHeader from "@/components/layout/header";
import GlobalFooter from "@/components/layout/footer";

const roboto = Roboto({
  subsets: ["latin", "latin-ext", "cyrillic"],
  weight: ["100", "300", "400", "500", "700", "900"],
  display: "swap",
  style: ["normal", "italic"],
  variable: "--font-roboto",
});

export const metadata: Metadata = {
  title: "Aalyana | Connect. Compare. Collaborate",
  description: "Connect. Compare. Collaborate",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={classNames(roboto.className, "antialiased")}>
        <ClientProvider>
          <Toaster />
          <GlobalHeader />
          {children}
          <GlobalFooter />
        </ClientProvider>
      </body>
    </html>
  );
}
