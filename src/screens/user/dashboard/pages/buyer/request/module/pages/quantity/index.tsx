'use client';

import { Input } from "@/components/ui/input/text";
import { Select } from "@/components/ui/input/select";
import React, { useState, useEffect } from 'react';
import { InputTargetType } from "@/components/ui/input/select";

interface QuantityProps {
  onQuantityChange?: (quantityDetails: QuantityDetails) => void;
  initialDetails?: any;
}

interface QuantityDetails {
  amount: number;
  unit: string;
}

const Quantity: React.FC<QuantityProps> = ({
  onQuantityChange,
  initialDetails = {
    amount: 0,
    unit: "kg"
  }
}) => {
  // Initialize with valid numeric amount
  const [quantityDetails, setQuantityDetails] = useState<QuantityDetails>({
    amount: typeof initialDetails.amount === 'number' ? initialDetails.amount : 
           (parseInt(initialDetails.amount) || 0),
    unit: initialDetails.unit || "kg"
  });
  
  // Update when initialDetails changes
  useEffect(() => {
    if (initialDetails) {
      setQuantityDetails({
        amount: typeof initialDetails.amount === 'number' ? initialDetails.amount : 
               (parseInt(initialDetails.amount) || 0),
        unit: initialDetails.unit || "kg"
      });
    }
  }, [initialDetails]);
  
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Allow empty string for direct input but store as number in state
    const value = e.target.value;
    const numericValue = value === '' ? 0 : parseInt(value) || 0;
    
    const updatedDetails = {
      ...quantityDetails,
      amount: numericValue
    };
    setQuantityDetails(updatedDetails);
    if (onQuantityChange) {
      onQuantityChange(updatedDetails);
    }
  };
  
  const handleUnitChange = (e: InputTargetType) => {
    const updatedDetails = {
      ...quantityDetails,
      unit: e.target.value
    };
    setQuantityDetails(updatedDetails);
    if (onQuantityChange) {
      onQuantityChange(updatedDetails);
    }
  };
  
  const unitOptions = [
    { label: "kg", value: "kg" },
    { label: "Unit", value: "unit" },
    { label: "Gram", value: "g" },
    { label: "Pieces", value: "piece" },
    { label: "Boxes", value: "box" },
    { label: "Liters", value: "litre" },
    { label: "Custom", value: "custom" }
  ];

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">How many units do you need?</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <div className="flex space-x-2">
            <div className="flex-grow">
              <Input
                name="amount"
                type="number"
                placeholder="Enter quantity"
                value={quantityDetails.amount === 0 ? '' : quantityDetails.amount}
                onChange={handleAmountChange}
                min="1"
                max="1000000"
              />
            </div>
            <div className="w-28">
              <Select
                placeholder="Unit"
                options={unitOptions}
                value={quantityDetails.unit}
                onChange={handleUnitChange}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Quantity;