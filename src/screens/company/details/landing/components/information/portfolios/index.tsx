"use client";

import PortfolioCard from "@/components/elements/card/portfolio";
import { PortfolioDetailsModal } from "@/components/elements/card/portfolio/details";
import { useState } from "react";

export const portfolioCardData = [
  {
    image: "/assets/pages/portfolio/p_01.png",
    title: "E-Mail-Kampagnen für myenergi",
    content:
      "This is the content of card 1. It gives a brief description of the project or case study.",
    labels: ["Featured", "New"],
    images: ["/assets/pages/portfolio/p_01.png", "/assets/pages/portfolio/p_02.png", "/assets/pages/portfolio/p_03.png", "/assets/pages/portfolio/p_04.png", "/assets/pages/portfolio/p_05.png"],
  },
  {
    image: "/assets/pages/portfolio/p_02.png",
    title: "E-Mail-Marketing für elektroland24",
    content:
      "This is the content of card 1. It gives a brief description of the project or case study.",
    labels: ["New"],
    images: ["/assets/pages/portfolio/p_02.png", "/assets/pages/portfolio/p_03.png", "/assets/pages/portfolio/p_04.png"],
  },
  {
    image: "/assets/pages/portfolio/p_03.png",
    title: "Corporate Identity und Development",
    content:
      "This is the content of card 3. A brief summary of the project or case study.",
    labels: ["Popular"],
    images: ["/assets/pages/portfolio/p_03.png", "/assets/pages/portfolio/p_04.png", "/assets/pages/portfolio/p_05.png"],
  },
  {
    image: "/assets/pages/portfolio/p_04.png",
    title: "E-Mail-Kampagnen für myenergi",
    content:
      "This is the content of card 4. A comprehensive explanation of the project.",
    labels: ["Exclusive"],
    images: ["/assets/pages/portfolio/p_04.png", "/assets/pages/portfolio/p_05.png", "/assets/pages/portfolio/p_06.png"],
  },
  {
    image: "/assets/pages/portfolio/p_05.png",
    title: "E-Mail-Marketing für elektroland24",
    content:
      "This is the content of card 5. The case study includes challenges and solutions.",
    labels: ["Sale", "Limited"],
    images: ["/assets/pages/portfolio/p_05.png", "/assets/pages/portfolio/p_06.png", "/assets/pages/portfolio/p_01.png"],
  },
  {
    image: "/assets/pages/portfolio/p_06.png",
    title: "Corporate Identity und Development",
    content:
      "This is the content of card 1. It gives a brief description of the project or case study.",
    labels: ["Limited"],
    images: ["/assets/pages/portfolio/p_06.png", "/assets/pages/portfolio/p_01.png", "/assets/pages/portfolio/p_02.png"],
  },
];

function Portfolios() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<{
    title: string;
    content: string;
    images: string[];
  } | null>(null);

  const openModal = (card: {
    title: string;
    content: string;
    images: string[];
  }) => {
    setSelectedCard(card);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedCard(null);
  };

  return (
    <div className="portfolio">
      <h2 className="text-base font-bold mb-8">Portfolio / Case Studies</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {portfolioCardData.map((card, index) => (
          <PortfolioCard
            key={index}
            image={card.image}
            title={card.title}
            content={card.content}
            labels={card.labels}
            onClick={() => openModal(card)}
            onReadMore={() => openModal(card)}
          />
        ))}
      </div>

      {isModalOpen && selectedCard && (
        <PortfolioDetailsModal
          title={selectedCard.title}
          content={selectedCard.content}
          images={selectedCard.images}
          onClose={closeModal}
          cardData={portfolioCardData}
        />
      )}
    </div>
  );
}

export default Portfolios;
