'use client';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
// import { serviceDetails } from "@/pages/user/dashboard/pages/seller/listing/module/services/@redux/selectors";
// import { Actions } from "@/pages/user/dashboard/pages/seller/listing/module/services/@redux/slice";
// import {
//   getService,
//   updateServiceDetails,
// } from "@/pages/user/dashboard/pages/seller/listing/module/services/@redux/thunk";
import { RootDispatch } from "@/redux/store";
import { AlertCircle } from "lucide-react";
import { lazy, Suspense, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { usePathname, useRouter, useParams } from "next/navigation";
import { updateRFQDetails } from "@/screens/user/dashboard/pages/buyer/manage/@redux/thunk"; // ✅ your RFQ update thunk
import { updateRFQDetailsSelector } from "@/screens/user/dashboard/pages/buyer/manage/@redux/selector";
import { resetRFQDetails } from "../../@redux/slice";

const formData = [
  {
    title: "Basic Service Information",
    component: lazy(() => import("./info")),
  },
  {
    title: "Additional information",
    component: lazy(() => import("./conatct")),
  },
  // {
  //   title: "Service Specifications",
  //   component: lazy(() => import("./variety")),
  // },
];

const AddService = () => {
  const dispatch = useDispatch<RootDispatch>();
  const router = useRouter();
  const { data: values, meta, loading } = useSelector(updateRFQDetailsSelector);
  const params = useParams();
  const service_id = params?.id as string;

  const handleSave = (addNew?: boolean) => {
    try {
      if (values && service_id) {
        const cleanProductDetails = { ...values.productDetails };
        delete cleanProductDetails._id;
        delete cleanProductDetails.detailedDescription;

        const cleanServiceDetails = values.serviceDetails
          ? { ...values.serviceDetails }
          : undefined;

        if (cleanServiceDetails && cleanServiceDetails._id) {
          delete cleanServiceDetails._id;
        }

        if (cleanServiceDetails && 'detailedDescription' in cleanServiceDetails) {
          delete cleanServiceDetails.detailedDescription;
        }

        const cleanContactInfo = values.contactInfo
          ? { ...values.contactInfo }
          : undefined;

        if (cleanContactInfo && cleanContactInfo._id) {
          delete cleanContactInfo._id;
        }

        if (cleanContactInfo && 'pincode' in cleanContactInfo) {
          delete cleanContactInfo.pincode;
        }

        if (
          cleanContactInfo &&
          cleanContactInfo.phoneCode &&
          cleanContactInfo.phone
        ) {
          cleanContactInfo.phone = `${cleanContactInfo.phoneCode}${cleanContactInfo.phone}`;
          delete cleanContactInfo.phoneCode;
        }

        const cleanValues = {
          ...values,
          productDetails: cleanProductDetails,
          ...(cleanServiceDetails ? { serviceDetails: cleanServiceDetails } : {}),
          ...(cleanContactInfo ? { contactInfo: cleanContactInfo } : {}),
        };

        dispatch(updateRFQDetails({ rfq_id: service_id, data: cleanValues }))
          .unwrap()
          .then(() => {
            dispatch(resetRFQDetails());

            if (!addNew) {
              router.back();
            }
          })
          .catch((error) => {
            console.error("Failed to update RFQ:", error);
          });
      }
    } catch (error) {
      console.error(error);
    }
  };

  //   if (loading) {
  //     return <Loader big center />;
  //   }

  //   if (service_id && !loading && !values) {
  //     return (
  //       <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
  //         <AlertCircle /> {meta?.message}
  //       </div>
  //     );
  //   }

  return (
    <main className="w-full h-full">
      <h2 className="text-base font-semibold mb-6">
        Update RFQ of Service
      </h2>
      <Accordion
        type="multiple"
        className="w-full"
        defaultValue={formData?.map((_, i) => String(i))}
      >
        {formData?.map((form, index) => {
          return (
            <AccordionItem
              key={index}
              value={String(index)}
              className="bg-white rounded-lg md:shadow-box md:p-4 [&:not(:last-child)]:mb-3"
            >
              <AccordionTrigger>{form?.title}</AccordionTrigger>
              <AccordionContent
                className="!py-6 px-7 flex flex-col gap-8 [&>*]:w-full"
              // disabled={loading}
              // loading={loading}
              >
                <Suspense fallback={<Loader center big />}>
                  <form.component />
                </Suspense>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>

      <div className="flex items-center justify-start md:justify-end flex-wrap gap-2 mt-7 [&>*]:rounded-md">
        <Button variant="destructive" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button
          variant="main-revert"
          onClick={() => handleSave()}
          disabled={!values}
        >
          Save
        </Button>
      </div>
    </main>
  );
};

export default AddService;
