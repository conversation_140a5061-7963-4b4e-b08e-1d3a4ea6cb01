import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import classNames from "classnames";
import { useState } from "react";

const ProductTabs = () => {
  const [active, setActive] = useState<number>(0);
  return (
    <div
      className={classNames(
        "z-10 relative w-full mb-3 group",
        "after:-z-10 after:w-full after:h-[1px] after:absolute after:left-0 after:bottom-0 after:bg-stone-200"
      )}
    >
      <Carousel
        opts={{
          align: "start",
          loop: false,
          slidesToScroll: 1,
        }}
        className="w-full static"
      >
        <CarouselContent className="-ml-8">
          {Array.from({ length: 50 }).map((_, index) => {
            const isActive = index === active;
            return (
              <CarouselItem
                key={index}
                role="button"
                className={classNames(
                  "ml-8 !pl-0 z-20 relative basis-auto max-w-max my-0.5 cursor-pointer pt-3 pb-4 transition-all",
                  "after:z-10 after:w-full after:h-[3px] after:absolute after:right-0 after:-bottom-0.5 after:transition-all",
                  isActive
                    ? "after:bg-main font-bold text-black"
                    : "after:bg-transparent text-stone-700 font-semibold"
                )}
                onClick={() => setActive(index)}
              >
                <span
                  className={classNames(
                    "text-[0.92rem] w-max whitespace-nowrap"
                  )}
                >
                  Sell Tab {index + 1}
                </span>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselPrevious className="absolute -left-12 top-1/2 -translate-y-1/2 shadow-xl bg-white border border-gray-200 !opacity-0 group-hover:!opacity-100 transition-opacity duration-200 z-10 h-10 w-10 " />
        <CarouselNext className="absolute -right-12 top-1/2 -translate-y-1/2 shadow-xl bg-white border border-gray-200 !opacity-0 group-hover:!opacity-100 transition-opacity duration-200 z-10 h-10 w-10" />
      </Carousel>
    </div>
  );
};

export default ProductTabs;