import { LockedRender } from "@/components/elements/card/list-card";
import ReviewsStars from "@/components/elements/icon/reviews-stars";
import { <PERSON><PERSON> } from "@/components/ui/button";
import InputLabel from "@/components/ui/input/components/label";
import { Dropzone, InlinePlaceholder } from "@/components/ui/input/dropzone";
import { RadioGroup, RadioGroupItem } from "@/components/ui/input/radio";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { TextEditor } from "@/components/ui/input/texteditor";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useParams } from "next/navigation";
import { ReactNode, useState } from "react";

const OpportunityDetails = () => {
  const { opportunity_id } = useParams();
  console.log({ opportunity_id });

  const [values, setValues] = useState<Record<string, any>>({});

  const handleChange = (e: any) => {
    setValues((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const companyDetail = ListingData[0];

  return (
    <div className="w-full h-full text-sm">
      <div className="grid grid-cols-1 md:grid-cols-[1fr_minmax(250px,auto)] gap-4">
        <div className="w-full order-2 md:order-1 bg-white shadow-box p-4 rounded-md">
          <h3 className="text-lg font-medium my-3 md:my-4">Submit Proposal</h3>

          <div className="flex items-center gap-3">
            <h3 className="text-lg font-medium">
              Website Design and Development
            </h3>
            <span className={classNames("text-green-400")}>(open)</span>
          </div>

          <div className="w-full flex flex-col gap-5 [&>*]:w-full mt-5">
            <FormTableRow
              title={
                <span className="block mt-1">Specific Product Required</span>
              }
              className="!items-start"
            >
              <span className="block mt-1">
                Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed
                diam nonumy eirmod tempor invidunt ut labore et dolore magna
                aliquyam erat, sed diam voluptua. At vero eos et accusam et
                justo duo dolores et ea rebum. Stet
              </span>
            </FormTableRow>

            <FormTableRow
              title={<span className="block mt-1">Product Specifications</span>}
              className="!items-start"
            >
              <span className="block mt-1">
                Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed
                diam nonumy eirmod tempor invidunt ut labore et dolore magna
                aliquyam erat, sed diam voluptua. At vero eos et accusam et
                justo duo dolores et ea rebum. Stet
              </span>
            </FormTableRow>

            <FormTableRow title="Unit Price">
              <Input
                type="text"
                name="unit_price"
                placeholder="Unit Price"
                value={values?.unit_price}
                onChange={handleChange}
              />
            </FormTableRow>

            <FormTableRow title="Offer Price">
              <Input
                type="text"
                name="offer_price"
                placeholder="Offer Price"
                value={values?.offer_price}
                onChange={handleChange}
              />
            </FormTableRow>

            <FormTableRow title="Proposal Type">
              <RadioGroup
                value={values?.proposal_type}
                name="billing_cycle"
                defaultValue="monthly"
                className="grid-cols-2 md:grid-cols-4 gap-6 w-max"
                onChange={handleChange}
              >
                <div className="flex items-center space-x-2 w-max">
                  <RadioGroupItem value="fixed_price" id="r1" />
                  <InputLabel htmlFor="r1">Fixed Price</InputLabel>
                </div>
                <div className="flex items-center space-x-2 w-max">
                  <RadioGroupItem value="negotiable" id="r2" />
                  <InputLabel htmlFor="r2">Negotiable</InputLabel>
                </div>
              </RadioGroup>
            </FormTableRow>

            <FormTableRow title="MOQ (Minimum Order Quantity)">
              <div className="flex-1 flex items-center gap-1">
                <Input
                  type="text"
                  name="minimum_order_quantity"
                  onChange={handleChange}
                  value={values?.minimum_order_quantity}
                  placeholder="Enter Quantity"
                  containerClassName="flex-1"
                />
                <Select
                  name="minimum_order_quantity_unit"
                  onChange={handleChange}
                  value={values?.minimum_order_quantity_unit}
                  placeholder="Unit"
                  options={[{ label: "kg", value: "kg" }]}
                />
              </div>
            </FormTableRow>

            <FormTableRow title="Delivery Time / Lead Time">
              <Input
                type="text"
                name="delivery_time"
                placeholder="Delivery Time"
                value={values?.delivery_time}
                onChange={handleChange}
              />
            </FormTableRow>

            <FormTableRow title="Cover letter" className="!items-start">
              <TextEditor
                name="cover_letter"
                value={values?.cover_letter}
                onChange={handleChange}
                containerClassName="flex-1"
                className="flex-1 rounded-md"
              />
            </FormTableRow>

            <FormTableRow
              title="Product Specification File"
              className="!items-start"
            >
              <Dropzone
                containerClassName="flex-1 w-full"
                onChange={(e) => handleChange(e)}
                name="brandingMedia"
                value={values?.brandingMedia}
                preview
                onFileDelete={(file) => console.log(file)}
              >
                {values?.brandingMedia && <InlinePlaceholder />}
              </Dropzone>
            </FormTableRow>
          </div>

          <div className="flex items-center justify-start md:justify-end flex-wrap gap-2 mt-12">
            <Button variant="main">Decline</Button>
            <Button variant="main-revert">Submit Proposal</Button>
          </div>
        </div>

        <DetailsSidebar details={companyDetail} />
      </div>
    </div>
  );
};

const DetailsSidebar = ({ details }: { details: any }) => {
  return (
    <div className="order-1 md:order-2 bg-white shadow-box p-4 rounded-md">
      <h3 className="text-lg font-semibold mb-6">About Client</h3>

      <div className="flex flex-col items-start gap-3.5 [&>*]:w-full">
        <h4 className="font-semibold flex justify-start">
          <LockedRender />
        </h4>
        <div className="flex items-center gap-1.5">
          <img
            src={details?.location?.flag}
            alt="flag"
            className="flex-1 aspect-auto max-w-5 w-5 h-auto object-contain"
          />
          <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 text-wrap">
            <span>{details?.location?.address}</span>
            <span>{details?.location?.label}</span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <ReviewsStars rating={4} />
          <span className="font-light text-base text-stone-700">4/5</span>
        </div>

        {details?.verified && (
          <div className="flex items-center gap-2 mb-2">
            <div className="hidden sm:flex bg-[#9F9795] text-white px-2 py-1 items-center justify-start gap-1.5 w-max max-w-max rounded-sm">
              <img
                src={"/assets/pages/verified-white.svg"}
                alt="flag"
                className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
              />
              <span className="flex-1 text-[0.6rem] font-medium">Verified</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const FormTableRow = ({
  title,
  children,
  className,
}: {
  title: string | ReactNode;
  children: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={classNames(
        "grid grid-cols-1 md:grid-cols-[200px_10px_minmax(400px,_1fr)] items-center align-middle gap-4",
        className
      )}
    >
      <span className="text-sm">{title}</span>
      <span className="hidden md:flex items-start justify-center leading-3 h-full mt-2">
        :
      </span>
      <span className="w-full flex items-center justify-start [&>*]:flex-1">
        {children}
      </span>
    </div>
  );
};

export default OpportunityDetails;
