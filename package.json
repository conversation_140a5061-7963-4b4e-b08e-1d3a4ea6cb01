{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-virtual": "^3.13.9", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jose": "^6.0.11", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.511.0", "moment": "^2.30.1", "next": "15.3.2", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "quill": "^2.0.3", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-file-drop": "^3.1.6", "react-hover-card": "^0.3.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-quill-new": "^3.4.6", "react-redux": "^9.2.0", "react-transition-group": "^4.4.5", "redux-persist": "^6.0.0", "reselect": "^5.1.1", "sonner": "^2.0.3", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "husky": "^9.1.7", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}