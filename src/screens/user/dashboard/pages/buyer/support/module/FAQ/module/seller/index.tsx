import FrequentlyAskedQuestions from "@/components/elements/list/faq";
import React from 'react'


const faqData = Array.from({ length: 6 })?.map((_) => ({
  title: "This is seller FAQ?",
  description:
    "Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and.",
}));


const index = () => {
  return (
    <div className="max-w-7xl w-full mx-auto px-4 sm:px-6 lg:px-8">
       <div className="bg-white shadow-md rounded-md px-7 py-10 mt-8">
        <h3 className="font-bold mb-4">Frequently Asked Questions</h3>
        <FrequentlyAskedQuestions type="single" data={faqData} />
      </div>
    </div>
  )
}

export default index
