import image1 from "/assets/pages/portfolio/p_01.png";
import image2 from "/assets/pages/portfolio/p_02.png";
import image3 from "/assets/pages/portfolio/p_03.png";
import image4 from "/assets/pages/portfolio/p_04.png";
import image5 from "/assets/pages/portfolio/p_05.png";
import image6 from "/assets/pages/portfolio/p_06.png";
import PortfolioCard from "@/components/elements/card/portfolio";
import { PortfolioDetailsModal } from "@/components/elements/card/portfolio/details";
import { useState } from "react";

export const portfolioCardData = [
  {
    image: image1,
    title: "E-Mail-Kampagnen für myenergi",
    content:
      "This is the content of card 1. It gives a brief description of the project or case study.",
    labels: ["Featured", "New"],
    images: [image1, image2, image3, image4, image5],
  },
  {
    image: image2,
    title: "E-Mail-Marketing für elektroland24",
    content:
      "This is the content of card 1. It gives a brief description of the project or case study.",
    labels: ["New"],
    images: [image2, image3, image4],
  },
  {
    image: image3,
    title: "Corporate Identity und Development",
    content:
      "This is the content of card 3. A brief summary of the project or case study.",
    labels: ["Popular"],
    images: [image3, image4, image5],
  },
  {
    image: image4,
    title: "E-Mail-Kampagnen für myenergi",
    content:
      "This is the content of card 4. A comprehensive explanation of the project.",
    labels: ["Exclusive"],
    images: [image4, image5, image6],
  },
  {
    image: image5,
    title: "E-Mail-Marketing für elektroland24",
    content:
      "This is the content of card 5. The case study includes challenges and solutions.",
    labels: ["Sale", "Limited"],
    images: [image5, image6, image1],
  },
  {
    image: image6,
    title: "Corporate Identity und Development",
    content:
      "This is the content of card 1. It gives a brief description of the project or case study.",
    labels: ["Limited"],
    images: [image6, image1, image2],
  },
];

function Portfolios() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<{
    title: string;
    content: string;
    images: string[];
  } | null>(null);

  const openModal = (card: {
    title: string;
    content: string;
    images: string[];
  }) => {
    setSelectedCard(card);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedCard(null);
  };

  return (
    <div className="portfolio">
      <h2 className="text-base font-bold mb-8">Portfolio / Case Studies</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {portfolioCardData.map((card, index) => (
          <PortfolioCard
            key={index}
            image={card.image}
            title={card.title}
            content={card.content}
            labels={card.labels}
            onClick={() => openModal(card)}
            onReadMore={() => openModal(card)}
          />
        ))}
      </div>

      {isModalOpen && selectedCard && (
        <PortfolioDetailsModal
          title={selectedCard.title}
          content={selectedCard.content}
          images={selectedCard.images}
          onClose={closeModal}
          cardData={portfolioCardData}
        />
      )}
    </div>
  );
}

export default Portfolios;
