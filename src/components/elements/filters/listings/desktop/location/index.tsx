"use client";

import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { Input } from "@/components/ui/input/text";
import { locations } from "@/constants/location";
import { getFilteredData } from "@/lib/utils/data";
import { useState } from "react";
import { FiSearch } from "react-icons/fi";

const LocationSearchFilter = () => {
  const [searchValue, setSearchValue] = useState<string>("");

  const filteredMenu = getFilteredData({
    data: locations,
    compareValue: searchValue,
    compareParams: ["label", "code"],
  });

  const finalData = searchValue ? filteredMenu : locations;

  return (
    <Dropdown
      className="flex-1 min-w-36"
      open={Boolean(searchValue)}
      content={
        <DropdownList
          data={finalData}
          className="min-w-full w-min border-0"
          onSelect={(option) => setSearchValue(option?.title)}
        />
      }
      contentClassName="w-full z-50 mt-0"
    >
      <Input
        type="text"
        placeholder="Location"
        wrapperClassName="rounded-full"
        icon={<FiSearch size={6} />}
        value={searchValue}
        onChange={(e) => setSearchValue(e?.target?.value)}
        containerClassName="flex-1"
      />
    </Dropdown>
  );
};

export default LocationSearchFilter;
