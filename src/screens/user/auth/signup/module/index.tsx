"use client";

import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, CheckboxChangeEvent } from "@/components/ui/input/checkbox";
import InputLabel from "@/components/ui/input/components/label";
import CountrySelect from "@/components/ui/input/country-select";
import {
  RadioChangeEvent,
  RadioGroup,
  RadioGroupItem,
} from "@/components/ui/input/radio";
import { Input } from "@/components/ui/input/text";
import { Loader } from "@/components/ui/loader";
import {
  capitalizeWords,
  hasRequiredKeys,
  isEmptyObject,
} from "@/lib/utils/data";
import * as Validation from "@/lib/utils/validation";
import useAuth from "@/screens/user/auth/@context/useAuth";
import { ChangeEvent, FormEvent, useState } from "react";
import { useRouter } from "next/navigation";
import AuthBox from "../../common/auth-box";
import Oauth from "../../common/oauth";

const Signup = () => {
const router = useRouter();
  const { register, loading, response } = useAuth();
  const [values, setValues] = useState<any>({
    user_role: "seller",
  });
  const [terms, setTerms] = useState<boolean>(false);
  const [error, setError] = useState<any>(null);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | CheckboxChangeEvent
      | RadioChangeEvent
  ) => {
    try {
      const name = e?.target?.name;
      const value = e?.target?.value;

      const validator = Validation?.[
        `validate${capitalizeWords(name)}` as keyof typeof Validation
      ]?.(value as string);

      if (validator) {
        setError((prev: any) => {
          if (!prev) prev = {};
          if (!validator?.valid) {
            prev[name] = validator?.errors;
          } else {
            delete prev[name];
          }
          return prev;
        });
      }

      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    try {
      e?.preventDefault();

      if (!isEmptyObject(error)) return;

      await register(values);

      setValues({});
      setTimeout(() => {
        router.push("/dashboard");
      }, 1000);
    } catch (error) {
      console.error(error);
    }
  };

  const isValid =
    terms &&
    isEmptyObject(error) &&
    hasRequiredKeys(values, [
      "email",
      "password",
      "user_role",
      "country",
      "fullName",
    ]);

  return (
    <AuthBox
      title="Create Your Account"
      subText="Join us to unlock exclusive features, offers, and updates."
    >
      {loading && <Loader big center box />}
      {response && (
        <Alert className="mb-4" variant={response?.type}>
          {response?.type && (
            <AlertTitle className="capitalize">{response?.type}</AlertTitle>
          )}
          {response?.meta?.message && (
            <AlertDescription className="font-semibold">
              {response?.meta?.message}
            </AlertDescription>
          )}
        </Alert>
      )}

      <div className="w-full flex flex-col gap-8">
        <form
          className="w-full flex flex-col gap-8"
          onSubmit={(e) => handleSubmit(e)}
        >
          <div className="flex flex-col gap-5">
            <InputLabel>Please select your role</InputLabel>

            <RadioGroup
              name="user_role"
              defaultValue={values?.user_role}
              value={values?.user_role}
              className="grid-cols-2 md:grid-cols-4 gap-6 w-max"
              onChange={handleChange}
            >
              <div className="flex items-center space-x-2 w-max">
                <RadioGroupItem value="seller" id="r1" />
                <InputLabel htmlFor="r1">Seller</InputLabel>
              </div>
              <div className="flex items-center space-x-2 w-max">
                <RadioGroupItem value="buyer" id="r2" />
                <InputLabel htmlFor="r2">Buyer</InputLabel>
              </div>
              <div className="flex items-center space-x-2 w-max">
                <RadioGroupItem value="advertiser" id="r3" />
                <InputLabel htmlFor="r3">Advertiser</InputLabel>
              </div>
              <div className="flex items-center space-x-2 w-max">
                <RadioGroupItem value="all-in-one" id="r4" />
                <InputLabel htmlFor="r4">All-in-One</InputLabel>
              </div>
            </RadioGroup>
          </div>

          <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
            <CountrySelect
              name="country"
              label="Country"
              placeholder="Select Country"
              className="!rounded-full"
              onChange={handleChange}
              value={values.country}
              dialCode
            />

            <Input
              type="text"
              name="fullName"
              label="Full Name"
              onChange={handleChange}
              value={values?.fullName ?? ""}
              placeholder="Enter your full name"
              wrapperClassName="!rounded-full"
              error={error?.fullName}
            />

            <Input
              type="email"
              name="email"
              label="Email Address"
              onChange={handleChange}
              value={values?.email ?? ""}
              placeholder="Enter your email address"
              wrapperClassName="!rounded-full"
              error={error?.email}
            />

            <Input
              type="password"
              name="password"
              label="Password"
              onChange={handleChange}
              value={values?.password ?? ""}
              placeholder="Enter password"
              wrapperClassName="!rounded-full"
              autoComplete="off"
              error={error?.password}
            />
            <Checkbox
              name="terms"
              label="I agree to the Terms & Conditions."
              labelClassName="whitespace-nowrap"
              onChange={(e) => setTerms(e?.target?.value)}
              checked={terms}
            />
          </div>
          <Button
            type="submit"
            variant="main-revert"
            className="w-full rounded-full py-2"
            disabled={!isValid}
          >
            Signup
          </Button>
        </form>

        <Oauth title="Sign up with" />

        <div className="flex items-center justify-center gap-1.5 text-sm text-center">
          <p>Do you have an Already account?</p>
          <button
            className="font-semibold hover:drop-shadow text-main"
            onClick={() => router.push("/signin")}
          >
            Sign in
          </button>
        </div>
      </div>
    </AuthBox>
  );
};

export default Signup;
