"use client";

import FooterBranding from "./components/branding";
import FooterCopyright from "./components/copyright";
import FooterLanguage from "./components/language";
import FooterMenu from "./components/navigation";
import FooterSocial from "./components/social";
import FooterSubscribe from "./components/subscribe";

const GlobalFooter = () => {
  return (
    <div id="global-footer" className="bg-dark text-white shadow">
      <div className="container pt-12 sm:pt-16 pb-7">
        <div className="flex flex-col sm:flex-row items-center sm:items-start flex-wrap justify-center sm:justify-between gap-16">
          <FooterMenu className="flex-1 order-2 sm:order-1" />
          <FooterSubscribe className="order-1 sm:order-2" />
        </div>
        <div className="flex items-center justify-center sm:justify-start flex-wrap gap-8 mt-8">
          <FooterBranding className="hidden sm:block" />
          <FooterLanguage />
        </div>
        <div className="border-t border-white pt-4 mt-3 flex items-center justify-between gap-6 flex-wrap">
          <FooterBranding className="block sm:hidden !py-2" />
          <FooterCopyright />
          <FooterSocial />
        </div>
      </div>
    </div>
  );
};

export default GlobalFooter;
