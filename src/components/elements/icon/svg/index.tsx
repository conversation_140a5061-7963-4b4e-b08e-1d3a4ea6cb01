"use client";

import { Loader } from "@/components/ui/loader";
import classNames from "classnames";
import { JSX, useEffect, useState } from "react";

type SvgLoaderProps = {
  src: string;
  className?: string;
  width?: number | string;
  height?: number | string;
  fallback?: JSX.Element;
  parentColor?: boolean;
};

const SvgLoader: React.FC<SvgLoaderProps> = ({
  src,
  className,
  width = "min-content",
  height = "min-content",
  fallback = <span>⚠️</span>,
  parentColor,
}) => {
  const [svgContent, setSvgContent] = useState<string | null>(null);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    const fetchSvg = async () => {
      try {
        const response = await fetch(src);
        if (!response.ok) throw new Error("Failed to load SVG");
        let svgText = await response.text();
        if (parentColor) {
          svgText = svgText.replace(/fill=".*?"/g, 'fill="inherit"');
          svgText = svgText.replace(/stroke=".*?"/g, 'stroke="inherit"');
          svgText = svgText.replace(
            /stroke-width=".*?"/g,
            'stroke-width="inherit"'
          );
        }
        setSvgContent(svgText);
      } catch (err) {
        console.error("SVG Load Error:", err);
        setError(true);
      }
    };

    fetchSvg();
  }, [parentColor, src]);

  if (error) return fallback;

  return svgContent ? (
    <span
      className={classNames("[&_svg]:w-5 [&_svg]:h-5", className)}
      style={{ display: "inline-block", width, height }}
      dangerouslySetInnerHTML={{ __html: svgContent }}
    />
  ) : (
    <Loader />
  );
};

export default SvgLoader;
