"use client";

import { HighlightedText } from "@/components/elements/text/highlight";
import { Checkbox } from "@/components/ui/input/checkbox";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import classNames from "classnames";
import { ChangeEvent, useState } from "react";

const QuoteContact = () => {
  const [values, setValues] = useState<any>({});

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | InputTargetType
  ) => {
    try {
      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="min-w-[40vw] min-h-[30vh] h-full flex-1 p-10 flex items-start justify-start w-full flex-col gap-6 [&>*]:w-full">
      <h5 className="text-2xl font-bold text-center">
        <HighlightedText
          title="Contact Information"
          highlight={["information"]}
        />
      </h5>

      <Input
        type="text"
        name="companyName"
        label="Company Name"
        onChange={handleChange}
        value={values?.name}
        placeholder="Enter your company name"
        labelClassName="mb-1 ml-5"
        wrapperClassName="!rounded-full  "
      />
      <Input
        type="text"
        name="name"
        label="Full Name"
        onChange={handleChange}
        value={values?.name}
        placeholder="Enter your full name"
        labelClassName="mb-1 ml-5"
        wrapperClassName="!rounded-full  "
      />
      <Input
        type="email"
        name="email"
        label="email Address"
        onChange={handleChange}
        value={values?.email}
        labelClassName="mb-1 ml-5"
        placeholder="Enter your email address"
        wrapperClassName="!rounded-full  "
      />

      <Input
        type="text"
        name="phone"
        label="Phone"
        onChange={handleChange}
        value={values?.phone}
        labelClassName="mb-1 ml-5"
        placeholder="Enter your phone number"
        wrapperClassName="!rounded-full  "
        countrySelector={{
          defaultValue: "+91",
          name: "countryCode",
          onChange: handleChange,
          value: values?.countryCode,
        }}
      />

      <Checkbox
        label="I agree to the Terms & Conditions."
        labelClassName="whitespace-nowrap"
        containerClassName={classNames("order-1 sm:order-2")}
      />
    </div>
  );
};

export default QuoteContact;
