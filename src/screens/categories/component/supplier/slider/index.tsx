"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ReactNode } from "react";

const ProductSlider = ({ children }: { children: ReactNode[] }) => {
  return (
    <>
      {/* Mobile Carousel - Only visible on small screens */}
      <div className="block md:hidden group">
        <Carousel
          opts={{
            align: "start",
            dragFree: true
          }}
          className="w-full"
        >
          <CarouselContent className="">
            {children.map((child, index) => (
              <CarouselItem
                key={index}
                className=" basis-[85%] sm:basis-1/3"
              >
                {child}
              </CarouselItem>
            ))}
          </CarouselContent>
          {/* Add navigation buttons for mobile */}
          <CarouselPrevious className="bg-white !shadow-xl border border-gray-100 rounded-full !h-10 !w-10 -left-4 focus:ring-1 focus:ring-gray-200 focus:outline-none !opacity-0 group-hover:!opacity-100 transition-opacity duration-200" />
          <CarouselNext className="bg-white !shadow-xl border border-gray-100 rounded-full !h-10 !w-10 -right-4 focus:ring-1 focus:ring-gray-200 focus:outline-none !opacity-0 group-hover:!opacity-100 transition-opacity duration-200" />
        </Carousel>
      </div>

      {/* Desktop/Original Carousel - Hidden on small screens */}
      <div className="hidden md:block group">
        <Carousel
          opts={{ align: "start" }}
          className="w-full relative"
        >
          <CarouselContent className="">
            {children.map((child, index) => (
              <CarouselItem
                key={index}
                className="pl-4 basis-full sm:basis-[50%] md:basis-1/3 2xl:basis-[20%]"
              >
                {child}
              </CarouselItem>
            ))}
          </CarouselContent>

          <CarouselPrevious className="bg-white !shadow-xl border border-gray-100 rounded-full !h-12 !w-12 -left-6 focus:ring-1 focus:ring-gray-200 focus:outline-none !opacity-0 group-hover:!opacity-100 transition-opacity duration-200" />
          <CarouselNext className="bg-white !shadow-xl border border-gray-100 rounded-full !h-12 !w-12 top-1 -right-6 focus:ring-1 focus:ring-gray-200 focus:outline-none !opacity-0 group-hover:!opacity-100 transition-opacity duration-200" />
        </Carousel>
      </div>
    </>
  );
};

export default ProductSlider;