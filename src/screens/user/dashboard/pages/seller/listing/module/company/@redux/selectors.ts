import { RootState } from "@/redux/root-reducer";
import { createSelector } from "reselect";

export const reducer = (state: RootState) => state.user_company;

export const currentFormStep = createSelector(
  [reducer],
  (reducer) => reducer.currentFormStep
);

export const companyList = createSelector(
  [reducer],
  (reducer) => reducer.companyList
);

export const companyDetails = createSelector(
  [reducer],
  (reducer) => reducer.companyDetails
);

export const categories = createSelector(
  [reducer],
  (reducer) => reducer.categories
);

export const sub_categories = createSelector(
  [reducer],
  (reducer) => reducer.sub_categories
);

export const sub_sub_categories = createSelector(
  [reducer],
  (reducer) => reducer.sub_sub_categories
);

export const fieldsByCategory = createSelector(
  [reducer],
  (reducer) => reducer.fieldsByCategory
);

export const emailVerificationInitiate = createSelector(
  [reducer],
  (reducer) => reducer.emailVerificationInitiate
);

export const emailVerify = createSelector(
  [reducer],
  (reducer) => reducer.emailVerify
);
