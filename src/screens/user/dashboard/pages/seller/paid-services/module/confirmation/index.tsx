import ConfirmPriceCard from "@/components/elements/card/price/confirm";
import { Button } from "@/components/ui/button";
import InputLabel from "@/components/ui/input/components/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/input/radio";
import { Switch } from "@/components/ui/input/switch";
import { Input } from "@/components/ui/input/text";
import { ReactNode, useState } from "react";

const faqData = Array.from({ length: 4 })?.map((_) => ({
  title: "Is listing my business free of charge?",
  description:
    "Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and.",
}));

const PlanConfirmation = () => {
  const [values, setValues] = useState<Record<string, any>>({});

  const handleChange = (e: any) => {
    setValues((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  return (
    <div className="w-full bg-white p-6 rounded-md">
      <div className="flex items-center flex-wrap gap-4 md:gap-10 mt-4 mb-8">
        <InputLabel className="text-base font-semibold">
          Select New Plan
        </InputLabel>

        <RadioGroup
          value={values?.plan_type}
          name="plan_type"
          defaultValue="basic"
          className="grid-cols-2 md:grid-cols-4 gap-6 w-max"
          onChange={handleChange}
        >
          <div className="flex items-center space-x-2 w-max">
            <RadioGroupItem value="basic" id="r1" />
            <InputLabel htmlFor="r1">Basic</InputLabel>
          </div>
          <div className="flex items-center space-x-2 w-max">
            <RadioGroupItem value="professional" id="r2" />
            <InputLabel htmlFor="r2">Professional</InputLabel>
          </div>
          <div className="flex items-center space-x-2 w-max">
            <RadioGroupItem value="enterprise" id="r3" />
            <InputLabel htmlFor="r3">Enterprise</InputLabel>
          </div>
          <div className="flex items-center space-x-2 w-max">
            <RadioGroupItem value="custom" id="r4" />
            <InputLabel htmlFor="r3">Custom</InputLabel>
          </div>
        </RadioGroup>
      </div>

      <div className="grid grid-cols-2 gap-6">
        {[...Array(+2).keys()]?.map((_, index) => {
          return (
            <ConfirmPriceCard
              key={index}
              title="Current Plan"
              type="Basic"
              price="80"
              period="month"
              checklist={[
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: true,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: true,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: false,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: false,
                },
              ]}
            />
          );
        })}
      </div>

      <div className="flex flex-col gap-5 [&>*]:w-full mt-12 mb">
        <FormTableRow title="Auto-Renewal">
          <Switch />
        </FormTableRow>
        <FormTableRow title="Discount">15%</FormTableRow>
        <FormTableRow title="Billing Cycle">
          <RadioGroup
            value={values?.billing_cycle}
            name="billing_cycle"
            defaultValue="monthly"
            className="grid-cols-2 md:grid-cols-4 gap-6 w-max"
            onChange={handleChange}
          >
            <div className="flex items-center space-x-2 w-max">
              <RadioGroupItem value="monthly" id="r1" />
              <InputLabel htmlFor="r1">Monthly</InputLabel>
            </div>
            <div className="flex items-center space-x-2 w-max">
              <RadioGroupItem value="yearly" id="r2" />
              <InputLabel htmlFor="r2">Yearly</InputLabel>
            </div>
            <div className="flex items-center space-x-2 w-max">
              <RadioGroupItem value="one-time" id="r3" />
              <InputLabel htmlFor="r3">One-Time</InputLabel>
            </div>
          </RadioGroup>
        </FormTableRow>
        <FormTableRow title="Apply Coupon Code">
          <Input
            type="text"
            name="coupon_code"
            placeholder="Coupon Code"
            value={values?.coupon_code}
            onChange={handleChange}
          />
        </FormTableRow>
        <FormTableRow title="Total Payable Amount">$1234</FormTableRow>
      </div>

      <Button variant="main-revert" className="max-w-max mt-8 mb-4">
        Confirm & Upgrade
      </Button>
    </div>
  );
};

const FormTableRow = ({
  title,
  children,
}: {
  title: string;
  children: ReactNode;
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-[160px_10px_minmax(900px,_1fr)] items-center align-middle gap-4">
      <span className="text-sm md:text-base">{title}</span>
      <span className="hidden md:flex items-center justify-center leading-3 h-full">
        :
      </span>
      <span className="w-full flex items-center justify-start [&>*]:flex-1 max-w-max">
        {children}
      </span>
    </div>
  );
};

export default PlanConfirmation;
