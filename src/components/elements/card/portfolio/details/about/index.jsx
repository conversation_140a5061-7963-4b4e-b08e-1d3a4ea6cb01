import React from "react";
import { FaShareAlt } from "react-icons/fa";

const AboutSection = () => {
  const aboutInfo = [
    {
      image: "/assets/pages/portfolio/about (1).svg",
      subHeading: "Client",
      text: "Happy Foodz GmbH",
    },
    {
      image: "/assets/pages/portfolio/about (5).svg",
      subHeading: "Location",
      text: "Lorem ipsum dolor sit amet, consetetur sadipscing elitr",
    },
    {
      image: "/assets/pages/portfolio/about (4).svg",
      subHeading: "Sector",
      text: "Food",
    },
    {
      image: "/assets/pages/portfolio/about (3).svg",
      subHeading: "Audience",
      text: "B2B",
    },
    {
      image: "/assets/pages/portfolio/about (2).svg",
      subHeading: "Expertise",
      text: "Digital Strategy, Mobile App, Website Developed, Mobile App Developed, Dashboard Developed, Website Design",
    },
  ];

  return (
    <div className="about-section">
      <h2 className="font-normal mb-8 flex justify-between items-center">
        <span className="text-xl">About</span>
        <button className="bg-[#F5E1DE] px-3 py-1 rounded-md text-sm text-[#29100C] flex items-center gap-2 cursor-pointer">
          <span>Share</span>
          <FaShareAlt />
        </button>
      </h2>

      {/* List of information */}
      <div className="about-info-list space-y-4">
        {aboutInfo.map((item, index) => (
          <div
            key={index}
            className={`about-item flex items-start gap-4 pb-4 ${
              index !== aboutInfo.length - 1
                ? "border-b-2 border-[#f0f0f0]"
                : ""
            }`}
          >
            {/* Image */}
            <div className="icon flex-shrink-0 mt-1">
              <img
                src={item.image}
                alt={item.subHeading}
                className="w-6 h-6 object-contain"
              />
            </div>

            {/* Subheading and Text */}
            <div className="content flex-1">
              <h3 className="text-sm">{item.subHeading}</h3>
              {item.subHeading === "Expertise" ? (
                <div className="flex flex-wrap gap-2 mt-1">
                  {item.text.split(",").map((label, idx) => (
                    <span
                      key={idx}
                      className="bg-[#F2E5E2] px-3 py-1 rounded-md text-sm text-[#29100C]"
                    >
                      {label.trim()}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">{item.text}</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AboutSection;
