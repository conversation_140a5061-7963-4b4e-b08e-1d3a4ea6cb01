import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { TextEditor } from "@/components/ui/input/texteditor";
import { InputTargetType } from "@/components/ui/input/types";
import { generateNumberRanges } from "@/lib/utils/data";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import { companyDetails } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import {
  unitsOfMeasurement,
  workShifts,
} from "@/constants/user/seller/company/update";

const numOfEmployees = generateNumberRanges(1, 5000, [4, 20, 75, 400, 4500]);

const Details = () => {
  const dispatch = useDispatch();
  const { data: values } = useSelector(companyDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
  ) => {
    try {
      const payload: any = {};
      payload[e?.target?.name] = e?.target?.value;
      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 gap-6">
        <MultiSelect
          name="business_usps"
          onValueChange={handleChange}
          defaultValue={values?.business_usps}
          value={values?.business_usps}
          label="Company USPs"
          placeholder="Select USPs"
          variant="inverted"
          maxCount={3}
          options={[]}
          addNew
        />

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Business Overview (Short)</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Sort Details about your Business
            </span>
          </div>
          <Textarea
            name="business_overview_short"
            label="One-line business summary"
            onChange={handleChange}
            value={values?.business_overview_short}
            placeholder="Type here"
            className="min-h-20"
            containerClassName="flex-1"
            maxLength={500}
            showCharCount
          />
        </div>

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Business Overview (Detailed)</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Brief introduction, history, and company mission/vision
            </span>
          </div>
          <TextEditor
            name="business_overview_long"
            value={values?.business_overview_long}
            onChange={handleChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            label="Business Overview (Detailed)"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-center gap-1">
            <Input
              type="text"
              name="min_order_qty"
              label="Minimum Order Quantity"
              onChange={handleChange}
              value={values?.min_order_qty}
              placeholder="Enter Quantity"
              containerClassName="flex-1"
            />
            <Select
              name="min_order_qty_unit"
              label="&nbsp;"
              onChange={handleChange}
              value={values?.min_order_qty_unit}
              placeholder="Unit"
              options={unitsOfMeasurement}
              labelClassName=""
            />
          </div>

          <Input
            type="text"
            name="min_order_value"
            label="Minimum Order Value"
            onChange={handleChange}
            value={values?.min_order_value}
            placeholder="Enter Value"
            containerClassName="flex-1"
          />
          <Input
            type="text"
            name="max_order_qty"
            label="Maximum Order Quantity"
            onChange={handleChange}
            value={values?.max_order_qty}
            placeholder="Enter Quantity"
            containerClassName="flex-1"
          />
          <Select
            name="number_of_employees"
            label="Number Of Employees"
            onChange={handleChange}
            value={values?.number_of_employees}
            placeholder="Select Strength"
            options={numOfEmployees}
          />
          <Input
            type="text"
            name="annual_turn_over"
            label="Annual Turnover"
            onChange={handleChange}
            value={values?.annual_turn_over}
            placeholder="Select Turnover"
            containerClassName="flex-1"
          />

          <Input
            type="text"
            name="import_export_code"
            label="Import Export Code"
            onChange={handleChange}
            value={values?.import_export_code}
            placeholder="Enter Code"
          />
          <Select
            name="working_days"
            label="Working Days"
            onChange={handleChange}
            defaultValue={values?.working_days}
            value={values?.working_days}
            placeholder="Select Days"
            options={[
              { label: "Monday to Friday", value: "mon-fri" },
              { label: "Monday to Saturday", value: "mon-sat" },
              { label: "Monday to Sunday", value: "mon-sun" },
            ]}
          />
          <Select
            name="operating_hours"
            label="Operating hours"
            onChange={handleChange}
            defaultValue={values?.operating_hours}
            value={values?.operating_hours}
            placeholder="Select Hours"
            options={workShifts}
          />
        </div>
      </div>
    </>
  );
};

export default Details;
