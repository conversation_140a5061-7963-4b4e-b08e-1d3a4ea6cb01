import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from '../@api/update';

// Thunk to fetch RFQ list with pagination and type filter
export const getRFQList = createAsyncThunk<
  any,
  { page?: number; limit?: number; type?: string } | undefined,
  { state: RootState }
>(
  "user_buyer_rfqdetails/getRFQList",
  async (params = undefined, { rejectWithValue }) => {
    try {
      const response = await API.getRFQList(params || {});
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to fetch RFQ list", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

// Thunk to fetch a specific RFQ detail by ID
export const getRFQDetail = createAsyncThunk<
  any,
  string | undefined,
  { state: RootState }
>(
  "user_buyer_rfqdetails/getRFQDetail",
  async (id = undefined, { rejectWithValue }) => {
    try {
      const response = await API.getRFQDetail(id);
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to fetch RFQ details", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

// Thunk to update RFQ status
export const updateRFQStatus = createAsyncThunk<
  any,
  { id: string; status: string },
  { state: RootState }
>(
  "user_buyer_rfqdetails/updateRFQStatus",
  async ({ id, status }, { rejectWithValue, dispatch }) => {
    try {
      const response = await API.updateRFQStatus(id, status);
      
      toast.success("RFQ status updated successfully", {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });
      
      dispatch(getRFQList());
      
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to update RFQ status", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

// Thunk to handle proposal actions
export const handleProposalAction = createAsyncThunk<
  any,
  { action: 'accept' | 'reject'; proposalId: string; opportunityId: string },
  { state: RootState }
>(
  "user_buyer_rfqdetails/handleProposalAction",
  async ({ action, proposalId, opportunityId }, { rejectWithValue, dispatch }) => {
    try {
      const response = await API.handleProposalAction({ action, proposalId, opportunityId });
      
      toast.success(`Proposal ${action}ed successfully`, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });
      
      dispatch(getRFQDetail(opportunityId));
      
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || `Failed to ${action} proposal`, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

export const deleteRFQ = createAsyncThunk<
  any,
  { rfqId: string; filters: { page: number; limit: number; type?: string; status?: string } },
  { state: RootState }
>(
  "deleteRFQ",
  async ({ rfqId, filters }, { rejectWithValue, dispatch }) => {
    try {
      const response: any = await API.deleteRFQ(rfqId);

      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      dispatch(getRFQList(filters));

      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const updateRFQDetails = createAsyncThunk<
  any,
  { rfq_id: string; data: FormData },
  { state: RootState }
>(
  "user_buyer_rfqdetails/updateRFQDetails",
  async ({ rfq_id, data }, { rejectWithValue, dispatch }) => {
    try {
      const response: any = await API.updateRFQDetails(data, rfq_id);

      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to update RFQ", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);



export const thunks = {
  getRFQList,
  getRFQDetail,
  updateRFQStatus,
  handleProposalAction,
  deleteRFQ,
  updateRFQDetails
};