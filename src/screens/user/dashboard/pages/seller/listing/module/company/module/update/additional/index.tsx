"use client";

import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  Dropzone,
  DropzoneInputTargetType,
  InlinePlaceholder,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { InputTargetType } from "@/components/ui/input/types";
import { companyDetails } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import Clientele from "./clientele";
import Features from "./features";
import { deleteMediaHandler } from "@/lib/utils/helper";

const AdditionalInfo = () => {
  const dispatch = useDispatch();
  const { data: values } = useSelector(companyDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        if (e.target?.name === "business_logo") {
          payload[e?.target?.name] = e.target.files?.[0];
        } else {
          const fileArray = Array.from(e.target.files);
          let fileList = Array.isArray(values?.[e?.target?.name])
            ? Array.from(values?.[e?.target?.name])
            : [];

          fileList = fileList.concat(fileArray);
          payload[e?.target?.name] = fileList;
        }
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const handleDeleteMedia = async (file: any, name: string) => {
    const updatedFiles =  await deleteMediaHandler(values, file, name);
    dispatch(Actions.setFormValues({ [name]: updatedFiles }));
  };

  return (
    <>
      <div className="grid grid-cols-1 gap-9">
        <MultiSelect
          onValueChange={handleChange}
          defaultValue={values?.certifications_licences}
          value={values?.certifications_licences}
          name="certifications_licences"
          label="Certification & License"
          placeholder="Add Certifications"
          variant="inverted"
          maxCount={3}
          options={[]}
          addNew
        />
        <MultiSelect
          onValueChange={handleChange}
          defaultValue={values?.awards_recognitions}
          value={values?.awards_recognitions}
          name="awards_recognitions"
          label="Awards & Recognitions"
          placeholder="Select Business type"
          variant="inverted"
          maxCount={3}
          options={[]}
          addNew
        />

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Upload Company Photos</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Required, Image Upload (1000×1000 px, Max 4)
            </span>
          </div>
          <Dropzone
            containerClassName="flex-1"
            onChange={(e) => handleChange(e)}
            onFileDelete={(file, name) => handleDeleteMedia(file, name)}
            name="company_photos"
            value={values?.company_photos}
            preview
            inputProps={{
              multiple: true,
            }}
          >
            {values?.company_photos.length && <InlinePlaceholder />}
          </Dropzone>
        </div>
      </div>
      <Features values={values} />
      <Clientele values={values} />
    </>
  );
};

export default AdditionalInfo;
