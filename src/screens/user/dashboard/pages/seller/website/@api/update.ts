import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";
import privateApiClient from "@/services/axios/private-api-client";

const formDataConfig = {
  headers: {
    "Content-Type": "multipart/form-data",
  },
};

export const fetchPortfolioList = async ({
  searchTerm,
  page,
  limit,
}: any = {}) => {
  return await privateApiClient.get(
    apiRoutes().user.seller.company.portfolio.fetchAll,
    {
      params: { searchTerm, page, limit },
    }
  );
};

export const fetchPortfolio = async (portfolio_id: string) => {
  return await privateApiClient.get(
    `${apiRoutes().user.seller.company.portfolio.fetch}/${portfolio_id}`
  );
};

export const addUpdatePortfolio = async (
  payload: FormData,
  portfolio_id?: string
) => {
  return await formDataClient.post(
    apiRoutes().user.seller.company.portfolio.addUpdate,
    payload,
    {
      ...formDataConfig,
      params: { portfolio_id },
    }
  );
};

export const deletePortfolio = async (portfolio_id: string) => {
  return await privateApiClient.delete(
    `${apiRoutes().user.seller.company.portfolio.delete}/${portfolio_id}`
  );
};
