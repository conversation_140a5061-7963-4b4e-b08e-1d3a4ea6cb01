"use client";

import * as React from "react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  useInfiniteScroll,
  type DataActionType,
  type PaginationType,
} from "@/lib/hooks/useInfiniteScroll";
import classNames from "classnames";
import { debounce } from "lodash";
import { CheckIcon, ChevronDown, WandSparkles } from "lucide-react";
import { ErrorBoundary } from "react-error-boundary";
import { RiResetLeftFill } from "react-icons/ri";
import { DropdownListDataType } from "../../dropdown/list/types";
import { Loader } from "../../loader";
import { Popover, PopoverContent, PopoverTrigger } from "../../popover";
import { Separator } from "../../separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../tooltip";
import { InputLabel } from "../components/label";
import { InputWrapper } from "../components/wrapper";

export type InputTargetType = {
  target: {
    value: string;
    name: string;
  };
};

interface ComboboxType {
  icon?: React.ReactNode | string;
  options: DropdownListDataType[];
  optionKeys?: {
    label?: string;
    value?: string;
    icon?: string;
  };
  onChange?: (e: InputTargetType) => void;
  onSearch?: (value: string) => void;
  value?: string;
  name?: string;
  placeholder?: string;
  className?: string;
  wrapperClassName?: string;
  label?: string;
  labelClassName?: string;
  error?: string | string[];
  optionSearch?: boolean;
  checkbox?: boolean;
  required?: boolean;
  dataLoading?: boolean;
  onToggle?: (val: boolean) => void;

  /** Mandatory if need pagination */
  dataAction?: DataActionType;
  resetAction?: () => void;

  animation?: number;

  /** Mandatory if need pagination */
  pagination?: PaginationType;

  modalPopover?: boolean;
  containerClassName?: string;
  iconClassName?: string;
}

const renderIcon = (icon: any) => {
  try {
    const IconComponent = icon;
    return React.createElement(IconComponent, null);
  } catch {
    return "";
  }
};

const Combobox: React.FC<ComboboxType> = ({
  icon,
  options,
  placeholder,
  onChange,
  onSearch,
  dataLoading,
  name,
  value,
  className,
  wrapperClassName,
  label,
  labelClassName,
  optionKeys,
  error,
  checkbox,
  required,
  dataAction,
  onToggle,
  pagination,
  resetAction,
  modalPopover = false,
  containerClassName,
  iconClassName,
  animation = 0,
}) => {
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  const [selectedValue, setSelectedValue] = React.useState<string | undefined>(
    value
  );
  const [searchTerm, setSearchTerm] = React.useState("");
  const [isPopoverOpen, setIsPopoverOpen] = React.useState(false);
  const [isAnimating, setIsAnimating] = React.useState(false);
  const [commandListCount, setCommandListCount] = React.useState<number>(20);

  const { loaderRef, loading: infiniteLoading } = useInfiniteScroll({
    action: dataAction,
    pagination,
    search: searchTerm,
    resetAction: resetAction,
  });

  const handleChange = (val: string = "") => {
    try {
      if (onChange) {
        onChange({
          target: {
            value: val,
            name: name ?? "select",
          },
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getLabel = React.useCallback(
    (option: DropdownListDataType) => {
      try {
        if (optionKeys && optionKeys?.label) {
          return option?.[optionKeys?.label];
        }

        return option?.label;
      } catch {
        return option?.label;
      }
    },
    [optionKeys]
  );

  const getValue = React.useCallback(
    (option: DropdownListDataType) => {
      try {
        if (optionKeys && optionKeys?.value) {
          return option?.[optionKeys?.value];
        }

        return option?.value;
      } catch {
        return option?.value;
      }
    },
    [optionKeys]
  );

  const handleTogglePopover: any = () => {
    setIsPopoverOpen((prev) => {
      if (onToggle) {
        onToggle(!prev);
      }

      return !prev;
    });
  };

  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      setIsPopoverOpen(true);
    } else if (event.key === "Backspace" && !event.currentTarget.value) {
      const newSelectedValue = selectedValue;
      setSelectedValue(newSelectedValue);
      handleChange(newSelectedValue);
    }
  };

  const debouncedSearch = React.useMemo(
    () =>
      debounce((val: string) => {
        if (
          options?.find((item) =>
            getLabel(item)?.toLowerCase()?.includes(val?.toLowerCase())
          )
        ) {
          return;
        }

        if (onSearch) {
          onSearch(val);
        }
      }, 500),
    [options, onSearch, getLabel]
  );

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    debouncedSearch(value);
  };

  const toggleOption = (selectedVal: string) => {
    setSelectedValue(selectedVal);
    handleChange(selectedVal);
    setIsPopoverOpen(false);
  };

  const handleClear = () => {
    setSelectedValue(undefined);
    handleChange(undefined);
  };

  const handleScrollTop = () => {
    try {
      if (dropdownRef.current) {
        dropdownRef.current?.scrollTo({ top: 0, behavior: "auto" });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const selectedOption = React.useMemo(() => {
    return Array.isArray(options)
      ? options?.find((option) => getValue(option) === value)
      : null;
  }, [getValue, options, value]);

  const displayValue = React.useMemo(() => {
    return selectedOption ? getLabel(selectedOption) : String(value);
  }, [getLabel, selectedOption, value]);

  return (
    <ErrorBoundary fallback={<div>Failed to load combobox</div>}>
      <Popover
        open={isPopoverOpen}
        onOpenChange={setIsPopoverOpen}
        modal={modalPopover}
      >
        <div
          className={classNames(
            "flex flex-col gap-1.5 items-center",
            containerClassName
          )}
        >
          {label && (
            <InputLabel
              className={classNames("w-full", labelClassName)}
              required={required}
            >
              {label}
            </InputLabel>
          )}
          <PopoverTrigger asChild>
            <InputWrapper
              role="button"
              onClick={handleTogglePopover}
              className={classNames("w-full", wrapperClassName, className)}
              icon={icon}
            >
              <div className="flex items-center justify-between w-full mx-auto">
                <span
                  className={classNames(
                    "text-sm font-normal mx-1 text-ellipsis overflow-hidden whitespace-nowrap capitalize",
                    {
                      "text-font/50 mx-1": !displayValue,
                    }
                  )}
                >
                  {displayValue || placeholder || "Select"}
                </span>
                <ChevronDown className="h-4 cursor-pointer text-inherit opacity-50" />
              </div>
            </InputWrapper>
          </PopoverTrigger>

          {error && (
            <div className="flex flex-col gap-0.5 justify-start text-xs text-red-500">
              {Array.isArray(error) && error?.length > 0 ? (
                error?.map((Item, index) => (
                  <span key={index} className="w-full">
                    {Item}
                  </span>
                ))
              ) : (
                <>{error}</>
              )}
            </div>
          )}
        </div>
        <PopoverContent
          align="start"
          onEscapeKeyDown={() => setIsPopoverOpen(false)}
          ref={dropdownRef}
        >
          <Command>
            <div className="sticky -top-2 flex items-center gap-2.5 bg-white px-4 pt-6 pb-2 z-10">
              <CommandInput
                placeholder={"Search..."}
                onKeyDown={handleInputKeyDown}
                className="focus:outline-0 focus:ring-0 h-input px-3 !py-1 w-full flex-1 mx-auto"
                containerClassName="pb-1"
                onValueChange={(value) => handleSearchChange(value)}
                value={searchTerm}
              />
              {resetAction && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        title="reset"
                        className="w-max h-input aspect-square border border-light hover:bg-slate-50 rounded-md flex items-center justify-center p-1.5"
                        onClick={() => {
                          resetAction?.();
                          setSearchTerm("");
                          handleScrollTop();
                        }}
                      >
                        <RiResetLeftFill />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>Reset</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <CommandList
              onScroll={() => setCommandListCount((prev) => prev + 15)}
            >
              <CommandEmpty>No results found.</CommandEmpty>
              <CommandGroup>
                {Array.isArray(options) &&
                  options?.length > 0 &&
                  options?.slice(0, commandListCount)?.map((option, index) => {
                    const isSelected = selectedValue?.includes(
                      getValue(option)
                    );
                    return (
                      <CommandItem
                        key={getValue(option) + String(index)}
                        onSelect={() => toggleOption(getValue(option))}
                        className={classNames(
                          "cursor-pointer hover:bg-slate-50 active:bg-stone-100",
                          {
                            "bg-primary text-white": isSelected && !checkbox,
                          }
                        )}
                      >
                        {checkbox && (
                          <div
                            className={classNames(
                              "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                              isSelected
                                ? "bg-primary text-primary-foreground"
                                : "opacity-50 [&_svg]:invisible"
                            )}
                          >
                            <CheckIcon className="h-4 w-4 text-white" />
                          </div>
                        )}

                        {option?.icon && (
                          <div
                            className={classNames(
                              "menu-item-icon-container relative max-w-max [&>svg]:w-4 [&>svg]:h-4",
                              iconClassName
                            )}
                          >
                            {typeof option?.icon === "string" ? (
                              <img
                                src={option?.icon ? option?.icon : ""}
                                alt={option?.icon ?? "menu-icon"}
                                className="menu-item-icon w-4 h-4 object-contain"
                              />
                            ) : React.isValidElement(option?.icon) ? (
                              <>{option?.icon}</>
                            ) : (
                              typeof option?.icon === "object" && (
                                <>
                                  {renderIcon(option?.icon as React.ReactNode)}
                                </>
                              )
                            )}
                          </div>
                        )}
                        <span>{getLabel(option)}</span>
                      </CommandItem>
                    );
                  })}

                {(dataLoading || infiniteLoading) && (
                  <CommandItem className="relative h-16 text-center my-2">
                    <Loader
                      center
                      big={dataLoading && !infiniteLoading}
                      box={dataLoading && !infiniteLoading}
                    />
                  </CommandItem>
                )}
                {pagination?.hasNextPage && (
                  <CommandItem ref={loaderRef}>
                    <div className="h-2 w-full" />
                  </CommandItem>
                )}
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup ref={loaderRef}>
                <div className="flex items-center justify-between">
                  {selectedValue && (
                    <>
                      <CommandItem
                        onSelect={handleClear}
                        className="flex-1 justify-center cursor-pointer"
                      >
                        Clear
                      </CommandItem>
                      <Separator
                        orientation="vertical"
                        className="flex min-h-6 h-full"
                      />
                    </>
                  )}
                  <CommandItem
                    onSelect={() => setIsPopoverOpen(false)}
                    className="flex-1 justify-center cursor-pointer max-w-full"
                  >
                    Close
                  </CommandItem>
                </div>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
        {animation > 0 && selectedValue && (
          <WandSparkles
            className={classNames(
              "cursor-pointer my-2 text-foreground bg-background w-3 h-3",
              isAnimating ? "" : "text-muted-foreground"
            )}
            onClick={() => setIsAnimating(!isAnimating)}
          />
        )}
      </Popover>
    </ErrorBoundary>
  );
};

export { Combobox };
export type { ComboboxType };
