"use client";

import { ReactNode, useEffect, useState } from "react";

import { getJsonCookie } from "@/lib/utils/data";
import * as API from "../@api/auth";
import {
  AuthType,
  ContextValueType,
  createPasswordType,
  ResetPasswordType,
} from "./types";
import { Context } from "./useAuth";

const AuthProvider = ({ children }: { children?: ReactNode }) => {
  const [auth, setAuth] = useState<AuthType | null>(null);
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const useDetails = getJsonCookie("userInfo");
    if (Object.keys(useDetails).length > 0) {
      setAuth((prev: AuthType | null) => ({
        ...(prev ?? {}),
        user: useDetails,
      }));
    }
  }, []);

  const initiateRequest = () => {
    setLoading(true);
    setResponse(null);
  };

  const handleError = () => {
    // localStorage.removeItem("user_access_token");
    setAuth(null);
    setLoading(false);
  };

  const resetPassword = async (payload: ResetPasswordType) => {
    try {
      initiateRequest();
      const res: any = await API.resetPassword(payload);
      setResponse(res);
      setLoading(false);
      return res;
    } catch (error) {
      console.error("reset password failed:", error);
      setResponse(error);
      handleError();
      throw error;
    }
  };

  const createPassword = async (payload: createPasswordType) => {
    try {
      initiateRequest();
      const res: any = await API.createPassword(payload);
      setResponse(res);
      setLoading(false);
      return res;
    } catch (error) {
      console.error("create password failed:", error);
      setResponse(error);
      handleError();
      throw error;
    }
  };

  const logout = () => {
    try {
      localStorage.removeItem("user_access_token");
      setAuth(null);
      setResponse(null);
      setLoading(false);
    } catch (error) {
      console.error("logout error:", error);
      localStorage.removeItem("user_access_token");
    }
  };

  const contextValue: ContextValueType = {
    auth,
    loading,
    response,
    setResponse,
    setAuth,
    resetPassword,
    createPassword,
    logout,
  };

  return (
    <Context.Provider value={{ ...contextValue }}>{children}</Context.Provider>
  );
};

export default AuthProvider;
