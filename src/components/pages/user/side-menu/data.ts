import { MenuData } from "./types";

export const sellerMenuData: MenuData = [
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/dashboard.svg",
    label: "Insights",
    activePathname: ["/dashboard/seller"],
    pathname: "/dashboard/seller",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/listing.svg",
    label: "Listing",
    pathname: "/dashboard/seller/listing",
    activePathname: ["/dashboard/seller/listing/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
    label: "Website",
    pathname: "/dashboard/seller/website",
    activePathname: ["/dashboard/seller/website"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
    label: "Inbox",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    label: "Opportunities",
    pathname: "/dashboard/seller/opportunities",
    activePathname: ["/dashboard/seller/opportunities/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/paid.svg",
    label: "Paid Services",
    pathname: "/dashboard/seller/paid-services",
    activePathname: ["/dashboard/seller/paid-services/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/reviews.svg",
    label: "Reviews",
    pathname: "/dashboard/seller/reviews",
    activePathname: ["/dashboard/seller/reviews"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/media.svg",
    label: "Photos & Media",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/setting.svg",
    label: "Setting",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/support.svg",
    label: "Support",
  },
];

// TODO
// Path name should be all small

export const BuyerMenuData: MenuData = [
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/dashboard.svg",
    label: "Insights",
    activePathname: ["/dashboard/buyer"],
    pathname: "/dashboard/buyer",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/listing.svg",
    label: "Profile",
    pathname: "/dashboard/buyer/bussiness",
    activePathname: ["/dashboard/buyer/bussiness/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
    label: "Search & Compare",
    pathname: "/dashboard/buyer/search",
    activePathname: ["/dashboard/buyer/search/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
    label: "Inbox",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
    label: "Request Quotations",
    pathname: "/dashboard/buyer/request",
    activePathname: ["/dashboard/buyer/request/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
    label: "Manage Quotations",
    pathname: "/dashboard/buyer/manage",
    activePathname: ["/dashboard/buyer/manage/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    label: "Reviews",
    pathname: "/dashboard/buyer/reviews",
    activePathname: ["/dashboard/buyer/reviews/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    label: "Favourite",
    pathname: "/dashboard/buyer/favourite",
    activePathname: ["/dashboard/buyer/favourite/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    label: "Photos & Media",
    pathname: "/dashboard/buyer/media",
    activePathname: ["/dashboard/buyer/media/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/paid.svg",
    label: "Paid Services",
    pathname: "/dashboard/buyer/paid-services",
    activePathname: ["/dashboard/buyer/paid-services/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/reviews.svg",
    label: "Settings",
    pathname: "/dashboard/buyer/settings",
    activePathname: ["/dashboard/buyer/settings/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/media.svg",
    label: "Support",
    pathname: "/dashboard/buyer/support",
    activePathname: ["/dashboard/buyer/support/*"],
  },
];
