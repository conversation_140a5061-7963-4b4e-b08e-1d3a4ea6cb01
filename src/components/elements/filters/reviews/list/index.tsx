"use client";

import { Select } from "@/components/ui/input/select";
import classNames from "classnames";
import { FC, HTMLAttributes, ReactNode } from "react";

interface ReviewListFilterType {
  className?: string;
  rating: string;
  sort: string;
  actions: {
    onAddClick: () => void;
    onMediaClick: () => void;
    onRatingsChange: (e: unknown) => void;
    onSortChange: (e: unknown) => void;
  };
}

const ReviewListFilter: FC<ReviewListFilterType> = ({
  className,
  rating,
  sort,
  actions,
}) => {
  return (
    <div
      className={classNames(
        "z-20 relative flex items-center flex-wrap justify-between gap-4",
        className
      )}
    >
      <div className="flex items-center flex-wrap justify-start gap-2.5">
        <ActionButton className="!px-6" onClick={() => actions.onAddClick()}>
          All
        </ActionButton>
        <ActionButton onClick={() => actions.onMediaClick()}>
          With photos/videos
        </ActionButton>

        <Select
          name="ratings"
          // onChange={handleChange}
          // value={rating}
          placeholder="Ratings"
          options={[
            { label: "5 Stars", value: "5" },
            { label: "4 Stars", value: "4" },
            { label: "3 Stars", value: "3" },
            { label: "2 Stars", value: "2" },
            { label: "1 Star", value: "1" },
          ]}
          className="!rounded-full border border-stone-500 !px-3 !py-1 text-xs !h-8"
          onValueChange={(val) => actions?.onRatingsChange(val)}
        />
      </div>

      <Select
        name="sort"
        // onChange={handleChange}
        // value={sort}
        placeholder="Sort by relevance"
        options={[
          { label: "Relevance 1", value: "1" },
          { label: "Relevance 2", value: "2" },
          { label: "Relevance 3", value: "3" },
          { label: "Relevance 4", value: "4" },
          { label: "Relevance 5", value: "5" },
        ]}
        className="!rounded-full border border-stone-500 !px-3 !py-1 text-xs !h-8"
        onValueChange={(val) => actions?.onSortChange(val)}
      />
    </div>
  );
};

interface ActionButtonType extends HTMLAttributes<HTMLButtonElement> {
  className?: string;
  children: ReactNode;
}
const ActionButton: FC<ActionButtonType> = ({
  className,
  children,
  ...rest
}) => (
  <button
    className={classNames(
      "bg-white rounded-full h-8 border border-stone-500 px-3 py-1 text-xs hover:bg-slate-100",
      className
    )}
    {...rest}
  >
    {children}
  </button>
);

export default ReviewListFilter;
