"use client";

import * as React from "react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  useInfiniteScroll,
  type DataActionType,
  type PaginationType,
} from "@/lib/hooks/useInfiniteScroll";
import classNames from "classnames";
import { debounce } from "lodash";
import { CheckIcon, ChevronDown, WandSparkles } from "lucide-react";
import { ErrorBoundary } from "react-error-boundary";
import { RiResetLeftFill } from "react-icons/ri";
import { DropdownListDataType } from "../../dropdown/list/types";
import { Loader } from "../../loader";
import { Popover, PopoverContent, PopoverTrigger } from "../../popover";
import { Separator } from "../../separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../tooltip";
import { InputLabel } from "../components/label";
import { InputWrapper } from "../components/wrapper";

export type InputTargetType = {
  target: {
    value: string;
    name: string;
  };
};

interface ComboboxType {
  icon?: React.ReactNode | string;
  options: DropdownListDataType[];
  optionKeys?: {
    label?: string;
    value?: string;
    icon?: string;
  };
  onChange?: (e: InputTargetType) => void;
  onSearch?: (value: string) => void;
  value?: string;
  name?: string;
  placeholder?: string;
  className?: string;
  wrapperClassName?: string;
  label?: string;
  labelClassName?: string;
  error?: string | string[];
  optionSearch?: boolean;
  checkbox?: boolean;
  required?: boolean;
  dataLoading?: boolean;
  onToggle?: (val: boolean) => void;

  /** Mandatory if need pagination */
  dataAction?: DataActionType;
  resetAction?: () => void;

  animation?: number;

  /** Mandatory if need pagination */
  pagination?: PaginationType;

  modalPopover?: boolean;
  containerClassName?: string;
  iconClassName?: string;

  /** Pre-selected item for cases where the selected value is not in current options (e.g., pagination) */
  preSelectedItem?: DropdownListDataType;
  disabled?: boolean;
}

const renderIcon = (icon: any) => {
  try {
    const IconComponent = icon;
    return React.createElement(IconComponent, null);
  } catch {
    return "";
  }
};

const Combobox: React.FC<ComboboxType> = ({
  icon,
  options,
  placeholder,
  onChange,
  onSearch,
  dataLoading,
  name,
  value,
  className,
  wrapperClassName,
  label,
  labelClassName,
  optionKeys,
  error,
  checkbox,
  required,
  dataAction,
  onToggle,
  pagination,
  resetAction,
  modalPopover = false,
  containerClassName,
  iconClassName,
  animation = 0,
  preSelectedItem,
  disabled,
}) => {
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  const [selectedValue, setSelectedValue] = React.useState<string | undefined>(
    value
  );
  const [searchTerm, setSearchTerm] = React.useState("");
  const [isPopoverOpen, setIsPopoverOpen] = React.useState(false);
  const [isAnimating, setIsAnimating] = React.useState(false);
  const [optionData, setOptionData] = React.useState<DropdownListDataType[]>(
    []
  );
  const [isSearching, setIsSearching] = React.useState(false);
  const [customPaginationLoading, setCustomPaginationLoading] =
    React.useState(false);
  const customLoaderRef = React.useRef<HTMLDivElement>(null);
  const lastPaginationCall = React.useRef<number>(0);

  const { loading: infiniteLoading } = useInfiniteScroll({
    action: dataAction,
    pagination,
    search: searchTerm,
    resetAction: resetAction,
  });

  // Throttled pagination function to prevent duplicate calls
  const triggerPagination = React.useCallback(() => {
    if (
      !pagination?.hasNextPage ||
      infiniteLoading ||
      customPaginationLoading ||
      !dataAction
    )
      return;

    const now = Date.now();
    if (now - lastPaginationCall.current < 1000) return; // Throttle to 1 second

    lastPaginationCall.current = now;
    setCustomPaginationLoading(true);

    dataAction({
      page: (pagination.currentPage || 1) + 1,
      limit: pagination.limit || 10,
      search: searchTerm,
    }).finally(() => {
      setCustomPaginationLoading(false);
    });
  }, [
    pagination?.hasNextPage,
    pagination?.currentPage,
    pagination?.limit,
    infiniteLoading,
    customPaginationLoading,
    dataAction,
    searchTerm,
  ]);

  const handleChange = (val: string = "") => {
    try {
      if (onChange) {
        onChange({
          target: {
            value: val,
            name: name ?? "select",
          },
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getLabel = React.useCallback(
    (option: DropdownListDataType) => {
      try {
        if (optionKeys && optionKeys?.label) {
          return option?.[optionKeys?.label];
        }

        return option?.label;
      } catch {
        return option?.label;
      }
    },
    [optionKeys]
  );

  const getValue = React.useCallback(
    (option: DropdownListDataType) => {
      try {
        if (optionKeys && optionKeys?.value) {
          return option?.[optionKeys?.value];
        }

        return option?.value;
      } catch {
        return option?.value;
      }
    },
    [optionKeys]
  );

  // Merge options with preSelectedItem if needed
  React.useEffect(() => {
    let mergedOptions = [...(options ?? [])];

    // If we have a selected value but it's not in current options, add preSelectedItem
    if (selectedValue && preSelectedItem) {
      const currentValues = options?.map(getValue) || [];
      if (!currentValues.includes(selectedValue)) {
        // Add preSelectedItem to the beginning if it matches the selected value
        if (getValue(preSelectedItem) === selectedValue) {
          mergedOptions = [preSelectedItem, ...mergedOptions];
        }
      }
    }

    setOptionData(mergedOptions);
  }, [options, selectedValue, preSelectedItem, getValue]);

  // Search state management
  React.useEffect(() => {
    if (!dataLoading && isSearching) {
      setIsSearching(false);
    }
  }, [dataLoading, isSearching]);

  // Custom intersection observer for reliable pagination
  React.useEffect(() => {
    if (!pagination?.hasNextPage || infiniteLoading || !dataAction) return;

    const element = customLoaderRef.current;
    if (!element) return;

    let isLoading = false;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isLoading) {
          isLoading = true;
          triggerPagination();
          setTimeout(() => {
            isLoading = false;
          }, 1000);
        }
      },
      {
        threshold: 0.1,
        rootMargin: "100px",
        root: null,
      }
    );

    // Small delay to ensure element is properly rendered
    const timer = setTimeout(() => {
      if (element) {
        observer.observe(element);
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [
    pagination?.hasNextPage,
    pagination?.currentPage,
    pagination?.limit,
    infiniteLoading,
    dataAction,
    searchTerm,
    triggerPagination,
  ]);

  const handleTogglePopover: any = () => {
    setIsPopoverOpen((prev) => {
      if (onToggle) {
        onToggle(!prev);
      }

      return !prev;
    });
  };

  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      setIsPopoverOpen(true);
    } else if (event.key === "Backspace" && !event.currentTarget.value) {
      const newSelectedValue = selectedValue;
      setSelectedValue(newSelectedValue);
      handleChange(newSelectedValue);
    }
  };

  const debouncedSearch = React.useMemo(
    () =>
      debounce((val: string) => {
        if (!onSearch) return;

        onSearch(val);

        // Check if we have local matches first
        const hasLocalMatch = optionData?.some((item) =>
          getLabel(item)?.toLowerCase()?.includes(val?.toLowerCase())
        );

        // For pagination: if no local matches, search via API
        if (pagination && !hasLocalMatch) {
          setIsSearching(true);
          return;
        }

        // For non-paginated data: only call API if term is not found locally
        if (!pagination && !hasLocalMatch) {
          setIsSearching(true);
        }
      }, 500),
    [optionData, onSearch, getLabel, pagination]
  );

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);

    // If search is cleared, reset search state
    if (!value.trim()) {
      setIsSearching(false);
      // For pagination, call resetAction to get original data
      if (pagination && resetAction) {
        resetAction();
      }
      return;
    }

    // Trigger search for non-empty values
    debouncedSearch(value);
  };

  // Remove custom filtering - let Command handle it

  const toggleOption = (selectedVal: string) => {
    setSelectedValue(selectedVal);
    handleChange(selectedVal);
    setIsPopoverOpen(false);
  };

  const handleClear = () => {
    setSelectedValue(undefined);
    handleChange(undefined);
  };

  const handleScrollTop = () => {
    try {
      if (dropdownRef.current) {
        dropdownRef.current?.scrollTo({ top: 0, behavior: "auto" });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const selectedOption = React.useMemo(() => {
    // First check in current options
    const optionInCurrent = Array.isArray(options)
      ? options?.find((option) => getValue(option) === value)
      : null;

    if (optionInCurrent) {
      return optionInCurrent;
    }

    // If not found in current options, check preSelectedItem
    if (preSelectedItem && getValue(preSelectedItem) === value) {
      return preSelectedItem;
    }

    return null;
  }, [getValue, options, value, preSelectedItem]);

  const displayValue = React.useMemo(() => {
    if (selectedOption) {
      return getLabel(selectedOption);
    }

    // If no selectedOption but we have a value, try to get label from preSelectedItem
    if (value && preSelectedItem && getValue(preSelectedItem) === value) {
      return getLabel(preSelectedItem);
    }

    // Last fallback - return empty string instead of showing ID
    return "";
  }, [getLabel, selectedOption, value, preSelectedItem, getValue]);

  const finalData = React.useMemo(() => {
    if (searchTerm) {
      return optionData.filter((item) =>
        getLabel(item)?.toLowerCase()?.includes(searchTerm?.toLowerCase())
      );
    }

    return optionData;
  }, [optionData, searchTerm, getLabel]);

  return (
    <ErrorBoundary fallback={<div>Failed to load combobox</div>}>
      <Popover
        open={isPopoverOpen}
        onOpenChange={setIsPopoverOpen}
        modal={modalPopover}
      >
        <div
          className={classNames(
            "flex flex-col gap-1.5 items-center",
            containerClassName
          )}
        >
          {label && (
            <InputLabel
              className={classNames("w-full", labelClassName)}
              required={required}
            >
              {label}
            </InputLabel>
          )}
          <PopoverTrigger asChild>
            <InputWrapper
              role="button"
              onClick={handleTogglePopover}
              className={classNames("w-full", wrapperClassName, className)}
              icon={icon}
              disabled={disabled}
            >
              <div className="flex items-center justify-between w-full mx-auto">
                <span
                  className={classNames(
                    "text-sm font-normal mx-1 text-ellipsis overflow-hidden whitespace-nowrap capitalize",
                    {
                      "text-font/50 mx-1": !displayValue,
                    }
                  )}
                >
                  {displayValue || placeholder || "Select"}
                </span>
                <ChevronDown className="h-4 cursor-pointer text-inherit opacity-50" />
              </div>
            </InputWrapper>
          </PopoverTrigger>

          {error && (
            <div className="flex flex-col gap-0.5 justify-start text-xs text-red-500">
              {Array.isArray(error) && error?.length > 0 ? (
                error?.map((Item, index) => (
                  <span key={index} className="w-full">
                    {Item}
                  </span>
                ))
              ) : (
                <>{error}</>
              )}
            </div>
          )}
        </div>
        <PopoverContent
          align="start"
          onEscapeKeyDown={() => setIsPopoverOpen(false)}
          ref={dropdownRef}
        >
          <Command shouldFilter={false}>
            <div className="sticky -top-2 flex items-center gap-2.5 bg-white px-4 pt-6 pb-2 z-10">
              <CommandInput
                placeholder={"Search..."}
                onKeyDown={handleInputKeyDown}
                className="focus:outline-0 focus:ring-0 h-input px-3 !py-1 w-full flex-1 mx-auto"
                containerClassName="pb-1"
                onValueChange={(value) => handleSearchChange(value)}
                value={searchTerm}
              />
              {resetAction && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        title="reset"
                        className="w-max h-input aspect-square border border-light hover:bg-slate-50 rounded-md flex items-center justify-center p-1.5"
                        onClick={() => {
                          resetAction?.();
                          setSearchTerm("");
                          handleScrollTop();
                        }}
                      >
                        <RiResetLeftFill />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>Reset</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <CommandList
              onScroll={(e) => {
                // Backup scroll-based pagination trigger
                const target = e.currentTarget;
                const scrollTop = target.scrollTop;
                const scrollHeight = target.scrollHeight;
                const clientHeight = target.clientHeight;

                // Trigger when user scrolls to within 100px of bottom
                if (scrollHeight - scrollTop - clientHeight < 100) {
                  triggerPagination();
                }
              }}
            >
              <CommandGroup>
                {Array.isArray(finalData) && finalData?.length > 0
                  ? finalData?.map((option, index) => {
                      const isSelected = selectedValue?.includes(
                        getValue(option)
                      );
                      return (
                        <CommandItem
                          key={getValue(option) + String(index)}
                          onSelect={() => toggleOption(getValue(option))}
                          className={classNames(
                            "cursor-pointer hover:bg-slate-50 active:bg-stone-100",
                            {
                              "bg-primary hover:!bg-primary text-white":
                                isSelected && !checkbox,
                            }
                          )}
                        >
                          {checkbox && (
                            <div
                              className={classNames(
                                "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                                isSelected
                                  ? "bg-primary text-primary-foreground"
                                  : "opacity-50 [&_svg]:invisible"
                              )}
                            >
                              <CheckIcon className="h-4 w-4 text-white" />
                            </div>
                          )}

                          {option?.icon && (
                            <div
                              className={classNames(
                                "menu-item-icon-container relative max-w-max [&>svg]:w-4 [&>svg]:h-4",
                                iconClassName
                              )}
                            >
                              {typeof option?.icon === "string" ? (
                                <img
                                  src={option?.icon ? option?.icon : ""}
                                  alt={option?.icon ?? "menu-icon"}
                                  className="menu-item-icon w-4 h-4 object-contain"
                                />
                              ) : React.isValidElement(option?.icon) ? (
                                <>{option?.icon}</>
                              ) : (
                                typeof option?.icon === "object" && (
                                  <>
                                    {renderIcon(
                                      option?.icon as React.ReactNode
                                    )}
                                  </>
                                )
                              )}
                            </div>
                          )}
                          <span>{getLabel(option)}</span>
                        </CommandItem>
                      );
                    })
                  : !(
                      dataLoading ||
                      infiniteLoading ||
                      customPaginationLoading ||
                      isSearching
                    ) && <CommandItem>No results found.</CommandItem>}

                {(dataLoading ||
                  infiniteLoading ||
                  customPaginationLoading) && (
                  <CommandItem className="relative h-16 text-center my-2">
                    <Loader
                      center
                      // big
                      // box
                    />
                  </CommandItem>
                )}
                {pagination?.hasNextPage &&
                  !infiniteLoading &&
                  !customPaginationLoading && (
                    <CommandItem className="h-8 w-full pointer-events-none">
                      <div
                        ref={customLoaderRef}
                        className="h-8 w-full opacity-0"
                        aria-hidden="true"
                        data-pagination-trigger="true"
                      />
                    </CommandItem>
                  )}
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup>
                <div className="flex items-center justify-between">
                  {selectedValue && (
                    <>
                      <CommandItem
                        onSelect={handleClear}
                        className="flex-1 justify-center cursor-pointer"
                      >
                        Clear
                      </CommandItem>
                      <Separator
                        orientation="vertical"
                        className="flex min-h-6 h-full"
                      />
                    </>
                  )}
                  <CommandItem
                    onSelect={() => setIsPopoverOpen(false)}
                    className="flex-1 justify-center cursor-pointer max-w-full"
                  >
                    Close
                  </CommandItem>
                </div>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
        {animation > 0 && selectedValue && (
          <WandSparkles
            className={classNames(
              "cursor-pointer my-2 text-foreground bg-background w-3 h-3",
              isAnimating ? "" : "text-muted-foreground"
            )}
            onClick={() => setIsAnimating(!isAnimating)}
          />
        )}
      </Popover>
    </ErrorBoundary>
  );
};

export { Combobox };
export type { ComboboxType };
