import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from "../@api/update";

// TODO
// Change to website apis
export const fetchWebsiteDetails = createAsyncThunk<
  any,
  string,
  { state: RootState }
>(
  "websiteDetails/fetchPortfolio",
  async (portfolio_id, { rejectWithValue }) => {
    try {
      const response = await API.fetchPortfolio(portfolio_id);
      window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

// TODO
// Change to website apis
export const updateWebsite = createAsyncThunk<
  any,
  { portfolio_id?: string; data: FormData },
  { state: RootState }
>(
  "updateWebsite",
  async ({ portfolio_id, data }, { rejectWithValue, dispatch }) => {
    try {
      const response: any = await API.addUpdatePortfolio(data, portfolio_id);
      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const thunks = {
  fetchWebsiteDetails,
  updateWebsite,
};
