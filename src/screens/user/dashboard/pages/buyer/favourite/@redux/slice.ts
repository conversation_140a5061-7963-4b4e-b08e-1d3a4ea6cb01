import { createSlice, PayloadAction, AsyncThunk } from "@reduxjs/toolkit";
import { createThunkCase, APIResponseType } from "@/redux/helper/thunk";
import { getFavourites, addFavourite } from "./thunk";

interface FavouritesStateType {
  getFavourites: APIResponseType;  // State for fetching favourites list
  addFavourite: APIResponseType;   // State for adding a favourite item
}

// Initial API state for both fetching and adding favourites
const APIState: FavouritesStateType = {
  getFavourites: {
    data: null,
    loading: false,
    meta: null,
  },
  addFavourite: {
    data: null,
    loading: false,
    meta: null,
  },
};

const initialState = {
  ...APIState,
} satisfies FavouritesStateType as FavouritesStateType;

export const slice = createSlice({
  name: "user_buyer_favourites",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // Add async thunk handlers for fetching favourites and adding favourite
    createThunkCase(builder, getFavourites as AsyncThunk<any, any, any>, APIState);
    createThunkCase(builder, addFavourite as AsyncThunk<any, any, any>, APIState);
  },
});

export default slice.reducer;
