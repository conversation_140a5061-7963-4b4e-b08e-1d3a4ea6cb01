"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { FaCaretDown } from "react-icons/fa";
import { HiOutlineAdjustmentsHorizontal } from "react-icons/hi2";

import { Input } from "@/components/ui/input/text";
import classNames from "classnames";
import { ChangeEvent, useState } from "react";
import { FiSearch } from "react-icons/fi";
import AdvancedFilters from "./filters";

const AdvancedFilter = () => {
  const [searchValue, setSearchValue] = useState<string>("");

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      setSearchValue(e?.target?.value);
    } catch (error) {
      console.error(error);
    }
  };

  const filterCount = 3;
  const resultsCount = "24,867";

  return (
    <Sheet>
      <SheetTrigger asChild>
        <button className="text-xs py-1 px-4 hover:bg-slate-50 transition-all flex items-center justify-center gap-2 border border-stone-500 rounded-md h-input">
          <div className="flex items-center gap-1.5">
            <HiOutlineAdjustmentsHorizontal size={20} />
            <span className="text-sm font-medium">Advanced Filter</span>
          </div>
          <FaCaretDown />
        </button>
      </SheetTrigger>
      <SheetContent className="!bg-white !p-0">
        <div className="h-full flex flex-col justify-between gap-4 [&>*]:w-full">
          <SheetHeader className="px-6 py-3 border-b border-slate-300">
            <SheetTitle>Advanced Filter</SheetTitle>
          </SheetHeader>
          <div className="flex-1 flex flex-col justify-between gap-4 [&>*]:w-full h-full overflow-hidden">
            <div className="flex-1 overflow-auto">
              <div className="my-4 flex items-center justify-between gap-4 px-6 text-sm">
                <span>{filterCount} Selected</span>
                <button className="text-main hover:text-red-500 hover:underline font-bold outline-0 border-0">
                  Clear all
                </button>
              </div>

              <div
                className={classNames(
                  "w-full mx-auto sticky top-0 bg-white px-6 my-5"
                )}
              >
                <Input
                  type="text"
                  placeholder="Location"
                  onChange={handleSearch}
                  value={searchValue}
                  wrapperClassName="rounded-full"
                  icon={<FiSearch size={6} />}
                />
              </div>

              <AdvancedFilters />
            </div>

            <SheetFooter className="px-6 my-6">
              <SheetClose asChild>
                <Button
                  type="submit"
                  className="!bg-main !text-white w-full hover:!border hover:!border-main hover:!text-main hover:!bg-white"
                >
                  See {resultsCount} Results
                </Button>
              </SheetClose>
            </SheetFooter>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default AdvancedFilter;
