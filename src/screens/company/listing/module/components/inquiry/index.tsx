import QuoteRequestForm from "@/components/elements/form/inquiry";
import { But<PERSON> } from "@/components/ui/button";
import { lazy } from "react";

type ContactNowModalProps = {
  className?: string;
};

const formData = [
  {
    title: "Request Proposal",
    component: lazy(
      () => import("@/components/elements/form/inquiry/category")
    ),
  },
  {
    title: "Request Proposal",
    component: lazy(() => import("@/components/elements/form/inquiry/product")),
    footerType: "navigate",
  },
  {
    title: "Request Proposal",
    component: lazy(() => import("@/components/elements/form/inquiry/units")),
    footerType: "navigate",
  },
  {
    title: "Request Proposal",
    component: lazy(
      () => import("@/components/elements/form/inquiry/delivered")
    ),
    footerType: "navigate",
  },
  {
    title: "Request Proposal",
    component: lazy(() => import("@/components/elements/form/inquiry/budget")),
    footerType: "navigate",
  },
  {
    title: "Request Proposal",
    component: lazy(
      () => import("@/components/elements/form/inquiry/deadline")
    ),
    footerType: "navigate",
  },
  {
    title: "Request Proposal",
    component: lazy(
      () => import("@/components/elements/form/inquiry/attachments")
    ),
    footerType: "navigate",
  },
  {
    title: "Request Proposal",
    component: lazy(() => import("@/components/elements/form/inquiry/contact")),
    footerType: "navigate",
  },
  {
    title: "Request Proposal",
    component: lazy(() => import("@/components/elements/form/inquiry/otp")),
    footerType: "navigate",
  },
  {
    title: "Request Proposal",
    component: lazy(() => import("@/components/elements/form/inquiry/thanks")),
    footerType: "last",
  },
];

const ContactNowModal = ({ className }: ContactNowModalProps) => {
  return (
    <QuoteRequestForm
      data={formData}
      className={`!flex flex-col items-start [&>*]:w-full overflow-visible ${className ?? ''}`}
      stepCount
    >
      <Button variant="outline" size="sm" className="border-main text-main">
        Contact Now
      </Button>
    </QuoteRequestForm>
  );
};

export default ContactNowModal;
