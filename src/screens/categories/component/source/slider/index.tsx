"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ReactNode } from "react";

const ProductSlider = ({ children }: { children: ReactNode | ReactNode[] }) => {
  return (
    <>
      {/* Mobile Carousel - Only visible on small screens */}
      <div className="block ml-2 md:hidden">
        <Carousel
          opts={{
            align: "center",
            dragFree: true
          }}
          className="w-full"
        >
          <CarouselContent className="">
            {Array.isArray(children) ? (
              children.map((child, index) => (
                <CarouselItem
                  key={index}
                  className="pl-2 basis-[32%] "
                >
                  {child}
                </CarouselItem>
              ))
            ) : (
              <CarouselItem className="pl-2 basis-1/2 sm:basis-1/3">
                {children}
              </CarouselItem>
            )}
          </CarouselContent>
          {/* <CarouselPrevious className="bg-white shadow-xl border border-gray-100 rounded-full h-11 w-11 left-0 focus:ring-1 focus:ring-gray-200 focus:outline-none" />
          <CarouselNext className="bg-white shadow-xl border border-gray-100 rounded-full h-11 w-11 right-0 focus:ring-1 focus:ring-gray-200 focus:outline-none" /> */}
        </Carousel>
      </div>

      {/* Desktop Carousel - Hidden on small screens */}
      <div className="hidden md:block">
        <Carousel
          opts={{
            align: "start",
          }}
          className="w-full static"
        >
          <CarouselContent className="">
            {Array.isArray(children) ? (
              children.map((child, index) => (
                <CarouselItem
                  key={index}
                  className=" md:basis-[21%] lg:basis-[12.5%] xl:basis-[10%] 2xl:basis-[6.25%] my-1"
                >
                  {child}
                </CarouselItem>
              ))
            ) : (
              Array.from({ length: 50 }).map((_, index) => (
                <CarouselItem
                  key={index}
                  className="pl-[10px] sm:basis md:basis-[20%] lg:basis-1/5 xl:basis-1/6 2xl:basis-[10%] my-1"
                >
                  {children}
                </CarouselItem>
              ))
            )}
          </CarouselContent>
          <CarouselPrevious className="bg-white shadow-xl border border-gray-100 rounded-full !h-12 !w-12 -left-10 focus:ring-1 focus:ring-gray-200 focus:outline-none" />
          <CarouselNext className="bg-white shadow-xl border border-gray-100 rounded-full !h-12 !w-12 top-1 -right-10 focus:ring-1 focus:ring-gray-200 focus:outline-none" />
        </Carousel>
      </div>
    </>
  );
};

export default ProductSlider;