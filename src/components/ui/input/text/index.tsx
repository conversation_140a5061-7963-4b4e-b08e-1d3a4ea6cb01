import classNames from "classnames";
import * as React from "react";
import InputLabel from "../components/label";
import { InputWrapper } from "../components/wrapper";
import CountrySelect, { CountrySelectType } from "../country-select";

export interface InputType extends React.ComponentProps<"input"> {
  icon?: React.ReactNode | string;
  action?: React.ReactNode;
  containerClassName?: string;
  wrapperClassName?: string;
  labelClassName?: string;
  label?: string;
  countrySelector?: CountrySelectType;
  showCharCount?: boolean;
  error?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputType>(
  (
    {
      className,
      containerClassName,
      wrapperClassName,
      labelClassName,
      label,
      type,
      id,
      icon,
      action,
      countrySelector,
      maxLength,
      value = "",
      showCharCount,
      error,
      ...props
    },
    ref
  ) => {
    const [passShow, setPassShow] = React.useState<boolean>(false);
    const uniqueId = React.useId();

    const inputType = (): string => {
      try {
        if (type === "password" && passShow) {
          return "text";
        }

        return type || "text";
      } catch {
        return "text";
      }
    };

    return (
      <div
        className={classNames(
          "flex flex-col gap-1.5 items-center [&>*]:w-full",
          containerClassName
        )}
      >
        {label && (
          <InputLabel htmlFor={id ?? uniqueId} className={labelClassName}>
            {label}
          </InputLabel>
        )}
        <InputWrapper
          icon={icon}
          className={wrapperClassName}
          error={Boolean(error)}
        >
          {countrySelector && (
            <CountrySelect
              comboInput
              dialCode
              placeholder={countrySelector?.placeholder ?? "Phone"}
              {...countrySelector}
              className="max-w-20"
            />
          )}
          <input
            type={inputType()}
            className={classNames(
              "flex-1 flex h-full w-full bg-transparent text-base transition-colors border-0 outline-0 focus:border-0 focus:outline-0 focus:ring-0 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm p-0",
              className
            )}
            ref={ref}
            value={value}
            maxLength={maxLength}
            {...props}
          />

          {type === "password" && (
            <button
              type="button"
              className="text-sm font-semibold drop-shadow"
              onClick={() => setPassShow((prev) => !prev)}
            >
              {passShow ? "Hide" : "Show"}
            </button>
          )}

          {action && <React.Fragment>{action}</React.Fragment>}
        </InputWrapper>

        {error && (
          <div className="flex flex-col gap-0.5 justify-start text-xs text-red-500">
            {Array.isArray(error) && error?.length > 0 ? (
              error?.map((Item, index) => (
                <span key={index} className="w-full">
                  {Item}
                </span>
              ))
            ) : (
              <>{error}</>
            )}
          </div>
        )}

        {showCharCount && value && maxLength && (
          <div className="flex justify-end text-xs text-gray-500">
            max {value?.toString()?.length}/{maxLength} chars
          </div>
        )}
      </div>
    );
  }
);
Input.displayName = "Input";

export { Input };
