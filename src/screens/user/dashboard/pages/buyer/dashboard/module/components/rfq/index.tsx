"use client";

import LineChart from "@/components/elements/chart/line-chart";
import DateRangeInput, { DateRange } from "@/components/ui/input/date range";
import classNames from "classnames";
import { addDays } from "date-fns";
import { FC, useState } from "react";

interface RfqReceivedType {
  className?: string;
}

const RfqReceived: FC<RfqReceivedType> = ({ className }) => {
  const [date, setDate] = useState<DateRange | undefined>({
    from: addDays(new Date(), -20),
    to: new Date(),
  });

  return (
    <div className={classNames("bg-white shadow-box p-4", className)}>
      <div className="flex items-center flex-wrap gap-4 justify-between">
        <div className="flex items-center gap-8">
          <div className="flex flex-col items-start gap-0.5 text-nowrap">
            <span className="text-[#14AA1F] text-2xl font-semibold">500+</span>
            <span className="text-stone-500 text-sm">This Month</span>
          </div>
          <div className="flex flex-col items-start gap-0.5 text-nowrap">
            <span className="text-[#9F9694] text-2xl font-semibold">2837+</span>
            <span className="text-stone-500 text-sm">Last Month</span>
          </div>
        </div>

        <div className="w-max">
          <DateRangeInput
            placeholder="Pick a date range"
            onChange={(e) => setDate(e?.target?.value)}
            value={date}
          />
        </div>
      </div>
      <LineChart height={275} className="mt-6 -mb-6" />
    </div>
  );
};

export default RfqReceived;
