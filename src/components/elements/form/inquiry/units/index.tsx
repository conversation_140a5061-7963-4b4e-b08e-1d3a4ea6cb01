"use client";

import { HighlightedText } from "@/components/elements/text/highlight";
import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { Input } from "@/components/ui/input/text";
import { locations } from "@/constants/location";
import { getFilteredData } from "@/lib/utils/data";
import { useState } from "react";
import { FiSearch } from "react-icons/fi";

const UnitsSelection = () => {
  const [searchValue, setSearchValue] = useState<string>("");

  const filteredMenu = getFilteredData({
    data: locations,
    compareValue: searchValue,
    compareParams: ["title", "code"],
  });

  const finalData = Boolean(searchValue) ? filteredMenu : locations;

  return (
    <div className="min-w-[40vw] min-h-[30vh] h-full flex-1 p-10 flex items-start justify-center">
      <div className="w-full flex flex-col items-center justify-center gap-4">
        <h5 className="text-2xl font-bold">
          <HighlightedText
            title="How many units do you need?"
            highlight={["units"]}
          />
        </h5>

        <div className="w-full">
          <Dropdown
            content={
              <DropdownList
                data={finalData}
                className="min-w-full w-min border-0"
              />
            }
            contentClassName="w-full z-20 mt-0"
          >
            <Input
              type="text"
              placeholder="e.g. 50 units"
              wrapperClassName="rounded-full"
              icon={<FiSearch size={6} />}
              value={searchValue}
              onChange={(e) => setSearchValue(e?.target?.value)}
            />
          </Dropdown>
        </div>
      </div>
    </div>
  );
};

export default UnitsSelection;
