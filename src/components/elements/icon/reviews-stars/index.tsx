import classNames from "classnames";
import { Star } from "lucide-react";

const ReviewsStars = ({ rating }: { rating: number }) => {
  return (
    <div className="flex items-center gap-0.5">
      {[...Array(+5).keys()].map((_, index) => {
        const highlight = index < rating;
        return (
          <Star
            key={index}
            className={classNames({
              "fill-main/40 stroke-main/70": highlight,
            })}
          />
        );
      })}
    </div>
  );
};

export default ReviewsStars;
