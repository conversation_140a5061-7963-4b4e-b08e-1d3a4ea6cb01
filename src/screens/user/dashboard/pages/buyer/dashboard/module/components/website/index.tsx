const WebsitePerformance = () => {
  return (
    <main className="bg-white shadow-box py-4">
      <div className="px-4">
        <h3 className="text-base font-medium mb-3"> Website</h3>
        <p className="text-xs text-stone-400 mb-6">
          Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
          nonumy
        </p>
      </div>

      <div className="border-t border-stone-200 flex flex-col items-start gap-4 pt-4 px-4">
        <COuntCard title="5,1355,75" subText="Website Clicks" />
        <div className="w-full bg-stone-200 h-[1px]" />
        <COuntCard title="4,14895,75" subText="Website Visitors" />
      </div>
    </main>
  );
};

const COuntCard = ({ title, subText }: any) => {
  return (
    <div className="w-full flex flex-col items-start gap-1.5">
      <p className="text-sm text-stone-600">{subText}</p>
      <h3 className="text-2xl font-medium"> {title}</h3>
    </div>
  );
};

export default WebsitePerformance;
