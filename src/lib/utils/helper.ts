import { store } from "@/redux/store";
import { deleteMedia } from "@/screens/user/dashboard/@redux/thunk";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { toast } from "sonner";

dayjs.extend(duration);

const cutText = (text: string, length: number) => {
  if (text.split(" ").length > 1) {
    const string = text.substring(0, length);
    const splitText = string.split(" ");
    splitText.pop();
    return splitText.join(" ") + "...";
  } else {
    return text;
  }
};

const formatDate = (date: string, format: string) => {
  return dayjs(date).format(format);
};

const capitalizeFirstLetter = (string: string) => {
  if (string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  } else {
    return "";
  }
};

const onlyNumber = (string: string) => {
  if (string) {
    return string.replace(/\D/g, "");
  } else {
    return "";
  }
};

const formatCurrency = (number: number) => {
  if (number) {
    const formattedNumber = number.toString().replace(/\D/g, "");
    const rest = formattedNumber.length % 3;
    let currency = formattedNumber.substr(0, rest);
    const thousand = formattedNumber.substr(rest).match(/\d{3}/g);
    let separator;

    if (thousand) {
      separator = rest ? "," : "";
      currency += separator + thousand.join(",");
    }

    return currency;
  } else {
    return "";
  }
};

const timeAgo = (time: string) => {
  const date = new Date((time || "").replace(/-/g, "/").replace(/[TZ]/g, " "));
  const diff = (new Date().getTime() - date.getTime()) / 1000;
  const dayDiff = Math.floor(diff / 86400);

  if (isNaN(dayDiff) || dayDiff < 0 || dayDiff >= 31) {
    return dayjs(time).format("MMMM DD, YYYY");
  }

  return (
    (dayDiff === 0 &&
      ((diff < 60 && "just now") ||
        (diff < 120 && "1 minute ago") ||
        (diff < 3600 && Math.floor(diff / 60) + " minutes ago") ||
        (diff < 7200 && "1 hour ago") ||
        (diff < 86400 && Math.floor(diff / 3600) + " hours ago"))) ||
    (dayDiff === 1 && "Yesterday") ||
    (dayDiff < 7 && dayDiff + " days ago") ||
    (dayDiff < 31 && Math.ceil(dayDiff / 7) + " weeks ago")
  );
};

const diffTimeByNow = (time: string) => {
  const startDate = dayjs(dayjs().format("YYYY-MM-DD HH:mm:ss").toString());
  const endDate = dayjs(dayjs(time).format("YYYY-MM-DD HH:mm:ss").toString());

  const duration = dayjs.duration(endDate.diff(startDate));
  const milliseconds = Math.floor(duration.asMilliseconds());

  const days = Math.round(milliseconds / 86400000);
  const hours = Math.round((milliseconds % 86400000) / 3600000);
  let minutes = Math.round(((milliseconds % 86400000) % 3600000) / 60000);
  const seconds = Math.round(
    (((milliseconds % 86400000) % 3600000) % 60000) / 1000
  );

  if (seconds < 30 && seconds >= 0) {
    minutes += 1;
  }

  return {
    days: days.toString().length < 2 ? "0" + days : days,
    hours: hours.toString().length < 2 ? "0" + hours : hours,
    minutes: minutes.toString().length < 2 ? "0" + minutes : minutes,
    seconds: seconds.toString().length < 2 ? "0" + seconds : seconds,
  };
};

const isset = (obj: object | string) => {
  if (obj !== null && obj !== undefined) {
    if (typeof obj === "object" || Array.isArray(obj)) {
      return Object.keys(obj).length;
    } else {
      return obj.toString().length;
    }
  }

  return false;
};

const toRaw = (obj: object) => {
  return JSON.parse(JSON.stringify(obj));
};

const randomNumbers = (from: number, to: number, length: number) => {
  const numbers = [0];
  for (let i = 1; i < length; i++) {
    numbers.push(Math.ceil(Math.random() * (from - to) + to));
  }

  return numbers;
};

const stringToHTML = (arg: string) => {
  const parser = new DOMParser(),
    DOM = parser.parseFromString(arg, "text/html");
  return DOM.body.childNodes[0] as HTMLElement;
};

const slideUp = (
  el: HTMLElement,
  duration = 300,
  callback = (el: HTMLElement) => {}
) => {
  try {
    el.style.transitionProperty = "height, margin, padding";
    el.style.display = "none";
    el.style.transitionDuration = duration + "ms";
    el.style.height = el.offsetHeight + "px";
    void el.offsetHeight;
    el.style.overflow = "hidden";
    el.style.height = "0";
    el.style.paddingTop = "0";
    el.style.paddingBottom = "0";
    el.style.marginTop = "0";
    el.style.marginBottom = "0";
    window.setTimeout(() => {
      el.style.display = "none";
      el.style.removeProperty("height");
      el.style.removeProperty("padding-top");
      el.style.removeProperty("padding-bottom");
      el.style.removeProperty("margin-top");
      el.style.removeProperty("margin-bottom");
      el.style.removeProperty("overflow");
      el.style.removeProperty("transition-duration");
      el.style.removeProperty("transition-property");
      callback(el);
    }, duration);
  } catch (error) {
    console.error(error);
  }
};

const slideDown = (
  el: HTMLElement,
  duration = 300,
  callback = (el: HTMLElement) => {}
) => {
  try {
    el.style.removeProperty("display");
    let display = window.getComputedStyle(el).display;
    if (display === "none") display = "block";
    el.style.display = display;
    const height = el.offsetHeight;
    el.style.overflow = "hidden";
    el.style.height = "0";
    el.style.paddingTop = "0";
    el.style.paddingBottom = "0";
    el.style.marginTop = "0";
    el.style.marginBottom = "0";
    void el.offsetHeight;
    el.style.transitionProperty = "height, margin, padding";
    el.style.transitionDuration = duration + "ms";
    el.style.height = height + "px";
    el.style.removeProperty("padding-top");
    el.style.removeProperty("padding-bottom");
    el.style.removeProperty("margin-top");
    el.style.removeProperty("margin-bottom");
    window.setTimeout(() => {
      el.style.display = "block";
      el.style.removeProperty("height");
      el.style.removeProperty("overflow");
      el.style.removeProperty("transition-duration");
      el.style.removeProperty("transition-property");
      callback(el);
    }, duration);
  } catch (error) {
    console.error(error);
  }
};

export const areSameFile = (a: File, b: File) => {
  try {
    return a.name === b.name && a.size === b.size && a.type === b.type;
  } catch {
    return false;
  }
};

export const deleteMediaHandler = async (
  values: Record<string, any>,
  file: any,
  name: string
) => {
  try {
    const existingFiles =
      values?.[name] && Array.isArray(values?.[name])
        ? [...values?.[name]]
        : [];

    let updatedFiles: any[] = [];
    if (file.hasOwnProperty("media_id")) {
      const res: any = store.dispatch(deleteMedia(file?.media_id));
      if (res?.type === "success") {
        updatedFiles = existingFiles.filter(
          (f: any) => f?.media_id !== file?.media_id
        );
      }
    } else if (file instanceof File) {
      updatedFiles = existingFiles.filter((f: any) => !areSameFile(f, file));
    }

    return Array.isArray(updatedFiles) && updatedFiles.length
      ? updatedFiles
      : null;
  } catch {
    toast.error("Failed to delete media", {
      classNames: {
        toast: "!bg-red-500 !text-white",
      },
    });
  }
};
/**
 * Normalize input to string or array of strings.
 *
 * @param input — any value (string, array, object array, etc.)
 * @param outputType — desired output: "string" or "stringArray"
 * @returns string | string[] | null
 */
export function normalizeToStrings(
  input: unknown,
  outputType: "string" | "stringArray" = "string"
): string | string[] | undefined {
  if (typeof input === "string") {
    return outputType === "string" ? input : [input];
  }

  if (Array.isArray(input)) {
    const arr = input as any[];

    if (arr.every((x) => typeof x === "string")) {
      return outputType === "string" ? (arr[0] ?? null) : (arr as string[]);
    }

    if (
      arr.every(
        (x) => x !== null && typeof x === "object" && Object.hasOwn(x, "_id")
      )
    ) {
      const ids = arr.map((x: any) => String((x as any)._id));
      return outputType === "string" ? (ids[0] ?? null) : ids;
    }
  }

  if (typeof input === "object" && input?.hasOwnProperty("_id")) {
    return outputType === "string"
      ? String((input as any)?._id)
      : [String((input as any)?._id)];
  }

  return undefined;
}

type AnyObject = Record<string, any>;

export const removeEmptyValues = (obj: AnyObject): AnyObject => {
  if (typeof obj !== "object" || obj === null) return obj;

  return Object.entries(obj).reduce((acc: AnyObject, [key, value]) => {
    const isEmptyObject =
      typeof value === "object" &&
      !Array.isArray(value) &&
      value !== null &&
      Object.keys(value).length === 0;

    const isEmptyArray = Array.isArray(value) && value.length === 0;

    if (
      value === null ||
      value === undefined ||
      value === "" ||
      isEmptyArray ||
      isEmptyObject
    ) {
      return acc; // Skip this key
    }

    // Recursively clean nested objects
    if (typeof value === "object" && !Array.isArray(value)) {
      const cleaned = removeEmptyValues(value);
      if (Object.keys(cleaned).length > 0) {
        acc[key] = cleaned;
      }
    } else {
      acc[key] = value;
    }

    return acc;
  }, {});
};

export const removeKeysFromObject = (
  obj: AnyObject,
  keysToRemove: string[]
): AnyObject => {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (!keysToRemove.includes(key)) {
      acc[key] = value;
    }
    return acc;
  }, {} as AnyObject);
};

export {
  capitalizeFirstLetter,
  cutText,
  diffTimeByNow,
  formatCurrency,
  formatDate,
  isset,
  onlyNumber,
  randomNumbers,
  slideDown,
  slideUp,
  stringToHTML,
  timeAgo,
  toRaw,
};
