import { createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "@/redux/store";
import { toast } from "sonner";
import * as API from "../@api/update";
import { Actions } from "./slice";

interface AddRFQResponse {
  data: any;
  meta: {
    code: number;
    status: number;
    message: string;
  };
}

// Async thunk to post a new RFQ (Request For Quote)
export const postRFQ = createAsyncThunk<AddRFQResponse, FormData, { state: RootState }>(
  "user_buyer_rfq/postRFQ",
  async (formValue, { rejectWithValue, dispatch }) => {
    try {
      // Call API to add RFQ with form data
      const res = await API.addRFQ(formValue);
      const data = res?.data;

      // Show success toast notification
      toast.success(data?.meta?.message || "RFQ posted successfully", {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      // Reset product and service details form state
      dispatch(Actions.resetProductDetails());
      dispatch(Actions.resetServiceDetails());

      return data;
    } catch (error: any) {
      // Extract and show error message toast
      const errorMessage =
        error?.response?.data?.meta?.message || error?.message || "Something went wrong";

      toast.error(errorMessage, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      // Reject thunk with error payload
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const thunks = {
  postRFQ,
};
