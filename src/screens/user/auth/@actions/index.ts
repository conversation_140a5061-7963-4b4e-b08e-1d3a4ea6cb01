"use server";

import { getErrorObject } from "@/services/axios/helper/error";
import { jwtVerify, SignJWT } from "jose";
import { cookies } from "next/headers";
import * as API from "../@api/auth";
import {
  createPasswordType,
  LoginType,
  RegisterType,
  ResetPasswordType,
} from "../@context/types";

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET);

export async function setToken(token: string) {
  if (!token) throw new Error("No token provided");

  const cookieStore = await cookies();

  cookieStore.set("user_access_token", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    path: "/",
    maxAge: 60 * 60 * 24 * 7,
  });
}

export async function getToken(): Promise<string | null> {
  const cookieStore = await cookies();
  const token = cookieStore.get("user_access_token")?.value;
  if (!token) return null;

  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload?.access_token as string;
  } catch {
    return null;
  }
}

export async function setCurrentUser(user: any) {
  const cookieStore = await cookies();

  cookieStore.set("userInfo", JSON.stringify(user), {
    httpOnly: false,
    path: "/",
    maxAge: 60 * 60 * 24 * 7,
  });

  const jwt = await new SignJWT({ user })
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("7d")
    .sign(JWT_SECRET);

  cookieStore.set("userInfoToken", jwt, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    path: "/",
    maxAge: 60 * 60 * 24 * 7,
  });
}

export async function getCurrentUser() {
  const cookieStore = await cookies();
  const token = cookieStore.get("userInfoToken")?.value;
  if (!token) return null;

  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload?.user as any;
  } catch {
    return null;
  }
}

export async function userAuthAction(access_token: string) {
  try {
    const res: any = await API.userAuth(access_token);
    await setCurrentUser(res?.data);
    return res;
  } catch (error) {
    return getErrorObject(error);
  }
}

export async function registerAction(payload: RegisterType) {
  try {
    const res: any = await API.register(payload);
    await setToken(res?.meta?.token);
    return res;
  } catch (error) {
    return getErrorObject(error);
  }
}

export async function loginAction(payload: LoginType) {
  try {
    const res: any = await API.login(payload);
    await setToken(res?.meta?.token);
    return res;
  } catch (error) {
    return getErrorObject(error);
  }
}

export async function resetPasswordAction(payload: ResetPasswordType) {
  try {
    const res: any = await API.resetPassword(payload);
    return res;
  } catch (error) {
    return getErrorObject(error);
  }
}

export async function createPasswordAction(payload: createPasswordType) {
  try {
    const res: any = await API.createPassword(payload);
    return res;
  } catch (error) {
    return getErrorObject(error);
  }
}

export async function logoutAction() {
  try {
    const cookieStore = await cookies();
    cookieStore.set("user_access_token", "", {
      httpOnly: true,
      path: "/",
      expires: new Date(0),
    });
    cookieStore.set("userInfo", "", {
      httpOnly: false,
      path: "/",
      expires: new Date(0),
    });
    cookieStore.set("userInfoToken", "", {
      httpOnly: true,
      path: "/",
      expires: new Date(0),
    });
  } catch (error) {
    return getErrorObject(error);
  }
}
