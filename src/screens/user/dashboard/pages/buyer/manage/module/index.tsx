'use client';

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { usePathname, useRouter, useParams } from "next/navigation";

const Listing = () => {
  const router = useRouter();

    useEffect(() => {
    router.push("/dashboard/buyer/manage/RFQList");
  }, [router]);

  return null;
};

const RFQTabsData = [
  {
    title: "RFQ Details",
    path: "/dashboard/buyer/manage/rfqlist",
  },
  {
    title: "Open",
    path: "/dashboard/buyer/manage/open",
  },
  {
    title: "Close",
    path: "/dashboard/buyer/manage/close",
  },
  {
    title: "Pending",
    path: "/dashboard/buyer/manage/pending",
  },
];

const DetailTabsData = [
  {
    title: "RFQ Details",
    path: "/dashboard/buyer/manage/detail",
  },
  {
    title: "Responses",
    path: "/dashboard/buyer/manage/response",
  },
];

export const ListingTabs = () => {
  const router = useRouter();
  const pathname = usePathname();

  const params = useParams();
 const id = params?.id as string;

  const isDetailView = pathname.includes('/detail') || pathname.includes('/response');
  const tabsData = isDetailView ? DetailTabsData : RFQTabsData;

  const initialActive = tabsData?.[0]?.path;
  const [active, setActive] = useState<string | undefined>(initialActive);

  useEffect(() => {
    if (pathname.match(/\/detail\/[^/]+/)) {
      setActive(DetailTabsData[0].path);
      return;
    }

    if (pathname.match(/\/response\/[^/]+/)) {
      setActive(DetailTabsData[1].path);
      return;
    }

    const current = tabsData?.find((item) => pathname?.includes(item?.path));
    setActive(current?.path);
  }, [pathname, tabsData]);

  const getCurrentId = () => {
    const match = pathname.match(/\/(detail|response)\/([^/]+)/);
    return match ? match[2] : '';
  };

  return (
    <Tabs
      defaultValue={initialActive}
      value={active}
      className="overflow-auto no-scrollbar py-1.5 border-b border-stone-300 mb-5"
    >
      <TabsList className="flex-nowrap">
        {tabsData?.map((tab, index) => {
          return (
            <TabsTrigger
              key={index}
              value={tab?.path}
              className="flex-1 uppercase before:data-[state=active]:-bottom-2.5"
              onClick={() => {
                if (isDetailView && (id || pathname.includes('/detail/') || pathname.includes('/response/'))) {
                  const currentId = id || getCurrentId();
                  if (currentId) {
                    if (tab.title === "RFQ Details") {
                      router.push(`/dashboard/buyer/manage/detail/${currentId}`);
                    } else if (tab.title === "Responses") {
                      router.push(`/dashboard/buyer/manage/response/${currentId}`);
                    }
                  } else {
                    router.push(tab?.path);
                  }
                } else {
                  router.push(tab?.path);
                }
              }}
            >
              {tab?.title}
            </TabsTrigger>
          );
        })}
      </TabsList>
    </Tabs>
  );
};

export default Listing;
