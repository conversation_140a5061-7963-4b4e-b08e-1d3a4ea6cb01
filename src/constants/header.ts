export const staticPageUrl = (path: string): string =>
  `${process.env.NEXT_PUBLIC_STATIC_PAGES_BASE_URL}/${path}`;

export const resources = [
  {
    label: "Industry News & Insights",
    path: staticPageUrl(""),
  },
  {
    label: "Tips for sellers",
    path: staticPageUrl("tips-for-sellers"),
  },
  {
    label: "Guides for buyers",
    path: staticPageUrl("tips-for-buyers"),
  },
  {
    label: "Articles",
    path: staticPageUrl("articles"),
  },
  {
    label: "Blog",
    path: staticPageUrl("blog"),
  },
];

export const helpCenter = [
  {
    label: "FAQs",
    path: staticPageUrl("frequently-asked-questions"),
  },
  {
    label: "User Guides",
    path: staticPageUrl("tips-for-buyers"),
  },
  {
    label: "How to Use the Platform",
    path: staticPageUrl(""),
  },
  {
    label: "Feedback",
    path: staticPageUrl("feedback"),
  },
  {
    label: "Complaints",
    path: staticPageUrl("feedback"),
  },
];

export const navigation = [
  {
    label: "Discover",
  },
];

export const navigationCategory = [
  {
    label: "Agriculture",
  },
  {
    label: "Manufacturing",
  },
  {
    label: "Construction & Real Estate",
  },
  {
    label: "IT & Technology",
  },
  {
    label: "Retail & Wholesale",
  },
  {
    label: "Transport & Logistics",
  },
  {
    label: "Healthcare & Pharmaceuticals",
  },
  {
    label: "Energy & Environment",
  },
];

export const navigationSubCategory = [
  {
    label: "Crops & Farming",
  },
  {
    label: "Livestock & Poultry",
  },
  {
    label: "Agricultural Machinery",
  },
];

export const navigationMenu = [
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Feed Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Fiber Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Fuel Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Rabi Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Zaid Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Cash Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Food Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Tuber Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Pulses",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Aquatic Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Nut Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Fallow Crops",
  },
  {
    icon: "https://images.nationalgeographic.org/image/upload/t_edhub_resource_key_image/v1638892233/EducationHub/photos/crops-growing-in-thailand.jpg",
    label: "Extra Crops",
  },
];

export const navigationData = navigation?.map((itemMain) => ({
  ...itemMain,
  menus: navigationCategory?.map((itemPar, index) => ({
    ...itemPar,
    menus: navigationSubCategory?.map((itemSub, indexSub) => ({
      ...itemSub,
      label: itemSub?.label + " " + index,
      menus: navigationMenu?.map((itemNav) => ({
        ...itemNav,
        label: itemNav?.label + " " + indexSub,
      })),
    })),
  })),
}));
