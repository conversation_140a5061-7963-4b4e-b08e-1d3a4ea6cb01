import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from "../@api/update";
import { Actions } from "./slice";
import { getDynamicFields } from "@/screens/user/dashboard/pages/seller/@api/common";

export const getProducts = createAsyncThunk<
  any,
  { searchTerm?: string; page?: string; limit?: string } | undefined,
  { state: RootState }
>("productsList", async (pagination, { rejectWithValue, dispatch }) => {
  try {
    const response: any = await API.getProducts({
      searchTerm: pagination?.searchTerm,
      page: pagination?.page,
      limit: pagination?.limit,
    });

    window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
    dispatch(Actions.setCurrentPage(String(response?.meta?.pagination?.page)));

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const getProduct = createAsyncThunk<any, string, { state: RootState }>(
  "productDetails/getProduct",
  async (product_id, { rejectWithValue }) => {
    try {
      const response = await API.getProduct(product_id);
      window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const getProductDynamicFields = createAsyncThunk<
  any,
  { sub_sub_category: string },
  { state: RootState }
>("dynamicFields", async ({ sub_sub_category }, { rejectWithValue }) => {
  try {
    const response: any = await getDynamicFields({
      entityType: "product",
      sub_sub_category: sub_sub_category,
    });

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const getProductSuggestions = createAsyncThunk<
  any,
  { searchTerm: string },
  { state: RootState }
>("productSuggestions", async ({ searchTerm }, { rejectWithValue }) => {
  try {
    const response: any = await API.getProductSuggestions({ searchTerm });

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const getProductPreValues = createAsyncThunk<
  any,
  { suggestionId: string },
  { state: RootState }
>(
  "productDetails/getProductPreValues",
  async ({ suggestionId }, { rejectWithValue }) => {
    try {
      const response: any = await API.getProductPreValues({ suggestionId });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const updateProductDetails = createAsyncThunk<
  any,
  { product_id?: string; data: FormData },
  { state: RootState }
>("updateProductDetails", async ({ product_id, data }, { rejectWithValue }) => {
  try {
    const response: any = await API.updateProductDetails(data, product_id);
    toast.success(response?.meta?.message as string, {
      classNames: {
        toast: "!bg-green-500 !text-white",
      },
    });

    return response;
  } catch (error: any) {
    toast.error(error?.meta?.message as string, {
      classNames: {
        toast: "!bg-red-500 !text-white",
      },
    });

    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const deleteProduct = createAsyncThunk<
  any,
  string,
  { state: RootState }
>("deleteProduct", async (product_id, { rejectWithValue, dispatch }) => {
  try {
    const response: any = await API.deleteProduct(product_id);
    toast.success(response?.meta?.message as string, {
      classNames: {
        toast: "!bg-green-500 !text-white",
      },
    });
    dispatch(getProducts());
    return response;
  } catch (error: any) {
    toast.error(error?.meta?.message as string, {
      classNames: {
        toast: "!bg-red-500 !text-white",
      },
    });

    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const thunks = {
  getProduct,
  getProducts,
  updateProductDetails,
  getProductDynamicFields,
  getProductSuggestions,
  getProductPreValues,
};
