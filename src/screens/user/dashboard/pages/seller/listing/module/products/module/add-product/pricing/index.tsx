import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import { unitsOfMeasurement } from "@/constants/user/seller/company/update";
import { productDetails } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";

const Pricing = () => {
  const dispatch = useDispatch();
  const { data: values } = useSelector(productDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values[e?.target?.name])
          ? Array.from(values[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setProductDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="min-w-min flex flex-col text-start gap-2">
        <div className="flex items-center justify-start gap-1.5">
          <InputLabel>Currency & Price</InputLabel>
          <Badge
            className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
            variant="destructive"
          >
            Required
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Input
          type="text"
          name="mrp_price"
          label="MRP"
          onChange={handleChange}
          value={values?.mrp_price ?? ""}
          placeholder="Min Price"
          containerClassName="flex-1"
        />
        <Input
          type="text"
          name="discounted_price"
          label="Discounted Price"
          onChange={handleChange}
          value={values?.discounted_price ?? ""}
          placeholder="Enter Price"
          containerClassName="flex-1"
        />
        <Input
          type="text"
          label="Minimum Order Quantity"
          placeholder="Enter Quantity"
          name="min_order_qty"
          value={values?.min_order_qty ?? ""}
          onChange={handleChange}
          containerClassName="flex-1"
        />
        <Input
          type="text"
          label="Minimum Order Value"
          placeholder="Enter Value"
          name="min_order_value"
          value={values?.min_order_value ?? ""}
          onChange={handleChange}
        />
        <Select
          name="stock"
          label="Stock Availability"
          onChange={handleChange}
          value={values?.stock ?? ""}
          placeholder="Select Availability"
          options={[
            { label: "In Stick", value: true },
            { label: "Out Of Stock", value: false },
          ]}
        />
        <Select
          name="bulk_discount"
          label="Bulk Discount Options"
          onChange={handleChange}
          value={values?.bulk_discount ?? ""}
          placeholder="Select"
          options={[
            { label: "Yes", value: true },
            { label: "No", value: false },
          ]}
        />
        {/* Moved to dynamic */}
        {/* <Input
          type="text"
          label="Size / Dimension"
          placeholder="Enter Size"
          name="size"
          value={values?.size ?? ""}
          onChange={handleChange}
        />
        <div className="flex items-center gap-1">
          <Input
            type="text"
            label="Weight"
            placeholder="Enter Weight"
            name="weight"
            value={values?.weight ?? ""}
            onChange={handleChange}
            containerClassName="flex-1"
          />
          <Select
            label="&nbsp;"
            name="weight_unit"
            value={values?.weight_unit ?? ""}
            placeholder="unit"
            onChange={handleChange}
            options={[
              { label: "Gm", value: "gm" },
              { label: "Kg", value: "kg" },
            ]}
          />
        </div> */}
        <MultiSelect
          label="Packaging Type"
          placeholder="Select Type"
          name="packaging_type"
          defaultValue={values?.packaging_type ?? []}
          onValueChange={handleChange}
          options={[]}
          maxCount={3}
          addNew
        />
        <MultiSelect
          label="Certification"
          placeholder="Select Certification"
          name="certification"
          defaultValue={values?.certification ?? []}
          onValueChange={handleChange}
          options={[]}
          maxCount={3}
          addNew
        />
      </div>
    </>
  );
};

export default Pricing;
