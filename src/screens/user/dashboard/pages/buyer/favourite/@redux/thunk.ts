import { createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "@/redux/store";
import * as API from "../@api/update";
import { toast } from "sonner";

// Async thunk to add an item to favourites (product, service, or business)
export const addFavourite = createAsyncThunk<
  any,
  {
    favoriteId: string;
    favoriteType: "product" | "service" | "business";
    tags?: string[];
    notes?: string;
    priority?: number;
    filters?: {
      favoriteType: "product" | "service" | "business";
      priority?: number;
      page?: number;
      limit?: number;
    };
  },
  { state: RootState }
>(
  "user_buyer_favourites/addFavourite",
  async ({ filters, ...payload }, { rejectWithValue, dispatch }) => {
    try {
      const response = await API.addFavourite(payload);

      // Show success toast message
      toast.success(response?.meta?.message || "Added to favourites", {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      // Optionally refresh favourites list after adding
      if (filters) {
        dispatch(getFavourites(filters));
      }

      return response;
    } catch (error: any) {
      // Show error toast message
      toast.error(error?.meta?.message || "Failed to add favourite", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

// Async thunk to fetch the list of favourite items based on filters
export const getFavourites = createAsyncThunk<
  any,
  { favoriteType: "product" | "service" | "business"; priority?: number; page?: number; limit?: number },
  { state: RootState }
>(
  "user_buyer_favourites/getFavourites",
  async (params, { rejectWithValue }) => {
    try {
      const response = await API.getFavourites(params);
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to fetch favourites", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);


// Async thunk to delete a favourite by ID and refresh the list afterwards
export const deleteFavourite = createAsyncThunk<
  any,
  {
    favouriteId: string;
    filters: {
      favoriteType: "product" | "service" | "business";
      priority?: number;
      page?: number;
      limit?: number;
    };
  },
  { state: RootState }
>(
  "user_buyer_favourites/deleteFavourite",
  async ({ favouriteId, filters }, { rejectWithValue, dispatch }) => {
    try {
      const response = await API.deleteFavourite(favouriteId);

      // Show success toast on successful deletion
      toast.success(response?.meta?.message || "Favourite removed", {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      // Refresh the favourites list with current filters
      dispatch(getFavourites(filters));

      return response;
    } catch (error: any) {
      // Show error toast on failure
      toast.error(error?.meta?.message || "Failed to remove favourite", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);
