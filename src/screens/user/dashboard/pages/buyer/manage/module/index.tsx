"use client";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const Listing = () => {
  const router = useRouter();

  useEffect(() => {
    router.push("/dashboard/buyer/manage/RFQList");
  }, [router]);

  return null;
};

const RFQTabsData = [
  {
    title: "RFQ Details",
    path: "/dashboard/buyer/manage/RFQList",
  },
  {
    title: "Open",
    path: "/dashboard/buyer/manage/Open",
  },
  {
    title: "Close",
    path: "/dashboard/buyer/manage/Close",
  },
  {
    title: "Pending",
    path: "/dashboard/buyer/manage/Pending",
  },
];

const DetailTabsData = [
  {
    title: "RFQ Details",
    path: "/dashboard/buyer/manage/detail",
  },
  {
    title: "Responses",
    path: "/dashboard/buyer/manage/response",
  },
];

export const ListingTabs = () => {
  const router = useRouter();
  const pathname = usePathname();

  const isDetailView =
    pathname.includes("/detail") || pathname.includes("/response");
  const tabsData = isDetailView ? DetailTabsData : RFQTabsData;

  const initialActive = tabsData?.[0]?.path;
  const [active, setActive] = useState<string | undefined>(initialActive);

  useEffect(() => {
    // If we're in detail view with an ID, we need to set the active tab to the detail tab
    if (pathname.match(/\/detail\/[^/]+/)) {
      setActive(DetailTabsData[0].path);
      return;
    }

    // Otherwise, find the current tab based on the pathname
    const current = tabsData?.find((item) => pathname?.includes(item?.path));
    setActive(current?.path);
  }, [pathname, tabsData]);

  return (
    <Tabs
      defaultValue={initialActive}
      value={active}
      className="overflow-auto no-scrollbar py-1.5 border-b border-stone-300 mb-5"
    >
      <TabsList className="flex-nowrap">
        {tabsData?.map((tab, index) => {
          return (
            <TabsTrigger
              key={index}
              value={tab?.path}
              className="flex-1 uppercase before:data-[state=active]:-bottom-2.5"
              onClick={() => {
                // If we're in detail view with an ID, preserve the ID when switching tabs
                if (isDetailView && pathname.includes("/detail/")) {
                  const id = pathname.split("/").pop();
                  if (tab.title === "RFQ Details") {
                    router.push(`/dashboard/buyer/manage/detail/${id}`);
                  } else if (tab.title === "Responses") {
                    router.push(`/dashboard/buyer/manage/response/${id}`);
                  }
                } else {
                  router.push(tab?.path);
                }
              }}
            >
              {tab?.title}
            </TabsTrigger>
          );
        })}
      </TabsList>
    </Tabs>
  );
};

export default Listing;
