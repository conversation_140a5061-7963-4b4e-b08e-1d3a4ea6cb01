import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import { thunks } from "./thunk";
import { AsyncThunk } from "@reduxjs/toolkit";
import { ProductUpdateValuesType } from "../module/update-product/types";
import { ServiceUpdateValuesType } from "../module/update-service/types";


export type RFQUpdateValuesType = ProductUpdateValuesType | ServiceUpdateValuesType;
interface APTStateType {
  getRFQList: APIResponseType;
  getRFQDetail: APIResponseType;
  updateRFQStatus: APIResponseType;
  updateRFQDetails: APIResponseType;
}

const APIState: APTStateType = {
  getRFQList: {
    data: null,
    loading: false,
    meta: null,
  },
  getRFQDetail: {
    data: null,
    loading: false,
    meta: null,
  },
  updateRFQStatus: {
    data: null,
    loading: false,
    meta: null,
  },
  updateRFQDetails: {
    data: null,
    loading: false,
    meta: null,
  }
};

const initialState = {
  ...APIState,
} satisfies APTStateType as APTStateType;

export const slice = createSlice({
  name: "user_buyer_rfqdetails",
  initialState,
  reducers: {
    setRFQDetails: (
      state,
      action: PayloadAction<Partial<RFQUpdateValuesType>>
    ) => {
      state.updateRFQDetails.data = {
        ...(state.updateRFQDetails.data || {}),
        ...action.payload,
      };
    },

    // ✅ Add this reducer to clear the form
    resetRFQDetails: (state) => {
      state.updateRFQDetails = {
        data: null,
        loading: false,
        meta: null,
      };
    },
  },

  extraReducers: (builder) => {
    Object.entries(thunks).forEach(([_, thunk]) => {
      createThunkCase(builder, thunk as AsyncThunk<any, any, any>, APIState);
    });
  },
});


export const { setRFQDetails, resetRFQDetails } = slice.actions;
export const Actions = slice.actions;
export default slice.reducer;