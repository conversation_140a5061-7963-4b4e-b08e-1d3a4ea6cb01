const defaultState = {
  error: true,
  type: "error",
  message: "something went wrong",
  meta: null,
  name: "error",
  code: "unknown",
};

export const handleError = (res: any) => {
  try {
    if (res) {
      if (res?.code === "ERR_NETWORK") {
        return {
          error: true,
          message: res?.message ?? "something went wrong",
          status: res?.response?.status ?? "unknown",
          code: res?.code ?? "ERR_NETWORK",
        };
      } else {
        return {
          error: true,
          ...(res?.response?.data?.errors ?? {}),
          message:
            res?.response?.data?.errors?.message ||
            res?.response?.data?.message ||
            "something went wrong",
          status: res?.response?.status ?? "unknown",
          code:
            res?.response?.data?.errors?.errorCode ||
            res?.response?.data?.errorCode,
        };
      }
    } else {
      return defaultState;
    }
  } catch (err) {
    console.error(err);
    return defaultState;
  }
};

export interface ErrorObjectType {
  error: boolean;
  type: string;
  meta: any;
  message: string;
  name: string;
  code: string;
}

export const getErrorObject = (error: any): ErrorObjectType => {
  try {
    return {
      error: error?.error,
      type: error?.type,
      meta: error?.meta,
      message: error?.message,
      name: error?.name,
      code: error?.code,
    };
  } catch {
    return defaultState;
  }
};
