'use client';

import { Select } from "@/components/ui/input/select";
import React, { useState } from 'react';
import { InputTargetType } from "@/components/ui/input/select";

interface CategoryProps {
  onCategoryChange?: (category: string) => void;
  selectedCategory?: string;
}

const CategoryDropdown: React.FC<CategoryProps> = ({ onCategoryChange, selectedCategory = "" }) => {
  const [category, setCategory] = useState(selectedCategory);
  
  const handleCategoryChange = (e: InputTargetType) => {
    setCategory(e.target.value);
    if (onCategoryChange) {
      onCategoryChange(e.target.value);
    }
  };
  
  const categoryOptions = [
    { label: "Technology", value: "technology" },
    { label: "Healthcare", value: "healthcare" },
    { label: "Education", value: "education" },
    { label: "Finance", value: "finance" },
    { label: "Retail", value: "retail" },
  ];
  
  return (
    <div className="flex items-center justify-center bg-white min-h-[50vh] w-full">
      <div className="flex flex-col items-center p-4 sm:p-6 max-w-xs sm:max-w-sm md:max-w-md">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 sm:mb-4 text-center">Category / Industry</h1>
        <p className="text-sm sm:text-base text-center mb-4 sm:mb-6 px-2 sm:px-4">Select the industry or category of the service you need.</p>
        <div className="w-full">
          <Select
            placeholder="Category"
            options={categoryOptions}
            value={category}
            onChange={handleCategoryChange}
          />
        </div>
      </div>
    </div>
  );
};

export default CategoryDropdown;