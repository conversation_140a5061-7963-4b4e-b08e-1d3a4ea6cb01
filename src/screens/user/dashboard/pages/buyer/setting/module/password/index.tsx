'use client';

import React, { useState } from "react";
import { Input } from "@/components/ui/input/text";
import { Button } from "@/components/ui/button";
import { useDispatch, useSelector } from "react-redux";
import { changePassword } from "../../@redux/thunk";
import { RootDispatch } from "@/redux/store";
import { getChangePasswordSelector } from "../../@redux/selector";

const ChangePassword = () => {
  const dispatch = useDispatch<RootDispatch>();
  const loading = useSelector(getChangePasswordSelector)?.loading;

  const [formData, setFormData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    
    if (formData.newPassword !== formData.confirmPassword) {
      console.error("New and confirm passwords do not match");
      return;
    }

    await dispatch(changePassword({
      old_password: formData.oldPassword,
      new_password: formData.newPassword,
      confirm_password: formData.confirmPassword,
    }));

    setFormData({
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    });
  };

  return (
    <div className="bg-white rounded-md shadow-sm p-6 pt-6 mt-8  pb-4 max-w-7xl mx-auto">
      <h2 className="text-lg font-medium mb-6 border-b pb-4">Change Password</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Input
            label="Old Password"
            id="oldPassword"
            name="oldPassword"
            value={formData.oldPassword}
            onChange={handleChange}
            type="password"
            placeholder="Input text"
            wrapperClassName="border rounded-md"
            containerClassName="mb-4"
          />
        </div>
        <div>
          <Input
            label="New Password"
            id="newPassword"
            name="newPassword"
            value={formData.newPassword}
            onChange={handleChange}
            type="password"
            placeholder="Input text"
            wrapperClassName="border rounded-md"
            containerClassName="mb-4"
          />
        </div>
        <div>
          <Input
            label="Confirm New Password"
            id="confirmPassword"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            type="password"
            placeholder="Input text"
            wrapperClassName="border rounded-md"
            containerClassName="mb-4"
          />
        </div>
        <div className="bg-white p-4 mt-2 -ml-4 mb-0 rounded-lg">
          <Button
            variant="main-revert"
            size="default"
            type="submit"
            className="w-20 rounded-md"
            disabled={loading}
          >
            {loading ? "Saving..." : "Save"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ChangePassword;