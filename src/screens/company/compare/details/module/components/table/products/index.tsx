"use client";

import { Button } from "@/components/ui/button";
import { selectedColumn } from "@/screens/company/compare/@redux/selectors";
import { useSelector } from "react-redux";

const CompareProducts = () => {
  const current = useSelector(selectedColumn);

  return (
    <section id="products" className="md:shadow-box rounded-lg">
      <div className="px-7 py-3 text-lg md:text-base font-bold border-b-none md:border-b border-stone-200">
        Main Products
      </div>

      <div className="px-4 md:px-0 flex flex-col md:flex-row items-start gap-4 md:gap-0 rounded-lg overflow-auto">
        <div className="flex-1 hidden lg:block w-full" />
        {Array.from({ length: 4 }).map((_, index) => {
          if (window?.innerWidth < 768 && current !== index) return null;

          return <ProductCard key={index} />;
        })}
      </div>
    </section>
  );
};

const ProductCard = () => {
  return (
    <div className="relative flex flex-col gap-5 items-start [&>*]:w-full w-full flex-1 md:border-l border-stone-200 p-6 bg-white shadow-box md:shadow-none rounded-lg">
      <div className="bg-[#E6E6E6] px-4 py-1 -translate-x-6 md:hidden block">
        <span className="text-sm font-bold">
          Accenture Solutions Private Limited.
        </span>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 gap-3">
        {Array.from({ length: 6 }).map((_, index) => (
          <button
            key={index}
            className="text-start hover:underline text-brown text-sm hover:drop-shadow text-nowrap"
          >
            Electronics & Gadgets
          </button>
        ))}
      </div>

      <Button variant="main" className="max-w-max">
        See all
      </Button>
    </div>
  );
};

export default CompareProducts;
