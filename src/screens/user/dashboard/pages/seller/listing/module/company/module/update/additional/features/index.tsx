"use client";

import { But<PERSON> } from "@/components/ui/button";
import InputLabel from "@/components/ui/input/components/label";
import { Input } from "@/components/ui/input/text";
import { removeAtIndex } from "@/lib/utils/data";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import { Plus, Trash2, X } from "lucide-react";
import { ChangeEvent, FC, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";

const Features = ({ values }: { values: any }) => {
  const dispatch = useDispatch();

  const featureLimit =
    Array.isArray(values?.why_choose_us) && values?.why_choose_us?.length > 5;

  const featurePayload = useMemo(() => {
    return "";
  }, []);

  useEffect(() => {
    if (!values?.why_choose_us) {
      const payload: any = { ...values, why_choose_us: [featurePayload] };
      dispatch(Actions.setFormValues(payload));
    }
  }, [dispatch, featurePayload, values, values?.why_choose_us]);

  const handleAddFeatures = () => {
    try {
      if (featureLimit) return;

      const payload: any = {
        ...values,
        why_choose_us: Array.isArray(values?.why_choose_us)
          ? [...values?.why_choose_us, featurePayload]
          : [featurePayload],
      };
      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const handleRemoveFeature = (index: number) => {
    try {
      if (values?.why_choose_us?.length === 1 && index === 0) {
        const payload: any = {
          ...values,
          why_choose_us: Array.isArray(values?.why_choose_us)
            ? [...values?.why_choose_us]
            : [],
        };
        payload.why_choose_us[index] = "";

        dispatch(Actions.setFormValues(payload));
      } else {
        const payload: any = {
          ...values,
          why_choose_us: Array.isArray(values?.why_choose_us)
            ? [...values?.why_choose_us]
            : [],
        };
        payload.why_choose_us = removeAtIndex(payload.why_choose_us, index);

        dispatch(Actions.setFormValues(payload));
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleFeatureChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number
  ) => {
    try {
      const value = e.target.value;

      if (!values?.why_choose_us) values.why_choose_us = [];

      const payload: any = {
        ...values,
        why_choose_us: Array.isArray(values?.why_choose_us)
          ? [...values?.why_choose_us]
          : [],
      };
      if (!payload?.why_choose_us[index]) {
        payload.why_choose_us[index] = "";
      }
      payload.why_choose_us[index] = value;

      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col items-start gap-6">
      <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
        <div className="flex items-center justify-start gap-1.5">
          <InputLabel>Why Choose Us</InputLabel>
          <span className="text-stone-500 text-[0.7rem]">
            (6 lines, 20 chars each)
          </span>
        </div>
      </div>

      <div className="w-full grid grid-col-1 gap-6 md:grid-cols-2">
        {Array.isArray(values?.why_choose_us) &&
          values?.why_choose_us?.length > 0 &&
          values?.why_choose_us?.map((item: string, index: number) => {
            return (
              <FeatureItem
                key={index}
                dataLength={values?.why_choose_us?.length}
                first={index === 0 && values?.why_choose_us?.length === 1}
                onDelete={() => handleRemoveFeature(index)}
                onChange={(e) => handleFeatureChange(e, index)}
                feature={item}
              />
            );
          })}
      </div>
      {!featureLimit && (
        <Button
          onClick={() => handleAddFeatures()}
          className="rounded-md flex items-center gap-2 border-dashed"
        >
          <Plus /> Add Feature
        </Button>
      )}
    </div>
  );
};

interface FeatureType {
  onDelete: () => void;
  onChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  feature: string;
  first: boolean;
  dataLength: number;
}

const FeatureItem: FC<FeatureType> = ({
  first,
  onDelete,
  feature,
  dataLength,
  onChange,
}) => {
  return (
    <div className="flex-1 flex items-start gap-1">
      <div className="me-2 flex items-center justify-center rounded-sm bg-slate-200 shadow w-9 h-9">
        <img
          src={"/assets/pages/accrordians/1.svg"}
          alt="feature-icon"
          className="object-contain"
        />
      </div>
      <div className="w-max flex-1 flex flex-col items-center justify-start gap-2 whitespace-nowrap text-ellipsis overflow-hidden">
        <Input
          type="text"
          name="title"
          value={feature}
          onChange={onChange}
          placeholder="Feature Title"
          containerClassName="w-full"
        />

        {/* <Textarea
          name="description"
          onChange={onChange}
          value={feature?.description}
          placeholder="Enter Description"
          className="min-h-20"
          labelClassName="mb-1 ml-5"
          containerClassName="w-full"
        /> */}
      </div>
      {!(dataLength === 1 && !(feature || feature)) && (
        <button
          onClick={() => onDelete()}
          className="flex items-center justify-center rounded-sm bg-white hover:bg-stone-100 transition-all w-auto h-9 [&>svg]:w-6 [&>svg]:h-6"
          title={first ? "Clear" : "Delete"}
        >
          {first ? <X /> : <Trash2 />}
        </button>
      )}
    </div>
  );
};

export default Features;
