"use client";

import { Checkbox } from "@/components/ui/input/checkbox";
import {
  Sheet,
  Sheet<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { sortingFilterData } from "@/constants/filters";
import { Fragment, useState } from "react";
import { FaSort } from "react-icons/fa";

const SortFilterMobile = () => {
  const [selected, setSelected] = useState<any>({
    title: "Relevance",
  });

  return (
    <Sheet>
      <SheetTrigger asChild>
        <button className="flex-1 flex items-center gap-2 justify-center hover:bg-stone-100 py-2">
          <FaSort size={20} />
          <span className="text-sm">Sort</span>
        </button>
      </SheetTrigger>
      <SheetContent side="bottom" className="!bg-white !p-0">
        <div className="h-full flex flex-col justify-between gap-4 [&>*]:w-full">
          <SheetHeader className="px-6 py-3 border-b border-slate-300">
            <SheetTitle className="text-start">Sort By</SheetTitle>
          </SheetHeader>
          <div className="flex-1 flex flex-start flex-col justify-between gap-3 [&>*]:w-full mb-3 text-start">
            {sortingFilterData?.map((fit, index) => {
              return (
                <Fragment key={index}>
                  <SheetClose
                    className="px-6 py-1 flex items-center justify-start"
                    asChild
                  >
                    <div className="flex items-center gap-0.5 [&_.checkbox]:w-8">
                      <Checkbox checked={selected?.title === fit?.label} />
                      <span
                        role="button"
                        className="text-start w-full"
                        onClick={() => setSelected(fit)}
                      >
                        {fit?.label}
                      </span>
                    </div>
                  </SheetClose>
                </Fragment>
              );
            })}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default SortFilterMobile;
