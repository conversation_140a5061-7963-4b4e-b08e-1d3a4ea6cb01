"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { FiEdit3 } from "react-icons/fi";
import Ratings from "./components/ratings";
import RatingGallery from "./components/gallery";
import ReviewComments from "./components/comments";

const Reviews = () => {
  return (
    <div className="relative w-full">
      <div className="flex items-center justify-between gap-4 mb-8">
        <h3 className="font-bold">Reviews and Ratings</h3>
        <Button variant="ghost-brown" className="flex items-center gap-2">
          <FiEdit3 /> <span>Write Review</span>
        </Button>
      </div>

      <Ratings />
      <RatingGallery />
      <ReviewComments />
    </div>
  );
};

export default Reviews;
