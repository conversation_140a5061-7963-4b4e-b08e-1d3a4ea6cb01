"use client";

import React, { useEffect, useRef, useState } from "react";
// import img1 from "./assests/Group 5399.png";
// import img2 from "./assests/Group 5400.png";
// import img3 from "./assests/Group 5423.png";
// import img4 from "./assests/full-length-senior-woman-using-phone-while-sitting-table.png";
// import img5 from "./assests/view-smartphone-female-hands-with-empty-screen-with-crowd-people-copyspace-your-individual-text.png";
// import img6 from "./assests/view-smartphone-female-hands-with-empty-screen-with-crowd-people-copyspace-your-individual-text.png";
import { useRouter } from "next/navigation";

interface TabData {
  id: string;
  title: string;
  image: string;
  description: string;
}

// TODO
// Correct the image files

const TabComponent: React.FC = () => {
  const router = useRouter();
  const tabs: TabData[] = [
    {
      id: "create-profile",
      title: "Find Reliable Suppliers",
      image: "assets/pages/buyer/Group 5423.png",
      description:
        "Sign up and set up your business profile in just a few steps.",
    },
    {
      id: "list-products",
      title: "Save Time and Effort",
      image: "assets/pages/buyer/full-length-senior-woman-using-phone-while-sitting-table.png",
      description: "Add your products or services with detailed descriptions.",
    },
    {
      id: "connect-buyers",
      title: "Compare Offers",
      image: "assets/pages/buyer/view-smartphone-female-hands-with-empty-screen-with-crowd-people-copyspace-your-individual-text.png",
      description:
        "Interact with potential customers interested in your offerings.",
    },
    {
      id: "grow-network",
      title: "Global Research, Local Support",
      image: "assets/pages/buyer/Group 5399.png",
      description: "Expand your business connections and reach more customers.",
    },
    {
      id: "receive-inquiries",
      title: "Completely Free For Buyers",
      image: "assets/pages/buyer/Group 5400.png",
      description: "Get notified when buyers show interest in your products.",
    },
  ];

  const [activeTab, setActiveTab] = useState<string>(tabs[0].id);
  const activeTabData = tabs.find((tab) => tab.id === activeTab) || tabs[0];
const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const mobileTabContentRef = useRef<HTMLDivElement>(null);
  const desktopTabContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + window.innerHeight / 2;
      
      const mobileContainerRect = mobileTabContentRef.current?.getBoundingClientRect();
      const desktopContainerRect = desktopTabContentRef.current?.getBoundingClientRect();
      
      const isMobileView = window.innerWidth < 1024; // lg breakpoint is typically 1024px
      const activeContainer = isMobileView ? mobileContainerRect : desktopContainerRect;
      
      if (!activeContainer) return;
      
      const containerTop = activeContainer.top + window.scrollY;
      const containerBottom = containerTop + activeContainer.height;
      
      if (scrollPosition >= containerTop && scrollPosition <= containerBottom) {
        let closestTab = tabs[0].id;
        let closestDistance = Infinity;
        
        tabRefs.current.forEach((tabRef, index) => {
          if (!tabRef) return;
          
          const rect = tabRef.getBoundingClientRect();
          const tabCenter = rect.top + rect.height / 2;
          const distance = Math.abs(tabCenter - window.innerHeight / 2);
          
          if (distance < closestDistance) {
            closestDistance = distance;
            closestTab = tabs[index].id;
          }
        });
        
        setActiveTab(closestTab);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
   const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    
    // Smooth scroll to the selected tab
    const selectedTabRef = tabRefs.current[tabs.findIndex(tab => tab.id === tabId)];
    if (selectedTabRef) {
      selectedTabRef.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const renderMobileTabButton = (tab: TabData) => {
    return (
      <button
        key={tab.id}
        onClick={() => handleTabClick(tab.id)}
        className={`whitespace-nowrap 
          px-3 py-3 text-xs            // default (very small screens)
          sm:px-3 sm:py-2 sm:text-sm   // ≥640px
          md:px-4 md:py-2 md:text-base // ≥768px
          lg:px-5 lg:py-3 lg:text-base // ≥1024px
          xl:px-6 xl:py-3 xl:text-lg   // ≥1280px
          rounded-md min-w-max 
          ${
            activeTab === tab.id
              ? "bg-[#29100C] text-white font-semibold"
              : "bg-white border border-gray-300 text-black"
          }`}
      >
        {tab.title}
      </button>
    );
  };

  return (
    <>
      {/* Main Heading - Added above the TabComponent */}
     <div className="text-center max-w-4xl mx-auto px-4 mb-10 mt-7 lg:mt-16">
        <h2 className="text-[16px] md:text-3xl lg:text-3xl font-bold text-gray-900">
          Source products, post requirements, & connect
          <br />
          with verified sellers effortlessly.
        </h2>
        <div className="mt-2 lg:mt-8 flex justify-center">
          <button
            onClick={() => router.push("/free-listing")}
            className="bg-[#29100C] text-white px-5 py-2 rounded-full font-medium hover:bg-[#4a2a20] transition-colors"
          >
            Join Now For Free
          </button>
        </div>
      </div>

      <div className="max-w-[85%] lg:mb-36 ml-4 lg:ml-4 xl:ml-20 justify-center lg:p-6">
        {/* Common heading for both mobile and desktop */}
        <h1 className="text-xl md:text-3xl  lg:text-[38px] font-bold mb-4 lg:mb-11 text-center lg:text-start lg:ml-3 font-inter lg:whitespace-nowrap">
          Why Choose Us As A Buyer?
        </h1>

        {/* Mobile + Tablet View */}
        <div className="block lg:hidden" ref={mobileTabContentRef}>
          <div className="flex overflow-x-auto pb-7 sm:pb-3 gap-2 no-scrollbar">
            {tabs.map(renderMobileTabButton)}
          </div>

          {/* Responsive Card Layout */}
          <div className="relative mt-1 sm:mt-1 h-[50vw] lg:h-[30vw] sm:h-[55vw] md:h-[55vw] mb-4">
            {/* Image Card */}
            <div className="absolute left-0 top-0 w-[70%] h-[90%] sm:w-[65%] md:w-[65%] md:h-[65%] z-0">
              <img
                src={activeTabData.image}
                alt={activeTabData.title}
                className="w-full h-full object-cover rounded-lg shadow-lg"
              />
            </div>

            {/* Text Card */}
            <div className="absolute top-[14%] left-[55%] w-[56%] h-[78%] sm:w-[70%] sm:h-[10%] md:h-[50%] md:w-[50%]  bg-white rounded-lg p-4 sm:p-2 md:p-4 shadow-xl z-10">
              <h2 className="text-xs sm:text-sm md:text-lg font-bold mb-2 w-[50%]">
                {activeTabData.title}
              </h2>
              <p className="text-gray-700 mb-4 md:text-lg sm:text-sm text-xs">
                {activeTabData.description}
              </p>
              <button
                onClick={() => router.push("/free-listing")}
                className="w-[100%] h-[30%] bg-[#29100C] text-white rounded-3xl flex items-center justify-center gap-1  text-xs sm:text-sm md:text-base"
              >
                Create Buyer Profile
              </button>
            </div>
          </div>

          {/* Carousel Indicators - Repositioned and Improved */}
          <div className="flex mb-2 justify-center items-center mt-4 sm:mb-2 sm:mt-6 md:-mt-24">
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                className={`mx-1 transition-all duration-300 ${
                  activeTab === tab.id
                    ? "bg-black w-4 h-2 rounded-full"
                    : "bg-gray-300 w-2 h-2 rounded-full"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Desktop View */}
        <div className="hidden lg:block" ref={desktopTabContentRef}>
          <div className="lg:flex lg:flex-row gap-10">
            <div className="lg:w-[30%] lg:pl-6 lg:-ml-3">
              <div className="flex flex-col gap-3">
                {tabs.map((tab, index) => (
                  <button
                    key={tab.id}
                   ref={(el) => { tabRefs.current[index] = el }}
                    onClick={() => handleTabClick(tab.id)}
                    className={`p-4 text-left rounded-lg border border-gray-300 transition-all text-lg ${
                      activeTab === tab.id
                        ? "bg-[#29100C] text-white font-semibold"
                        : " hover:bg-gray-200"
                    }`}
                  >
                    {tab.title}
                  </button>
                ))}
              </div>
            </div>

            <div className="lg:w-[47%] -mt-16 ml-0 lg:ml-11 xl:ml-20 w-full">
              <div className="relative">
                {/* Text Card */}
                <div
                  className="2xl:w-[900px] xl:w-[480px] xl:h-[360px] lg:w-[430px] lg:h-[300px] bg-white rounded-lg p-6 mt-16 w-full"
                  style={{ boxShadow: "0px 3px 13px #00000021" }}
                >
                  <div className="text-black mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="40"
                      height="40"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <circle cx="12" cy="8" r="5" />
                      <path d="M20 21a8 8 0 0 0-16 0" />
                      <circle cx="12" cy="8" r="2" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold mb-4 w-[70%]">
                    {activeTabData.title}
                  </h2>
                  <p className="mb-6 text-gray-700 text-lg max-w-[35%] break-words">
                    {activeTabData.description}
                  </p>
                  <button
                    onClick={() => router.push("/free-listing")}
                    className="bg-[#29100C] text-white px-4 py-2 rounded-3xl flex items-center gap-2"
                  >
                    <span>+</span> Create Buyer Profile
                  </button>
                </div>

                {/* Image Card */}
                <div className="absolute xl:left-[50%] lg:top-[25%] lg:left-[50%] lg:w-[430px] lg:h-[300px] 2xl:w-[750px] xl:left[100%] xl:w-[480px] xl:h-[360px] w-full z-10 shadow-xl flex items-center justify-center">
                  <img
                    src={activeTabData.image}
                    alt={activeTabData.title}
                    className="w-full h-full object-cover rounded-lg shadow-2xl"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TabComponent;
