import { Locked<PERSON><PERSON> } from "@/components/elements/card/list-card";
import ReviewsStars from "@/components/elements/icon/reviews-stars";
import { But<PERSON> } from "@/components/ui/button";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useParams } from "next/navigation";
import { MdOutlineVerified } from "react-icons/md";
import { TbXboxXFilled } from "react-icons/tb";

const columnData = {
  proposal: [
    {
      key: "category",
      label: "Category",
    },
    {
      key: "quantity",
      label: "Quantity / Volume Needed",
    },
    {
      key: "budget",
      label: "Budget Range",
    },
    {
      key: "delivery",
      label: "Delivery Location",
    },
    {
      key: "urgency",
      label: "Requirement Urgency",
    },
    {
      key: "supplier_type",
      label: "Preferred Supplier Type",
    },
    {
      key: "payment_terms",
      label: "Preferred Payment Terms",
    },
    {
      key: "product_requirement",
      label: "Specific Product Requirement",
    },
    {
      key: "product_specification",
      label: "Product specification",
    },
  ],
  contact: [
    {
      key: "contact_name",
      label: "Contact Name",
    },
    {
      key: "email",
      label: "Email Address",
    },
    {
      key: "company_name",
      label: "Company Name",
    },
    {
      key: "contact_number",
      label: "Contact Number",
    },
    {
      key: "additional_notes",
      label: "Additional Notes",
    },
  ],
};

const data = {
  category: "Digital Marketing",
  quantity: "1-500",
  budget: "5 lakh",
  delivery: "delhi, mumbai, gujarat",
  urgency: "1 week",
  supplier_type: "Manufacturer, Wholesaler",
  payment_terms: "Net 60",
  product_requirement:
    "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
  product_specification:
    "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
};

const checkList = [
  {
    label: "Identify Verified",
    success: true,
  },
  {
    label: "Payment Verified",
    success: false,
  },
  {
    label: "Email Verified",
    success: true,
  },
  {
    label: "Profile Verified",
    success: true,
  },
  {
    label: "Phone Verified",
    success: true,
  },
];

const OpportunityDetails = () => {
  const { opportunity_id } = useParams();
  console.log({ opportunity_id });

  const companyDetail = ListingData[0];

  return (
    <div className="w-full h-full">
      <div className="flex flex-col md:flex-row items-start justify-between gap-4 my-6 md:my-5">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-medium">
            Website Design and Development
          </h3>
          <span className={classNames("text-green-400")}>(open)</span>
        </div>

        <div className="flex flex-col items-start gap-0.5">
          <span className="text-xs">Total Response</span>
          <span className="text-base font-bold">20</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[1fr_minmax(250px,auto)] gap-4">
        <div className="order-2 md:order-1 bg-white shadow-box p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-2">Proposal Details</h3>

          <table className="table-fixed text-sm [&_td]:align-top [&_td]:py-1.5">
            <tbody>
              {columnData?.proposal?.map((col, index) => {
                const current = (data as any)?.[col?.key];
                return (
                  <tr key={index}>
                    <td className="w-min text-nowrap">{col?.label}</td>
                    <td className="px-6">:</td>
                    <td>{current}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>

          <div className="w-full mt-6">
            <h3 className="text-lg font-semibold mb-2">Contact Information</h3>

            <table className="table-fixed text-sm [&_td]:align-top [&_td]:py-1.5">
              <tbody>
                {columnData?.contact?.map((cont, index) => {
                  return (
                    <tr key={index}>
                      <td className="w-min text-nowrap">{cont?.label}</td>
                      <td className="px-6">:</td>
                      <td>
                        <LockedRender />
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          <div className="flex items-center justify-start md:justify-end flex-wrap gap-2 mt-7 [&>*]:rounded-md">
            <Button variant="main-revert">Submit Proposal</Button>
            <Button variant="destructive">Decline</Button>
          </div>
        </div>
        <div className="order-1 md:order-2 bg-white shadow-box p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-6">About Client</h3>

          <div className="flex flex-col items-start gap-3.5 [&>*]:w-full">
            <h4 className="font-semibold">Tencent Holdings(Hide)</h4>
            <div className="flex items-center gap-1.5">
              <img
                src={companyDetail?.location?.flag}
                alt="flag"
                className="flex-1 aspect-auto max-w-5 w-5 h-auto object-contain"
              />
              <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 text-wrap">
                <span>
                  {companyDetail?.location?.address}
                  {", "}
                </span>
                <span>{companyDetail?.location?.label}</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <ReviewsStars rating={4} />
              <span className="font-light text-base text-stone-700">4/5</span>
            </div>

            {companyDetail?.verified && (
              <div className="flex items-center gap-2 mb-2">
                <div className="hidden sm:flex bg-[#9F9795] text-white px-2 py-1 items-center justify-start gap-1.5 w-max max-w-max rounded-sm">
                  <img
                    src={"/assets/pages/verified-white.svg"}
                    alt="flag"
                    className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
                  />
                  <span className="flex-1 text-[0.6rem] font-medium">
                    Verified
                  </span>
                </div>
              </div>
            )}

            <div className="flex items-start flex-col gap-3 [&>*]:w-full">
              {checkList?.map((ch, index) => (
                <div
                  key={index}
                  className="flex items-start justify-between gap-2.5 [&>svg]:w-5 [&>svg]:h-5"
                >
                  <span className="text-sm font-light">{ch?.label}</span>
                  {ch?.success ? (
                    <MdOutlineVerified className="fill-green-500" />
                  ) : (
                    <TbXboxXFilled className="fill-red-500" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OpportunityDetails;
