"use client";
import ProductCard from "@/components/elements/card/product";
import CategoryCard from "./card";
import { Button } from "@/components/ui/button";
import ProductSlider from "./slider";
import { useState } from "react";

const Products = () => {
  // Category data structure with key-value pairs
  const [categories] = useState({
    keyboards: {
      image: "assets/pages/buyer/building.jpg",
      label: "Keyboards"
    },
    handheldGame: {
      image: "assets/pages/buyer/building.jpg",
      label: "Handheld Game"
    },
    stylusPens: {
      image: "assets/pages/buyer/building.jpg",
      label: "Stylus Pens"
    },
    keyboardMouse: {
      image: "assets/pages/buyer/building.jpg",
      label: "Keyboard Mouse"
    },
    usbHubs: {
      image: "assets/pages/buyer/building.jpg",
      label: "USB Hubs"
    },
    otherProducts: {
      image: "assets/pages/buyer/building.jpg",
      label: "Other Products"
    },
    gamingOnEar: {
      image: "assets/pages/buyer/building.jpg",
      label: "Gaming On-Ear"
    },
    otherGame: {
      image: "assets/pages/buyer/building.jpg",
      label: "Other Game"
    },
    smartRings: {
      image: "assets/pages/buyer/building.jpg",
      label: "Smart Rings"
    },
    powerBanks: {
      image: "assets/pages/buyer/building.jpg",
      label: "Power Banks"
    },
    otherProduct: {
      image: "assets/pages/buyer/building.jpg",
      label: "Other Products"
    },
    otherProduct1: {
      image: "assets/pages/buyer/building.jpg",
      label: "Other Products"
    },
    otherProduct2: {
      image: "assets/pages/buyer/building.jpg",
      label: "Other Products"
    },
    otherProduct3: {
      image: "assets/pages/buyer/building.jpg",
      label: "Other Products"
    },
    otherProduct4: {
      image: "assets/pages/buyer/building.jpg",
      label: "Other Products"
    },
    otherProduct5: {
      image: "assets/pages/buyer/building.jpg",
      label: "Other Products"
    },
  });

  return (
    <div className="flex justify-center w-full mt-7 px-2 sm:px-6 lg:px-8">
      <div className="w-full max-w-screen-2xl 2xl:max-w-[90%]">
        <div className="w-full bg-white rounded-lg shadow-lg p-4 border border-gray-100">
          <div className="mb-4 px-1">
            <h3 className="text-base font-bold text-gray-800">Source by Categories</h3>
          </div>
          <div className="w-full">
            <ProductSlider>
              {Object.entries(categories).map(([key, category]) => (
                <CategoryCard 
                  key={key}
                  image={category.image}
                  label={category.label}
                />
              ))}
            </ProductSlider>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Products;