"use client";

import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/input/switch";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const PaidServices = () => {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (pathname === "/dashboard/seller/paid-services") {
      router.push("/dashboard/seller/paid-services/advertising");
    }
  }, [router, pathname]);

  return <PaidServicesTabs />;
};

const TabsData = [
  {
    title: "Advertising Plan",
    path: "/dashboard/seller/paid-services/advertising",
  },
  {
    title: "Buy Leads Plan",
    path: "/dashboard/seller/paid-services/leads",
  },
  {
    title: "Membership",
    path: "/dashboard/seller/paid-services/membership",
  },
];

export const PaidServicesTabs = () => {
  const router = useRouter();
  const pathname = usePathname();

  const initialActive = TabsData?.[0]?.path;
  const [active, setActive] = useState<string | undefined>(initialActive);

  useEffect(() => {
    const current = TabsData?.find((item) => pathname?.includes(item?.path));
    setActive(current?.path);
  }, [pathname]);

  return (
    <>
      <div className="relative w-full h-full grid grid-cols-1">
        <PriceModalSwitch />
        <Tabs
          defaultValue={initialActive}
          value={active}
          className="order-1 md:order-2 overflow-auto no-scrollbar py-1.5 border-b border-stone-300 mb-5"
        >
          <TabsList className="flex-nowrap">
            {TabsData?.map((tab, index) => {
              return (
                <TabsTrigger
                  key={index}
                  value={tab?.path}
                  className="flex-1 uppercase before:data-[state=active]:-bottom-2.5"
                  onClick={() => router.push(tab?.path)}
                >
                  {tab?.title}
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>
      </div>
      {/* <Outlet /> */}
      {/* Change this to be a children wrapped in layout component */}
    </>
  );
};

export const PriceModalSwitch = () => {
  return (
    <div className="static md:absolute right-0 -top-9 order-2 md:order-1 mb-6 md:mb-0 ms-3 md:ms-0">
      <div className="flex items-center gap-2">
        <span className="font-semibold text-base">Monthly</span>
        <Switch />
        <span className="font-semibold text-base">Yearly</span>
        <Badge>15%</Badge>
      </div>
    </div>
  );
};

export default PaidServices;
