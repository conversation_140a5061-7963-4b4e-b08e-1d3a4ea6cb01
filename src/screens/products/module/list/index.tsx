"use client";

import AdvancedFilter from "@/components/elements/filters/listings/desktop/advanced";
import CategoryFilter from "@/components/elements/filters/listings/desktop/category";
import CompanyTypeFilter from "@/components/elements/filters/listings/desktop/company-type";
import LocationSearchFilter from "@/components/elements/filters/listings/desktop/location";
import RatingFilter from "@/components/elements/filters/listings/desktop/ratings";
import SortFilter from "@/components/elements/filters/listings/desktop/sort";
import VerifiedFilter from "@/components/elements/filters/listings/desktop/verified";
import AdBanner from "@/components/layout/ads/banner";
import useScrolled from "@/lib/hooks/useScrolled";
import ProductCard from "@/screens/user/dashboard/pages/buyer/favourite/module/product/product-card";
import classNames from "classnames";
import Compare from "../compare-pop";
// import routes from "@/router/routes/types/product";

const Products = () => {
  const isScrolled = useScrolled();

  return (
    <div className="bg-white">
      <div className="container mx-auto px-4 sm:px-6 md:px-8">
        {/* Listing Filter */}
        <div
          className={classNames(
            "flex flex-col md:flex-row md:items-center md:flex-wrap gap-2 md:gap-3 bg-white py-4 md:py-6 px-4 z-30 transition-all",
            isScrolled &&
              "shadow-md sticky top-header-sm sm:top-header-md md:top-header",
            classNames
          )}
        >
          <div className="w-full md:w-auto">
            <LocationSearchFilter />
          </div>
          <div className="grid grid-cols-2 md:flex gap-2 md:gap-3">
            <CategoryFilter />
            <RatingFilter />
          </div>
          <div className="grid grid-cols-2 md:flex gap-2 md:gap-3">
            <CompanyTypeFilter />
            <VerifiedFilter />
          </div>
          <div className="flex gap-2 mt-2 md:mt-0">
            <AdvancedFilter />
            {isScrolled && <SortFilter />}
          </div>
        </div>{" "}
        {/* <CreateRouter routeData={routes}/> */}
        {/* Product Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-6">
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
        </div>
        {/* Advertisement Banner */}
        <AdBanner />
        {/* Second Product Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-6">
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <ProductCard />
          <Compare />
        </div>
        {/* Loading More Button */}
        <div className="flex items-center justify-center mt-8 mb-4">
          <span className="flex items-center justify-center gap-2 bg-brown text-white rounded-full px-4 py-2.5 text-sm font-medium whitespace-nowrap max-w-max">
            Loading more...
          </span>
        </div>
      </div>
    </div>
  );
};

export default Products;
