import { createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "@/redux/store";
import * as API from "../@api/update";
import { toast } from "sonner";

// Thunk to compare up to 4 products
export const compareProductsThunk = createAsyncThunk<any, string[], { state: RootState }>(
  "product_compare/compareProducts",
  async (productIds, { rejectWithValue }) => {
    try {
      const response = await API.compareProducts(productIds);

      toast.success(response?.meta?.message || "Products compared successfully", {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Product comparison failed", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);
