"use client";

import React, { useState, useEffect, useRef } from 'react';
import img from './assests/Group 5431.png';
import { useRouter } from "next/navigation";

const Index = () => {
  const router = useRouter();
    const [activeStep, setActiveStep] = useState(0);
    const stepsRef = useRef<(HTMLDivElement | null)[]>([]);
  
  // Steps data
  const steps = [
    {
      title: "Post Your Requirement",
      description: "Provide product details, quantity, and delivery location in simple steps.",
    },
    {
      title: "Get Tailored Proposals",
      description: "Receive competitive offers from trusted suppliers based on your posted requirements.",
    },
    {
      title: "Compare and Choose",
      description: "Evaluate offers on pricing, quality, and delivery timelines to select the right supplier.",
    },
    {
      title: "Close the Deal",
      description: "Connect directly with the supplier to negotiate terms and complete your purchase.",
    }
  ];

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + window.innerHeight / 2;
      
      // Find which step is currently in view
      stepsRef.current.forEach((step, index) => {
        if (!step) return;
        
        const rect = step.getBoundingClientRect();
        const stepTop = rect.top + window.scrollY;
        const stepBottom = stepTop + rect.height;
        
        if (scrollPosition >= stepTop && scrollPosition <= stepBottom) {
          setActiveStep(index);
        }
      });
    };
    
    window.addEventListener('scroll', handleScroll);
    
    // Initial check
    handleScroll();
    
    // Cleanup
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className="flex flex-col md:flex-row bg-red-50 p-6 md:p-12 gap-6 mx-auto w-full max-w-full md:max-w-[1500px] lg:max-w-[1500px] xl:max-w-full"
      style={{ height: 'calc(100% - 20px)' }}
    >
      {/* Heading for mobile only - shows at the top */}
      <div className="md:hidden w-full text-center mb-4">
        <h1 className="text-3xl font-bold text-gray-900">How It Works</h1>
      </div>
      
      {/* Left side with image - keeping original styling */}
      <div className="w-full md:w-1/2 2xl:w-full flex justify-center items-center">
        <img src="assets/pages/buyer/Group 5431.png" alt="Procurement Platform" className="max-w-full h-auto" />
      </div>

      {/* Right side with "How It Works" content */}
      <div className="w-full md:w-1/2 2xl:w-full">
        {/* Heading visible only on larger screens */}
        <h1 className="hidden md:block text-4xl font-bold text-gray-900 mb-8">How It Works</h1>

        {/* Steps */}
        {steps.map((step, idx) => (
          <div 
            key={idx} 
            className="flex mb-8"
            ref={(el) => { stepsRef.current[idx] = el }}
            >
            <div className="mr-4 flex flex-col items-center">
              <div className={`${activeStep === idx ? 'bg-red-500' : 'bg-gray-300'} text-white rounded-md p-2 flex items-center justify-center transition-colors duration-300`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              {idx < steps.length - 1 && (
                <div className={`w-px h-16 ${activeStep > idx ? 'bg-red-500' : 'bg-gray-200'} mx-auto mt-1 transition-colors duration-300`}></div>
              )}
            </div>
            <div>
              <h2 className={`text-xl ${activeStep === idx ? 'font-bold text-gray-900' : 'font-normal text-gray-500'} transition-all duration-300`}>
                {step.title}
              </h2>
              <p className={`mt-2 ${activeStep === idx ? 'text-gray-700' : 'text-gray-400'} transition-colors duration-300`}>
                {step.description}
              </p>
            </div>
          </div>
        ))}

        <div className="flex md:block justify-center">
          <button 
            onClick={() => router.push("/free-listing")}
            className="bg-[#33221C] text-white font-medium py-3 px-8 rounded-full hover:bg-[#462D24] transition-colors duration-200"
          >
            Create Buyer account
          </button>
        </div>
      </div>
    </div>
  );
};

export default Index;