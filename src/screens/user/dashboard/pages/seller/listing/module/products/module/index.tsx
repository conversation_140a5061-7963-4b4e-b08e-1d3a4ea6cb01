"use client";

// src/components/pages/user/dashboard/pages/products/index.tsx
import ListPagination from "@/components/elements/list/pagination";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import {
  getFilteredData,
  getPaginationOptions,
  getPaginationSummary,
} from "@/lib/utils/data";
import {
  currentPage,
  productsList,
  searchValue,
} from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import { getProducts } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/thunk";
import { RootDispatch } from "@/redux/store";
import { debounce } from "lodash";
import { Plus, Search } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import ProductList from "./list";

const Products = () => {
const router = useRouter();
  const dispatch = useDispatch<RootDispatch>();
  const { data: productData, pagination, loading } = useSelector(productsList);
  const search = useSelector(searchValue);
  const pageNow = useSelector(currentPage);

  useEffect(() => {
    dispatch(getProducts());
  }, [dispatch]);

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (searchTerm?: string, page?: string) =>
          dispatch(getProducts({ searchTerm, page })),
        500
      ),
    [dispatch]
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(Actions.setSearchValue(e?.target?.value));
    debouncedSearch(e?.target?.value);
  };

  const handlePagination = (page: string) => {
    dispatch(Actions.setCurrentPage(page));
    debouncedSearch(search, page);
  };

  const filteredData = getFilteredData({
    data: productData,
    compareValue: search,
    compareParams: ["product_name"],
  });

  const isDataThere = !loading && productData && pagination?.totalPages > 1;

  return (
    <main className="w-full h-full">
      {/* <h2 className="text-base font-semibold mb-6">Product List</h2> */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="flex items-center space-x-4">
          <Button
            onClick={() => router.push("add-product")}
            className="rounded-md"
            variant="main-revert"
          >
            Add New Product
          </Button>
          <Button
            onClick={() => router.push("add-product")}
            className="rounded-md"
          >
            <Plus className="!w-5 !h-5" />
          </Button>

          {isDataThere && getPaginationSummary(pagination) && (
            <span className="px-3 text-sm text-slate-400 font-semibold">
              {getPaginationSummary(pagination)}
            </span>
          )}
        </div>

        <div className="flex justify-end items-center">
          <Input
            type="text"
            name="product_search"
            onChange={(e) => handleChange(e)}
            value={search}
            placeholder="Search..."
            action={
              <button type="submit">
                <Search className="w-4 h-4" />
              </button>
            }
          />
        </div>
      </div>

      <ProductList products={filteredData} />

      {isDataThere && (
        <div className="flex justify-between mt-8 items-center gap-4">
          <ListPagination
            totalPages={pagination?.totalPages}
            currentPage={pagination?.page}
            onPageChange={(page) => handlePagination(String(page))}
          />

          <div className="ml-auto">
            <Select
              name="pages"
              value={pageNow}
              onChange={(e) => handlePagination(e?.target?.value)}
              placeholder="Page"
              options={getPaginationOptions(pagination?.totalPages)}
              className="w-max"
            />
          </div>
        </div>
      )}
    </main>
  );
};

export default Products;
