import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { InputTargetType } from "@/components/ui/input/types";
import { RootDispatch } from "@/redux/store";
import { productDetails } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";

const ProductDetails = () => {
  const dispatch = useDispatch<RootDispatch>();

  const { data: values } = useSelector(productDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values[e?.target?.name])
          ? Array.from(values[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setProductDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <MultiSelect
        onValueChange={handleChange}
        defaultValue={values?.usp ?? []}
        value={values?.usp ?? []}
        name="usp"
        label="USP (Unique Selling price)"
        placeholder="Select Product UPS"
        variant="inverted"
        maxCount={3}
        options={[]}
        addNew
      />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Input
          type="text"
          name="origin"
          label="Place of Origin"
          placeholder="Enter Origin"
          onChange={handleChange}
          value={values?.origin ?? ""}
        />
        <Input
          type="text"
          name="brand_name"
          label="Brand Name"
          placeholder="Enter Brand Name"
          value={values?.brand_name ?? ""}
          onChange={handleChange}
        />
        <Input
          type="text"
          name="sku"
          label="SKU/Model Number"
          placeholder="Enter unique no."
          value={values?.sku ?? ""}
          onChange={handleChange}
        />
        <Select
          name="product_status"
          label="Product Status"
          onChange={handleChange}
          value={values?.product_status ?? ""}
          placeholder="Select Status"
          options={[
            { label: "Active", value: true },
            { label: "Inactive", value: false },
          ]}
        />
      </div>
      <Input
        type="text"
        name="meta_title"
        label="Meta Title"
        placeholder="Enter title"
        value={values?.meta_title ?? ""}
        onChange={handleChange}
      />
      <Textarea
        name="meta_description"
        label="Meta Description"
        onChange={handleChange}
        value={values?.meta_description ?? ""}
        placeholder="Type here"
        className="min-h-20"
        containerClassName="flex-1"
        maxLength={500}
        showCharCount
      />
      <MultiSelect
        label="Meta Keywords"
        placeholder="Add Keywords"
        name="meta_keywords"
        defaultValue={values?.meta_keywords ?? []}
        onValueChange={handleChange}
        options={[]}
        maxCount={3}
        addNew
      />
    </>
  );
};

export default ProductDetails;
