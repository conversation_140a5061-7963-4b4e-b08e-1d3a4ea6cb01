import MAIN_LOGO from "/assets/branding/logo.svg";
import { Button } from "@/components/ui/button";
import { Actions } from "@/screens/elements/@redux/slice";
import { useDispatch } from "react-redux";

const InquiryCategory = () => {
  const dispatch = useDispatch();

  const handleCLick = () => {
    dispatch(Actions.setCurrentFormStep(2));
  };

  return (
    <div className="min-w-[40vw] h-full flex-1 p-10 flex items-start justify-center">
      <div className="w-full flex flex-col items-center justify-center gap-4">
        <img
          src={MAIN_LOGO}
          alt="Aalyana"
          className="w-full max-w-28 object-contain mb-1 hidden md:block"
        />

        <h5 className="font-bold text-2xl">What are you looking for?</h5>

        <div className="flex items-center justify-center gap-4">
          <Button onClick={handleCLick} variant="main">
            Products
          </Button>
          <Button onClick={handleCLick} variant="main">
            Services
          </Button>
        </div>
      </div>
    </div>
  );
};

export default InquiryCategory;
