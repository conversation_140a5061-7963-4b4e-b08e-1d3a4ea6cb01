import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import { createSlice, PayloadAction, AsyncThunk } from "@reduxjs/toolkit";
import { getBusinessDetails, updateBusinessDetails, getSubSubCategories } from "./thunk";

interface APTStateType {
  businessDetails: APIResponseType;
  sub_sub_categories: APIResponseType;
}

const APIState: APTStateType = {
  businessDetails: {
    data: null,
    loading: false,
    meta: null,
  },
  sub_sub_categories: {
    data: null,
    loading: false,
    meta: null,
  },
};

interface stateType extends APTStateType {
  currentFormStep: number;
  formEdits: {
    [key: string]: any;
  };
}

const initialState: stateType = {
  ...APIState,
  currentFormStep: 0,
  formEdits: {},
};

export const slice = createSlice({
  name: "user_buyer_business",
  initialState,
  reducers: {
    setCurrentFormStep: (state, action: PayloadAction<number>) => {
      state.currentFormStep = action.payload;
    },
    setFormValues: (state, action: PayloadAction<any | null>) => {
      if (action.payload) {
        if (!state.businessDetails.data) {
          state.businessDetails.data = {};
        }
        
        state.formEdits = {
          ...state.formEdits,
          ...action.payload,
        };        
        
        state.businessDetails.data = {
          ...state.businessDetails.data,
          ...state.formEdits,
        };
        
        console.log('Redux: Form values updated', {
          payload: action.payload,
          formEdits: state.formEdits,
          businessData: state.businessDetails.data
        });
        
      } else {
        state.businessDetails = { ...initialState.businessDetails };
        state.formEdits = {};
      }
    },
    clearFormValues: (state) => {
      state.formEdits = {};
    },
    // New action to merge API response with form edits
    mergeApiResponse: (state, action: PayloadAction<any>) => {
      if (action.payload) {
        console.log('Redux: Merging API response with form edits', {
          apiData: action.payload,
          currentFormEdits: state.formEdits
        });
        
        state.businessDetails.data = {
          ...action.payload,
          ...state.formEdits, 
        };
      }
    },
    logState: (state) => {
      console.log("Current Redux State:", {
        businessDetails: state.businessDetails,
        formEdits: state.formEdits,
        sub_sub_categories: state.sub_sub_categories
      });
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateBusinessDetails.pending, (state) => {
      })
      .addCase(updateBusinessDetails.fulfilled, (state, action) => {
        
        state.businessDetails.meta = action.payload?.meta || { message: "Updated successfully" };
        
        if (action.payload?.data) {
          state.businessDetails.data = {
            ...action.payload.data,
            ...state.formEdits, 
          };
        }
      })
      .addCase(updateBusinessDetails.rejected, (state, action) => {
        state.businessDetails.meta = action.payload as any || { error: "Failed to update business details" };
        console.log('Redux: Update business details rejected', action.payload);
      });
    
    builder
      .addCase(getBusinessDetails.pending, (state) => {
        state.businessDetails.loading = true;
      })
      .addCase(getBusinessDetails.fulfilled, (state, action) => {
        state.businessDetails.loading = false;
        console.log('Redux: Get business details fulfilled', action.payload);
        
        if (action.payload?.data) {
          const industryCategory = action.payload.data.industry_category;
          if (industryCategory && Array.isArray(industryCategory) && industryCategory.length > 0) {
            console.log("Industry Category from API:", industryCategory);
          }
          
          const newBusinessData = {
            ...action.payload.data,
            ...state.formEdits, 
          };
          
          state.businessDetails.data = newBusinessData;
          
          console.log('Redux: Business data merged with form edits', {
            apiData: action.payload.data,
            formEdits: state.formEdits,
            finalData: newBusinessData
          });
          
        } else {
          state.businessDetails.data = state.businessDetails.data || {};
        }
        state.businessDetails.meta = action.payload?.meta || null;
      })
      .addCase(getBusinessDetails.rejected, (state, action) => {
        state.businessDetails.loading = false;
        state.businessDetails.meta = action.payload as any || { error: "Failed to fetch business details" };
      });
    
    builder
      .addCase(getSubSubCategories.pending, (state) => {
        state.sub_sub_categories.loading = true;
      })
      .addCase(getSubSubCategories.fulfilled, (state, action) => {
        state.sub_sub_categories.loading = false;
        state.sub_sub_categories.data = action.payload?.data || action.payload || [];
        state.sub_sub_categories.meta = action.payload?.meta || { message: "Sub categories fetched successfully" };
      })
      .addCase(getSubSubCategories.rejected, (state, action) => {
        state.sub_sub_categories.loading = false;
        state.sub_sub_categories.data = [];
        state.sub_sub_categories.meta = action.payload as any || { error: "Failed to fetch sub categories" };
      });
  },
});

export const Actions = slice.actions;
export default slice.reducer;