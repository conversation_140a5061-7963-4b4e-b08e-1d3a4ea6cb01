import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";

interface AddFavouritePayload {
  favoriteId: string;
  favoriteType: 'product' | 'service' | 'business';
  tags?: string[];
  notes?: string;
  priority?: number;
}

// Add a favourite item (product, service, or business)
export const addFavourite = async (payload: AddFavouritePayload) => {
  try {
    const response = await formDataClient.post(apiRoutes().buyer.favourite.addFavourite, payload);
    return response.data;
  } catch (error) {
    console.error("API addFavourite error:", error);
    throw error;
  }
};

// Get a list of favourite items with optional filters
export const getFavourites = async ({
  favoriteType,
  priority,
  page,
  limit,
}: {
  favoriteType: 'product' | 'service' | 'business';
  priority?: number;
  page?: number;
  limit?: number;
}) => {
  try {
    const response = await formDataClient.get(apiRoutes().buyer.favourite.fetchFavourite, {
      params: {
        favoriteType,
        priority,
        page,
        limit,
      },
    });
    return response.data;
  } catch (error) {
    console.error("API getFavourites error:", error);
    throw error;
  }
};

// Delete a favourite item by its ID
export const deleteFavourite = async (favouriteId: string) => {
  try {
    const response = await formDataClient.delete(
      `${apiRoutes().buyer.favourite.deleteFavourite}/${favouriteId}`
    );
    return response.data;
  } catch (error) {
    console.error("API deleteFavourite error:", error);
    throw error;
  }
};

