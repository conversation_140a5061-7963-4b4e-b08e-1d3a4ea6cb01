"use client";

import {
  compareOverview,
  finalOverview,
  overviewValues,
} from "@/constants/compare";
import { selectedColumn } from "@/screens/company/compare/@redux/selectors";
import classNames from "classnames";
import { ReactNode } from "react";
import { useSelector } from "react-redux";

const DesktopOverview = () => {
  const current = useSelector(selectedColumn);

  return (
    <div className="w-full h-full overflow-y-hidden flex">
      <table className="border-collapse table-fixed">
        <tbody>
          {finalOverview.map((_, trIndex) => {
            return (
              <tr key={trIndex} className="">
                <TdRender className="md:text-nowrap">
                  {compareOverview?.[trIndex]?.name}
                </TdRender>
                {[...Array(+4).keys()]?.map((_, index) => {
                  const isACtive = current === index;
                  if (window?.innerWidth < 768 && !isACtive) return null;

                  return (
                    <TdRender key={index} className="md:text-nowrap">
                      {Array.isArray(overviewValues?.[trIndex]?.content) ? (
                        <TagsRender
                          tags={overviewValues?.[trIndex]?.content as string[]}
                        />
                      ) : (
                        `${
                          overviewValues?.[trIndex]?.content
                        } ${index?.toString()}`
                      )}
                    </TdRender>
                  );
                })}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

const TdRender = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => (
  <td
    className={classNames(
      "border-y border-l first-of-type:border-l-0 first-of-type:font-bold border-stone-200 text-xs px-6 py-3 md:text-nowrap",
      className
    )}
  >
    {children}
  </td>
);

const TagsRender = ({ tags }: { tags: string[] }) => {
  return (
    <div className="flex flex-wrap gap-1">
      {tags?.map((tag, index) => {
        return (
          <button
            key={index}
            className="flex items-center justify-center px-3 py-0.5 bg-stone-50 hover:bg-stone-100 border border-stone-300 rounded-full text-stone-800 text-xs"
          >
            {tag}
          </button>
        );
      })}
    </div>
  );
};

export default DesktopOverview;
