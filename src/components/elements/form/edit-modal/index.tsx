import React from "react";
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  Di<PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import classNames from "classnames";
import { Edit } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface EditModalType {
  action?: React.ReactNode;
  children: React.ReactNode;
  hasAccess?: boolean;
  onClose?: () => void;
  onSave?: () => void;
  className?: string;
  contentClassName?: string;
  title?: string;
}

const EditModal: React.FC<EditModalType> = ({
  action,
  children,
  className,
  contentClassName,
  hasAccess = false,
  title = "Form",
}) => {
  if (!hasAccess) {
    return null;
  }

  return (
    <div>
      <Dialog>
        <DialogTrigger asChild>
          {action ?? (
            <Button variant={"main-revert"} className={classNames(className)}>
              <Edit />
            </Button>
          )}
        </DialogTrigger>
        <DialogContent
          className={classNames(
            "bg-[#FAF7F7] !p-0 !max-w-full md:!max-w-max overflow-auto max-h-full h-screen md:h-max md:max-h-[80vh] !gap-2",
            contentClassName
          )}
          hideClose
        >
          <DialogTitle className="flex items-center gap-1">{title}</DialogTitle>
          {children}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EditModal;
