import { cva } from "class-variance-authority";
import classNames from "classnames";
import {
  createElement,
  FC,
  HTMLAttributes,
  isValidElement,
  ReactNode,
} from "react";

interface InputWrapperType extends HTMLAttributes<HTMLDivElement> {
  icon?: ReactNode | string;
  children: ReactNode;
  className?: string;
  error?: boolean;
}

const renderIcon = (icon: any) => {
  try {
    const IconComponent = icon;
    return createElement(IconComponent, null);
  } catch {
    return "";
  }
};

const wrapperStyle = cva(
  "input-wrapper h-input border border-light flex items-center gap-2 px-3 py-2 bg-white hover:bg-slate-50 rounded-md"
);

const InputWrapper: FC<InputWrapperType> = ({
  children,
  className,
  icon,
  error,
  ...props
}) => {
  return (
    <div
      className={classNames(
        wrapperStyle(),
        {
          "!border-red-500": error,
        },
        className
      )}
      {...props}
    >
      {icon && (
        <div className="input-icon-container relative max-w-max [&>svg]:w-4 [&>svg]:h-4">
          {typeof icon === "string" ? (
            <img
              src={icon ? icon : ""}
              alt={icon ?? "input-icon"}
              className="input-icon w-4 h-4 object-contain"
            />
          ) : isValidElement(icon) ? (
            <>{icon}</>
          ) : (
            typeof icon === "object" && <>{renderIcon(icon as ReactNode)}</>
          )}
        </div>
      )}
      {children}
    </div>
  );
};

export { InputWrapper, wrapperStyle };
