import { RootState } from "@/redux/root-reducer";
import { createSelector } from "reselect";

export const reducer = (state: RootState) => state.user_products;

export const searchValue = createSelector(
  [reducer],
  (reducer) => reducer.searchValue
);

export const currentPage = createSelector(
  [reducer],
  (reducer) => reducer.currentPage
);

export const productsList = createSelector(
  [reducer],
  (reducer) => reducer.productsList
);

export const productDetails = createSelector(
  [reducer],
  (reducer) => reducer.productDetails
);

export const dynamicFields = createSelector(
  [reducer],
  (reducer) => reducer.dynamicFields
);

export const productSuggestions = createSelector(
  [reducer],
  (reducer) => reducer.productSuggestions
);

export const suggestedProduct = createSelector(
  [reducer],
  (reducer) => reducer.suggestedProduct
);
