import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import {
  DataActionType,
  PaginationType,
  useInfiniteScroll,
} from "@/lib/hooks/useInfiniteScroll";
import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import classNames from "classnames";
import { debounce } from "lodash";
import {
  CheckIcon,
  ChevronDown,
  PlusSquare,
  WandSparkles,
  XCircle,
  XIcon,
} from "lucide-react";
import * as React from "react";
import { RiResetLeftFill } from "react-icons/ri";
import { Loader } from "../../loader";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "../../tooltip";
import { InputLabel } from "../components/label";

/**
 * Variants for the multi-select component to handle different styles.
 * Uses class-variance-authority (cva) to define different styles based on "variant" prop.
 */
const multiSelectVariants = cva(
  "m-1 transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300",
  {
    variants: {
      variant: {
        default:
          "border-foreground/10 text-foreground bg-card hover:bg-card/80",
        secondary:
          "border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        inverted: "inverted",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

type MultiSelectTarget = {
  target: {
    value: string[];
    name: string;
  };
};

/**
 * Props for MultiSelect component
 */

interface OptionType {
  label: string;
  /** The unique value associated with the option. */
  value: string;
  /** Optional icon component to display alongside the option. */
  icon?: React.ComponentType<{ className?: string }>;
  [key: string]: any;
}

interface MultiSelectType
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof multiSelectVariants> {
  /**
   * An array of option objects to be displayed in the multi-select component.
   * Each option object has a label, value, and an optional icon.
   */
  options: OptionType[];
  optionKeys?: {
    label?: string;
    value?: string;
    icon?: string;
  };

  name?: string;

  /**
   * Callback function triggered when the selected values change.
   * Receives an array of the new selected values.
   */
  onValueChange: (value: MultiSelectTarget) => void;

  /** The default selected values when the component mounts. */
  defaultValue?: string[];
  value?: string[];

  /**
   * Placeholder text to be displayed when no values are selected.
   * Optional, defaults to "Select options".
   */
  placeholder?: string;

  morePlaceholder?: string;

  /**
   * Animation duration in seconds for the visual effects (e.g., bouncing badges).
   * Optional, defaults to 0 (no animation).
   */
  animation?: number;

  /**
   * Maximum number of items to display. Extra selected items will be summarized.
   * Optional, defaults to 3.
   */
  maxCount?: number;

  /**
   * The modality of the popover. When set to true, interaction with outside elements
   * will be disabled and only popover content will be visible to screen readers.
   * Optional, defaults to false.
   */
  modalPopover?: boolean;

  /**
   * If true, renders the multi-select component as a child of another component.
   * Optional, defaults to false.
   */
  asChild?: boolean;

  /**
   * If true, can add new option with searched keyword.
   * Optional, defaults to false.
   */
  addNew?: boolean;

  /**
   * Additional class names to apply custom styles to the multi-select component.
   * Optional, can be used to add custom styles.
   */
  className?: string;
  containerClassName?: string;
  label?: string;
  labelClassName?: string;
  required?: boolean;
  dataLoading?: boolean;
  onOpen?: (val: boolean) => void;
  pagination?: PaginationType;
  dataAction?: DataActionType;
  resetAction?: () => void;
  onSearch?: (value: string) => void;

  /**
   * Pre-selected items that might not be in the current options list.
   * Used to display names for items that haven't been fetched yet due to pagination.
   * Format: Array of objects with the same structure as options
   *
   * Example usage with company sub_sub_category:
   * ```
   * const preSelectedItems = values?.sub_sub_category
   *   ?.filter(item => item && typeof item === "object" && item._id && item.sub_sub_category_name)
   *   ?.map(item => ({
   *     _id: item._id,
   *     sub_sub_category_name: item.sub_sub_category_name,
   *   })) || [];
   * ```
   */
  preSelectedItems?: OptionType[];
}

const MultiSelect = React.memo(
  React.forwardRef<HTMLButtonElement, MultiSelectType>(
    (
      {
        options = [],
        optionKeys,
        name,
        onValueChange,
        variant,
        defaultValue = [],
        value = [],
        placeholder = "Select options",
        morePlaceholder = "more",
        animation = 0,
        maxCount = 3,
        modalPopover = false,
        asChild = false,
        className,
        label,
        labelClassName,
        containerClassName,
        addNew,
        required,
        onOpen,
        pagination,
        dataAction,
        dataLoading,
        resetAction,
        onSearch,
        preSelectedItems = [],
        ...props
      },
      ref
    ) => {
      const dropdownRef = React.useRef<HTMLDivElement>(null);
      const valueByParent = defaultValue?.length > 0 ? defaultValue : value;
      const [selectedValues, setSelectedValues] =
        React.useState<string[]>(valueByParent);
      const [isPopoverOpen, setIsPopoverOpen] = React.useState(false);
      const [isAnimating, setIsAnimating] = React.useState(false);
      const [searchTerm, setSearchTerm] = React.useState("");
      const [commandListCount, setCommandListCount] =
        React.useState<number>(20);
      const [optionData, setOptionData] = React.useState<OptionType[]>([]);

      const { loaderRef, loading: infiniteLoading } = useInfiniteScroll({
        action: dataAction,
        pagination,
        search: searchTerm,
        resetAction: resetAction,
      });

      const getLabel = React.useCallback(
        (option: OptionType) => {
          try {
            if (optionKeys && optionKeys?.label) {
              return option?.[optionKeys?.label];
            }

            return option?.label;
          } catch {
            return option?.label;
          }
        },
        [optionKeys]
      );

      const getValue = React.useCallback(
        (option: OptionType) => {
          try {
            if (optionKeys && optionKeys?.value) {
              return option?.[optionKeys?.value];
            }

            return option?.value;
          } catch {
            return option?.value;
          }
        },
        [optionKeys]
      );

      React.useEffect(() => {
        let missingDefaults: any = [];
        let mergedOptions = [...(options ?? [])];

        if (valueByParent && Array.isArray(valueByParent)) {
          const currentValues = options?.map(getValue) || [];
          missingDefaults = valueByParent.filter(
            (val) => !currentValues.includes(val)
          );
        }

        // Handle missing defaults - items that are selected but not in current options
        if (missingDefaults?.length > 0) {
          const addedOptions = missingDefaults?.map((val: string) => {
            // First check if we have this item in preSelectedItems
            const preSelectedItem = preSelectedItems?.find(item => getValue(item) === val);
            if (preSelectedItem) {
              return preSelectedItem;
            }

            // If no preSelectedItem found, create a fallback option with ID as label
            // This will be replaced when the actual data is fetched
            return {
              [optionKeys?.label || "label"]: val,
              [optionKeys?.value || "value"]: val,
            };
          });

          mergedOptions = [...addedOptions, ...mergedOptions];
        }

        // Add preSelectedItems that aren't already in options or missing defaults
        if (preSelectedItems?.length > 0) {
          const existingValues = mergedOptions.map(getValue);
          const additionalPreSelected = preSelectedItems.filter(
            item => !existingValues.includes(getValue(item))
          );
          mergedOptions = [...mergedOptions, ...additionalPreSelected];
        }

        // Only update if there's a meaningful change
        const newValues = mergedOptions?.map(getValue);
        const prevValues = optionData?.map(getValue);

        const isDifferent =
          newValues?.length !== prevValues?.length ||
          !newValues?.every((v, i) => v === prevValues[i]);

        if (isDifferent) {
          setOptionData(mergedOptions);
        }
      }, [valueByParent, getValue, optionData, options, pagination, preSelectedItems, optionKeys]);

      React.useEffect(() => {
        if (selectedValues?.length !== valueByParent?.length) {
          setSelectedValues(valueByParent);
        }
      }, [valueByParent, selectedValues?.length]);

      const handleValueChange = (value: string[]) => {
        try {
          if (onValueChange) {
            onValueChange({
              target: {
                value: value,
                name: name ?? "multi-select",
              },
            });
          }
        } catch (error) {
          console.error(error);
        }
      };

      const debouncedSearch = React.useMemo(
        () =>
          debounce((val: string) => {
            // Only search if the term is not found in current options
            if (
              optionData?.find((item) =>
                getLabel(item)?.toLowerCase()?.includes(val?.toLowerCase())
              )
            ) {
              return;
            }

            if (onSearch) {
              onSearch(val);
            }
          }, 500),
        [optionData, onSearch, getLabel]
      );

      const handleSearchChange = (value: string) => {
        setSearchTerm(value);
        debouncedSearch(value);
      };

      const handleInputKeyDown = (
        event: React.KeyboardEvent<HTMLInputElement>
      ) => {
        if (event.key === "Enter") {
          setIsPopoverOpen(true);
        } else if (event.key === "Backspace" && !event.currentTarget.value) {
          const newSelectedValues = [...selectedValues];
          newSelectedValues.pop();
          setSelectedValues(newSelectedValues);
          handleValueChange(newSelectedValues);
        }
      };

      const toggleOption = (option: string) => {
        const newSelectedValues = selectedValues.includes(option)
          ? selectedValues.filter((value) => value !== option)
          : [...selectedValues, option];
        setSelectedValues(newSelectedValues);
        handleValueChange(newSelectedValues);
      };

      const handleClear = () => {
        setSelectedValues([]);
        handleValueChange([]);
      };

      const handleTogglePopover: any = () => {
        setIsPopoverOpen((prev) => {
          if (onOpen) {
            onOpen(!prev);
          }

          return !prev;
        });
      };

      const handleScrollTop = () => {
        try {
          if (dropdownRef.current) {
            dropdownRef.current?.scrollTo({ top: 0, behavior: "auto" });
          }
        } catch (error) {
          console.error(error);
        }
      };

      const clearExtraOptions = () => {
        const newSelectedValues = selectedValues.slice(0, maxCount);
        setSelectedValues(newSelectedValues);
        handleValueChange(newSelectedValues);
      };

      const toggleAll = () => {
        if (optionData && selectedValues?.length === optionData?.length) {
          handleClear();
        } else {
          const allValues: string[] = Array.isArray(optionData)
            ? optionData?.map((option) => getValue(option))
            : [];
          setSelectedValues(allValues);
          handleValueChange(allValues);
        }
      };

      return (
        <Popover
          open={isPopoverOpen}
          onOpenChange={setIsPopoverOpen}
          modal={modalPopover}
        >
          <div
            className={classNames(
              "flex flex-col gap-1.5 items-center",
              containerClassName
            )}
          >
            {label && (
              <InputLabel
                className={classNames("w-full", labelClassName)}
                required={required}
              >
                {label}
              </InputLabel>
            )}
            <PopoverTrigger asChild>
              <Button
                ref={ref}
                {...props}
                onClick={handleTogglePopover}
                className={classNames(
                  "multi-select-trigger flex w-full rounded-md border min-h-input h-auto items-center justify-between [&_svg]:pointer-events-auto",
                  "bg-white hover:bg-slate-50 !border-light !py-1 !px-3 shadow-none",
                  className
                )}
                value={value}
              >
                {selectedValues?.length > 0 ? (
                  <div className="trigger-wrapper flex justify-between items-center w-full">
                    <div className="selected-container flex flex-wrap items-center">
                      {selectedValues
                        ?.slice(0, maxCount)
                        .map((value, index) => {
                          const option =
                            Array.isArray(optionData) && optionData?.length > 0
                              ? optionData?.find((o) => getValue(o) === value)
                              : null;

                          const IconComponent = option?.icon;

                          if (!option) return null;

                          return (
                            <Badge
                              key={value + String(index)}
                              className={classNames(
                                "capitalize",
                                isAnimating ? "animate-bounce" : "",
                                multiSelectVariants({ variant })
                              )}
                              style={{ animationDuration: `${animation}s` }}
                              variant="destructive"
                            >
                              {IconComponent && (
                                <IconComponent className="h-4 w-4 mr-2" />
                              )}
                              {getLabel(option)}

                              <span
                                role="button"
                                className="ml-2 cursor-pointer hover:bg-red-100 transition-colors"
                                onClick={(event) => {
                                  event.stopPropagation();
                                  toggleOption(getValue(option));
                                }}
                              >
                                <XCircle className="h-4 w-4" />
                              </span>
                            </Badge>
                          );
                        })}
                      {selectedValues?.length > maxCount && (
                        <Badge
                          className={classNames(
                            "bg-transparent text-font border-font/1 hover:bg-transparent",
                            isAnimating ? "animate-bounce" : "",
                            multiSelectVariants({ variant })
                          )}
                          style={{ animationDuration: `${animation}s` }}
                          variant="destructive"
                        >
                          {`+ ${
                            selectedValues?.length - maxCount
                          } ${morePlaceholder}`?.trim()}

                          <span
                            role="button"
                            className="ml-2  cursor-pointer hover:bg-red-100 transition-colors"
                            onClick={(event) => {
                              event.stopPropagation();
                              clearExtraOptions();
                            }}
                          >
                            <XCircle className="h-4 w-4" />
                          </span>
                        </Badge>
                      )}
                      <span className="text-sm text-font/50 mx-1">
                        {placeholder}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span
                        role="button"
                        className="h-4 mx-0.5 cursor-pointer hover:bg-red-100 transition-colors"
                        onClick={(event) => {
                          event.stopPropagation();
                          handleClear();
                        }}
                      >
                        <XIcon className="h-4" />
                      </span>
                      <Separator
                        orientation="vertical"
                        className="flex min-h-6 h-full"
                      />
                      <ChevronDown className="h-4 mx-2 cursor-pointer text-inherit opacity-50" />
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-between w-full mx-auto">
                    <span className="text-sm text-font/50 mx-1">
                      {placeholder}
                    </span>
                    <ChevronDown className="h-4 cursor-pointer text-inherit opacity-50" />
                  </div>
                )}
              </Button>
            </PopoverTrigger>
          </div>
          <PopoverContent
            ref={dropdownRef}
            align="start"
            onEscapeKeyDown={() => setIsPopoverOpen(false)}
          >
            <Command>
              <div className="sticky -top-2 flex items-center gap-2.5 bg-white px-4 pt-6 pb-2 z-10">
                <CommandInput
                  placeholder={addNew ? "Search or + Add new" : "Search..."}
                  onKeyDown={handleInputKeyDown}
                  className="focus:outline-0 focus:ring-0 h-input px-3 !py-1 w-full flex-1 mx-auto"
                  containerClassName="pb-1"
                  onValueChange={(value) => handleSearchChange(value)}
                  value={searchTerm}
                />
                {resetAction && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          title="reset"
                          className="w-max h-input aspect-square border border-light hover:bg-slate-50 rounded-md flex items-center justify-center p-1.5"
                          onClick={() => {
                            setSearchTerm("");
                            handleScrollTop();
                            resetAction?.();
                          }}
                        >
                          <RiResetLeftFill />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent>Reset</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
              <CommandList
                onScroll={() => setCommandListCount((prev) => prev + 15)}
              >
                <CommandEmpty
                  className={classNames("!text-inherit", addNew ? "!py-3" : "")}
                >
                  {pagination && dataLoading ? (
                    <Loader center />
                  ) : addNew ? (
                    <button
                      className="text-sm font-normal text-start px-3 flex items-center justify-start gap-2 w-full"
                      onClick={(e) => {
                        e?.stopPropagation();
                        const newOption: any = {
                          [optionKeys && optionKeys?.label
                            ? optionKeys?.label
                            : "label"]: searchTerm,
                          [optionKeys && optionKeys?.value
                            ? optionKeys?.value
                            : "value"]: searchTerm,
                        };

                        if (
                          !(
                            Array.isArray(optionData) && optionData?.length > 0
                          ) ||
                          !optionData?.find(
                            (option) => option.label === searchTerm
                          )
                        ) {
                          setOptionData((prev) => [newOption, ...(prev ?? [])]);
                        }

                        toggleOption(getValue(newOption));
                        setIsPopoverOpen(false);
                      }}
                    >
                      <PlusSquare className="h-4 w-4 text-main" />
                      <span>
                        Add <b>{`"${searchTerm}"`}</b>
                      </span>
                    </button>
                  ) : (
                    <div className="text-base text-red-500 px-3 py-6 text-center">
                      No results found.
                    </div>
                  )}
                </CommandEmpty>

                <CommandGroup>
                  {Array.isArray(optionData) && optionData?.length > 0 && (
                    <CommandItem
                      key="all"
                      onSelect={toggleAll}
                      className="cursor-pointer"
                    >
                      <div
                        className={classNames(
                          "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                          selectedValues?.length === optionData?.length
                            ? "bg-primary text-primary-foreground"
                            : "opacity-50 [&_svg]:invisible"
                        )}
                      >
                        <CheckIcon className="h-4 w-4 text-white" />
                      </div>
                      <span>(Select All)</span>
                    </CommandItem>
                  )}

                  {Array.isArray(optionData) &&
                    optionData?.length > 0 &&
                    optionData
                      ?.slice(0, commandListCount)
                      ?.map((option, index) => {
                        const isSelected = selectedValues.includes(
                          getValue(option)
                        );

                        return (
                          <CommandItem
                            key={getValue(option) + String(index)}
                            onSelect={() => toggleOption(getValue(option))}
                            className="cursor-pointer"
                          >
                            <div
                              className={classNames(
                                "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                                isSelected
                                  ? "bg-primary text-primary-foreground"
                                  : "opacity-50 [&_svg]:invisible"
                              )}
                            >
                              <CheckIcon className="h-4 w-4 text-white" />
                            </div>
                            {React.isValidElement(option.icon) && (
                              <option.icon className="mr-2 h-4 w-4 text-font/50" />
                            )}
                            <span>
                              {getLabel(option)}
                            </span>
                          </CommandItem>
                        );
                      })}

                  {infiniteLoading && (
                    <CommandItem className="text-center">
                      <Loader center />
                    </CommandItem>
                  )}
                  {pagination?.hasNextPage && (
                    <CommandItem ref={loaderRef}>
                      <div className="h-2 w-full" />
                    </CommandItem>
                  )}
                </CommandGroup>
                <CommandSeparator />
                <CommandGroup ref={loaderRef}>
                  <div className="flex items-center justify-between">
                    {selectedValues?.length > 0 && (
                      <>
                        <CommandItem
                          onSelect={handleClear}
                          className="flex-1 justify-center cursor-pointer"
                        >
                          Clear
                        </CommandItem>
                        <Separator
                          orientation="vertical"
                          className="flex min-h-6 h-full"
                        />
                      </>
                    )}
                    <CommandItem
                      onSelect={() => setIsPopoverOpen(false)}
                      className="flex-1 justify-center cursor-pointer max-w-full"
                    >
                      Close
                    </CommandItem>
                  </div>
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
          {animation > 0 && selectedValues?.length > 0 && (
            <WandSparkles
              className={classNames(
                "cursor-pointer my-2 text-foreground bg-background w-3 h-3",
                isAnimating ? "" : "text-muted-foreground"
              )}
              onClick={() => setIsAnimating(!isAnimating)}
            />
          )}
        </Popover>
      );
    }
  )
);

MultiSelect.displayName = "MultiSelect";

export { MultiSelect };
export type { MultiSelectTarget, MultiSelectType, OptionType };
