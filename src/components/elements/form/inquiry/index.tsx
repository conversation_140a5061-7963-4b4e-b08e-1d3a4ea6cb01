"use client";

import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Footer,
  DialogHeader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Loader } from "@/components/ui/loader";
import { currentFormStep } from "@/screens/elements/@redux/selectors";
import { Actions } from "@/screens/elements/@redux/slice";
import classNames from "classnames";
import { ArrowLeft, ArrowRight, X } from "lucide-react";
import {
  Fragment,
  JSX,
  LazyExoticComponent,
  ReactNode,
  Suspense,
  useEffect,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { HighlightedText } from "../../text/highlight";

interface FormDataType {
  headerClassName?: string;
  hideHeader?: boolean;
  title?: string | ReactNode;
  highlight?: string[];
  footerType?: string | "next" | "navigate" | "last";
  component: LazyExoticComponent<(d: any) => JSX.Element>;
}

interface QuoteRequestFormType {
  className?: string;
  children: ReactNode;
  data: FormDataType[];
  stepCount?: boolean;
}

const QuoteRequestForm = ({
  className,
  children,
  data,
  stepCount,
}: QuoteRequestFormType) => {
  const dispatch = useDispatch();
  const currentStep = useSelector(currentFormStep);

  useEffect(() => {
    return () => {
      dispatch(Actions.setCurrentFormStep(1));
    };
  }, [dispatch]);

  const handleSteps = (step: number, type: "prev" | "next" | "close") => {
    if (
      !(type === "prev" && step > 0) &&
      !(type === "next" && step < data?.length + 1)
    ) {
      return;
    }
    dispatch(Actions.setCurrentFormStep(step));
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className={classNames(
          "bg-[#FAF7F7] !p-0 !max-w-full md:!max-w-max overflow-auto max-h-full h-screen md:h-max md:max-h-[80vh] !gap-2",
          className
        )}
        hideClose
      >
        {data?.map((form: FormDataType, index: number) => {
          if (currentStep !== index + 1) return null;

          return (
            <Fragment key={index}>
              {!form?.hideHeader && (
                <DialogHeader
                  className={classNames(
                    "bg-white px-6 py-3 shadow sticky top-0 z-20 max-h-12 outline-0 ring-0",
                    form?.headerClassName
                  )}
                >
                  <div className="flex items-center justify-between gap-4">
                    <DialogTitle className="flex items-center gap-1">
                      {typeof form?.title === "string" ? (
                        <HighlightedText
                          title={form?.title}
                          highlight={form?.highlight}
                        />
                      ) : (
                        form?.title
                      )}
                    </DialogTitle>
                    <DialogClose className="drop-shadow">
                      <X />
                    </DialogClose>
                  </div>
                </DialogHeader>
              )}

              {stepCount && (
                <div className="text-sm bg-main/20 max-w-max px-2 rounded-sm ms-4 me-auto mb-3">
                  Step {Number(currentStep)} of {data?.length}
                </div>
              )}

              <div className="flex-1 w-full overflow-auto">
                {form?.component && (
                  <Suspense
                    fallback={
                      <div className="relative min-w-[40vw] py-20">
                        <Loader center box big />
                      </div>
                    }
                  >
                    <form.component />
                  </Suspense>
                )}
              </div>

              {form?.footerType && (
                <DialogFooter className="w-full h-max flex !flex-row items-center gap-4 !justify-between bg-white px-5 py-3 border-t border-slate-200 shadow-inner">
                  {form?.footerType === "navigate" ? (
                    <>
                      <Button
                        className="flex items-center gap-2"
                        variant="ghost-brown-revert"
                        onClick={() => handleSteps(currentStep - 1, "prev")}
                      >
                        <ArrowLeft />
                        <span>Previous</span>
                      </Button>
                      <Button
                        className="flex items-center gap-2 "
                        variant="main-revert"
                        onClick={() => handleSteps(currentStep + 1, "next")}
                      >
                        <span>
                          {currentStep === data?.length - 1
                            ? "Submit"
                            : "Continue"}
                        </span>
                        <ArrowRight />
                      </Button>
                    </>
                  ) : (
                    form?.footerType === "last" && (
                      <DialogClose
                        className={classNames(
                          "flex items-center gap-2 mx-auto",
                          buttonVariants({ variant: "main-revert" })
                        )}
                      >
                        <ArrowLeft />
                        <span>Back to Page</span>
                      </DialogClose>
                    )
                  )}
                </DialogFooter>
              )}
            </Fragment>
          );
        })}
      </DialogContent>
    </Dialog>
  );
};

export default QuoteRequestForm;
