import { createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "@/redux/root-reducer";
import * as API from '../@api/update';
import { toast } from "sonner"; // Toast notification library

// Async thunk to fetch profile details
export const getProfileDetails = createAsyncThunk<any, void, { state: RootState }>(
  "user_buyer_settings/getProfileDetails",
  async (_, { rejectWithValue }) => {
    try {
      const response = await API.getProfileDetails();
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to fetch profile details", {
        classNames: { toast: "!bg-red-500 !text-white" },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

// Async thunk to delete user account
export const deleteAccount = createAsyncThunk<any, void, { state: RootState }>(
  "user_buyer_settings/deleteAccount",
  async (_, { rejectWithValue }) => {
    try {
      const response = await API.deleteAccount();
      toast.success(response?.meta?.message || "Account deleted successfully", {
        classNames: { toast: "!bg-green-500 !text-white" },
      });
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to delete account", {
        classNames: { toast: "!bg-red-500 !text-white" },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

// Async thunk to update profile image
export const updateProfileImage = createAsyncThunk<any, File, { state: RootState }>(
  "user_buyer_settings/updateProfileImage",
  async (file, { rejectWithValue }) => {
    try {
      const response = await API.updateProfileImage(file);
      toast.success(response?.meta?.message || "Profile image updated successfully", {
        classNames: { toast: "!bg-green-500 !text-white" },
      });
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to update profile image", {
        classNames: { toast: "!bg-red-500 !text-white" },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

// Async thunk to update profile details
export const updateProfileDetails = createAsyncThunk<
  any,
  { name: string; role: string; country: string; address: string },
  { state: RootState }
>(
  "user_buyer_settings/updateProfileDetails",
  async (payload, { rejectWithValue }) => {
    try {
      const response = await API.updateProfileDetails(payload);
      toast.success(response?.meta?.message || "Profile details updated successfully", {
        classNames: { toast: "!bg-green-500 !text-white" },
      });
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to update profile details", {
        classNames: { toast: "!bg-red-500 !text-white" },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);

// Async thunk to change user password
export const changePassword = createAsyncThunk<
  any,
  { old_password: string; new_password: string; confirm_password: string },
  { state: RootState }
>(
  "user_buyer_settings/changePassword",
  async (payload, { rejectWithValue }) => {
    try {
      const response = await API.changeUserPassword(payload);
      toast.success(response?.meta?.message || "Password changed successfully", {
        classNames: { toast: "!bg-green-500 !text-white" },
      });
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Failed to change password", {
        classNames: { toast: "!bg-red-500 !text-white" },
      });
      return rejectWithValue(error?.response || error?.message);
    }
  }
);
