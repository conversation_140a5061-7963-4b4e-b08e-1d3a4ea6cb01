"use client";

import { But<PERSON> } from "@/components/ui/button";
import { InputLabel } from "@/components/ui/input/components/label";
import { Input } from "@/components/ui/input/text";
import { removeAtIndex } from "@/lib/utils/data";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import { Plus, Trash2, X } from "lucide-react";
import { ChangeEvent, FC, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";

const Clientele = ({ values }: { values: any }) => {
  const dispatch = useDispatch();

  const featureLimit =
    Array.isArray(values?.clientele) && values?.clientele?.length > 5;

  const featurePayload = useMemo(() => {
    return "";
  }, []);

  useEffect(() => {
    if (!values?.clientele) {
      const payload: any = {
        ...values,
        clientele: [featurePayload],
      };
      dispatch(Actions.setFormValues(payload));
    }
  }, [dispatch, featurePayload, values, values?.clientele]);

  const handleAddClientele = () => {
    try {
      if (featureLimit) return;

      const payload: any = {
        ...values,
        clientele: Array.isArray(values?.clientele)
          ? [...values?.clientele, featurePayload]
          : [featurePayload],
      };
      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const handleRemoveFeature = (index: number) => {
    try {
      if (values?.clientele?.length === 1 && index === 0) {
        const payload: any = {
          ...values,
          clientele: Array.isArray(values?.clientele)
            ? [...values?.clientele]
            : [],
        };
        payload.clientele[index] = "";

        dispatch(Actions.setFormValues(payload));
      } else {
        const payload: any = {
          ...values,
          clientele: Array.isArray(values?.clientele)
            ? [...values?.clientele]
            : [],
        };
        payload.clientele = removeAtIndex(payload.clientele, index);

        dispatch(Actions.setFormValues(payload));
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleFeatureChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number
  ) => {
    try {
      const value = e.target.value;

      if (!values?.clientele) values.clientele = [];

      const payload: any = {
        ...values,
        clientele: Array.isArray(values?.clientele)
          ? [...values?.clientele]
          : [],
      };
      if (!payload?.clientele[index]) {
        payload.clientele[index] = "";
      }
      payload.clientele[index] = value;

      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col items-start gap-6">
      <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
        <div className="flex items-center justify-start gap-1.5">
          <InputLabel>Clientele</InputLabel>
          <span className="text-stone-500 text-[0.7rem]">
            (6 lines, 20 chars each)
          </span>
        </div>
      </div>

      <div className="w-full grid grid-col-1 gap-6 md:grid-cols-2">
        {Array.isArray(values?.clientele) &&
          values?.clientele?.length > 0 &&
          values?.clientele?.map((item: string, index: number) => {
            return (
              <ClienteleItem
                key={index}
                dataLength={values?.clientele?.length}
                first={index === 0 && values?.clientele?.length === 1}
                onDelete={() => handleRemoveFeature(index)}
                onChange={(e) => handleFeatureChange(e, index)}
                client={item}
              />
            );
          })}
      </div>
      {!featureLimit && (
        <Button
          onClick={() => handleAddClientele()}
          className="rounded-md flex items-center gap-2 border-dashed"
        >
          <Plus /> Add Client
        </Button>
      )}
    </div>
  );
};

interface FeatureType {
  onDelete: () => void;
  onChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  client: string;
  dataLength: number;
  first: boolean;
}

const ClienteleItem: FC<FeatureType> = ({
  first,
  onDelete,
  client,
  dataLength,
  onChange,
}) => {
  return (
    <div className="flex-1 flex items-start gap-1">
      <div className="w-max flex-1 flex flex-col items-center justify-start gap-2 whitespace-nowrap text-ellipsis overflow-hidden">
        <Input
          type="text"
          name="title"
          value={client}
          onChange={onChange}
          placeholder="Client Title"
          containerClassName="w-full"
        />
      </div>
      {!(dataLength === 1 && !client) && (
        <button
          onClick={() => onDelete()}
          className="flex items-center justify-center rounded-sm bg-white hover:bg-stone-100 transition-all w-auto h-9 [&>svg]:w-6 [&>svg]:h-6"
          title={first ? "Clear" : "Delete"}
        >
          {first ? <X /> : <Trash2 />}
        </button>
      )}
    </div>
  );
};

export default Clientele;
