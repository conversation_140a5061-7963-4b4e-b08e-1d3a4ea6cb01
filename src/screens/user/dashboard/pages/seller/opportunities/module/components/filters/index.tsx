"use client";


import LocationSearchFilter from "@/components/elements/filters/listings/desktop/location";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { InputTargetType } from "@/components/ui/input/types";
import { ChangeEvent, useState } from "react";
import { useSelector } from "react-redux";
import { sub_sub_categories } from "../../../../listing/module/company/@redux/selectors";

const PreferencesFilter = () => {
  const { data: categoriesData } = useSelector(sub_sub_categories);
  const [values, setValues] = useState<any>(null);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
  ) => {
    try {
      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex items-center flex-wrap gap-3">
      <LocationSearchFilter />

      <MultiSelect
        name="category"
        placeholder="Category"
        morePlaceholder=""
        onValueChange={handleChange}
        defaultValue={values?.category}
        value={values?.category}
        options={categoriesData}
        optionKeys={{
          label: "sub_sub_category_name",
          value: "_id",
        }}
        maxCount={1}
      />

      <MultiSelect
        name="order_value"
        placeholder="Order Value"
        morePlaceholder=""
        onValueChange={handleChange}
        defaultValue={values?.order_value}
        value={values?.order_value}
        options={[
          { label: "Order 1", value: "1" },
          { label: "Order 2", value: "2" },
          { label: "Order 3", value: "3" },
        ]}
        maxCount={1}
      />

      <MultiSelect
        name="buyer_type"
        placeholder="Buyer Type"
        morePlaceholder=""
        onValueChange={handleChange}
        defaultValue={values?.buyer_type}
        value={values?.buyer_type}
        options={[
          { label: "Buyer 1", value: "1" },
          { label: "Buyer 2", value: "2" },
          { label: "Buyer 3", value: "3" },
        ]}
        maxCount={1}
      />

      <MultiSelect
        name="languages"
        placeholder="Languages"
        morePlaceholder=""
        onValueChange={handleChange}
        defaultValue={values?.languages}
        value={values?.languages}
        options={[
          { label: "language 1", value: "1" },
          { label: "language 2", value: "2" },
          { label: "language 3", value: "3" },
        ]}
        maxCount={1}
      />
    </div>
  );
};

export default PreferencesFilter;
