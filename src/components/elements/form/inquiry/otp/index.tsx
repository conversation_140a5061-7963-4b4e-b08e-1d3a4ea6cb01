"use client";

import { HighlightedText } from "@/components/elements/text/highlight";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input/otp";

const QuoteOTP = () => {
  return (
    <div className="min-w-[50vw] flex flex-col items-center justify-center gap-8 py-10 px-4 bg-[#FAF7F7]">
      <h5 className="text-2xl font-bold text-center">
        <HighlightedText title="OTP verification" highlight={["otp"]} />
      </h5>

      <InputOTP maxLength={4} containerClassName="!gap-5" autoFocus>
        {[...Array(+4).keys()]?.map((_, index) => {
          return (
            <InputOTPGroup key={index} className="flex-1">
              <InputOTPSlot
                index={index}
                className="!h-12 !min-w-14 !w-full !max-w-20 !flex-1"
              />
            </InputOTPGroup>
          );
        })}
      </InputOTP>
    </div>
  );
};

export default QuoteOTP;
