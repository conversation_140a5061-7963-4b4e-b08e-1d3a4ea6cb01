"use client";
import React, { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

const SellerBenefitsCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const carouselItems = [
    {
      id: "gateway",
      title: "Your Gateway to Growth",
      image: "assets/pages/seller/Group 5396.png",
    },
    {
      id: "network",
      title: "Verified Buyer Network",
      image: "assets/pages/seller/Group 5415.png",
    },
    {
      id: "expertise",
      title: "Showcase Your Expertise",
      image: "assets/pages/seller/Group 5416.png",
    },
  ];
  
  const handleTabChange = (value: string) => {
    const index = carouselItems.findIndex((item) => item.id === value);
    setCurrentSlide(index);
  };
  
  return (
    <div className="w-[100%] sm:w-[95%] bg-[#FAFAFA] mb-6 mx-auto p-4 sm:p-3 lg:p-10 mt-6 rounded-sm">
      <h1 className="text-[17px] lg:text-3xl xl:text-4xl sm:text-2xl mt-4 ml-1 lg:ml-4 sm:ml-3 font-bold text-gray-900 mb-8 sm:mb-4">
        Why Join As A Seller?
      </h1>
      
      {/* Tabs Navigation */}
      <Tabs
        value={carouselItems[currentSlide].id}
        onValueChange={handleTabChange}
      >
        <div className="w-full overflow-x-auto mb-6 sm:mb-4">
          <TabsList className="border-b border-gray-200 bg-transparent p-0 h-auto min-w-full flex justify-start">
            {carouselItems.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={`text-sm sm:text-base lg:text-lg xl:text-xl pb-2 px-3 sm:px-4 lg:px-6 
                            flex-shrink-0 whitespace-nowrap border-b-2 transition-all duration-200
                            ${
                              currentSlide ===
                              carouselItems.findIndex(
                                (item) => item.id === tab.id
                              )
                                ? "text-red-500 border-red-500 font-semibold"
                                : "text-black border-transparent hover:text-red-500 hover:border-red-300"
                            }`}
              >
                {tab.title}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
      
      {/* Carousel */}
      <Carousel className="w-full relative">
        <CarouselContent>
          {carouselItems.map((item, index) => (
            <CarouselItem
              key={item.id}
              className={index === currentSlide ? "block" : "hidden"}
            >
              <div className="flex justify-center w-full">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-4/5 sm:w-full h-auto"
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        
        {/* Custom Carousel Indicators */}
        <div className="flex justify-center mt-2 sm:mt-4">
          {carouselItems.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                setCurrentSlide(index);
                handleTabChange(carouselItems[index].id);
              }}
              className={`w-3 h-3 mx-1 rounded-full transition-colors duration-200 ${
                index === currentSlide ? "bg-black" : "bg-gray-300"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </Carousel>
    </div>
  );
};

export default SellerBenefitsCarousel;