import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { PortfolioUpdateType } from "../module/add-portfolio/types";
import { thunks } from "./thunk";

interface APTStateType {
  portfolioList: APIResponseType;
  portfolioDetails: APIResponseType;
}

const APIState: APTStateType = {
  portfolioList: {
    data: null,
    loading: true,
    meta: null,
  },
  portfolioDetails: {
    data: null,
    loading: true,
    meta: null,
  },
};

interface stateType extends APTStateType {
  searchValue: string;
  currentPage: string;
}

const initialState = {
  searchValue: "",
  currentPage: "1",
  ...APIState,
} satisfies stateType as stateType;

const slice = createSlice({
  name: "user_portfolio",
  initialState,
  reducers: {
    setSearchValue: (state, action: PayloadAction<string>) => {
      state.searchValue = action.payload;
    },
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    setPortfolioDetails: (
      state,
      action: PayloadAction<Record<string, any> | null>
    ) => {
      if (action?.payload) {
        state.portfolioDetails.data = {
          ...(state.portfolioDetails.data || {}),
          ...action.payload,
        };
      } else {
        state.portfolioDetails = { ...initialState.portfolioDetails };
      }
    },
  },
  extraReducers: (builder) => {
    Object.entries(thunks).forEach(([_, thunk]) => {
      createThunkCase(builder, thunk, APIState);
    });
  },
});

export const Actions = slice.actions;
export default slice.reducer;
