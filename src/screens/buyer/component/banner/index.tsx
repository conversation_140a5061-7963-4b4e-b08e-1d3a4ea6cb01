"use client";

// import backgroundImage from "./Group 5406.png";
import { useRouter } from "next/navigation";

export default function Banner() {
  const router = useRouter();

  return (
    <div className="relative w-full " style={{ minHeight: "180px" }}>
      {/* Background Image Container - Fully Responsive */}
      <div className="w-full h-full overflow-hidden">
        {/* TODO */}
        {/* Add correct banner */}

        {/* <img
          src={backgroundImage}
          alt="Business background"
          className="w-full h-full object-cover object-center brightness-75"
          style={{
            maxWidth: "100%",
            maxHeight: "2200px",
            minHeight: "180px",
          }}
        /> */}
      </div>

      {/* Text Overlay - Responsive positioning */}
      <div className="absolute inset-0 flex items-center">
        <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8 2xl:pl-10 2xl:mx-16 2xl:max-w-full">
          {/* Text container - fully responsive */}
          <div className="max-w-[150px] mb-3 pr-4 xs:max-w-[200px] sm:max-w-sm md:max-w-[350px] md:mt-1 lg:max-w 2xl:max-w-[900px] 2xl:ml-0">
            <h1 className="text-[18px] xs:text-xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-5xl 2xl:text-6xl font-bold text-white mb-0 sm:mb-2 md:mb-3 lg:mb-4 md:mt-3">
              Start <br />
              Buying Today!
            </h1>
            <p className="text-[10px] xs:text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl 2xl:text-4xl text-white mb-1 sm:mb-3 md:mb-4 lg:mb-6">
              Don't miss out on connecting with serious buyers. Join Now and
              grow your business!
            </p>
            <button
              onClick={() => router.push("/free-listing")}
              className="bg-red-500 hover:bg-red-600 text-white font-medium 
                               text-xs sm:text-sm md:text-base 2xl:text-lg
                               py-1 xs:py-1.5 sm:py-2 md:py-2.5 lg:py-2
                               px-2 xs:px-4 sm:px-5 md:px-6 lg:px-8
                               rounded-full transition duration-300 "
            >
              Join Now For Free
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
