"use client";

import * as AccordionPrimitive from "@radix-ui/react-accordion";
import classNames from "classnames";
import { ChevronDown } from "lucide-react";
import * as React from "react";
import { Loader } from "../loader";

const Accordion = AccordionPrimitive.Root;

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={classNames("border-b border-slate-200", className)}
    {...props}
  />
));
AccordionItem.displayName = "AccordionItem";

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={classNames(
        "flex flex-1 items-center justify-between py-4 text-sm font-medium transition-all hover:underline text-left [&[data-state=open]>svg]:rotate-180",
        className
      )}
      {...props}
    >
      {children}
      <ChevronDown className="h-4 w-4 shrink-0 text-stone-500 transition-transform duration-200" />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

interface AccordionContentType
  extends React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content> {
  disabled?: boolean;
  loading?: boolean;
}
const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  AccordionContentType
>(({ className, children, disabled, loading, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className={classNames(
      "relative overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",
      {
        relative: disabled || loading,
      },
      disabled &&
        "after:absolute after:top-0 after:left-0 after:right-0 after:bottom-0 after:w-full after:h-full after:bg-stone-100 after:opacity-60 after:z-30 after:rounded-md"
    )}
    {...props}
  >
    {loading && <Loader big box center className="bg-white" />}
    <div className={classNames("pb-4 pt-0", className)}>{children}</div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { Accordion, AccordionContent, AccordionItem, AccordionTrigger };
