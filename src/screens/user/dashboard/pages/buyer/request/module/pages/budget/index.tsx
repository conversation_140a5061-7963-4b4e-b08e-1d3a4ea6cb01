'use client';

import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input/text";

interface BudgetProps {
  onBudgetChange?: (details: BudgetDetails) => void;
  initialDetails?: any; // Allow any structure to handle different format inputs
}

interface BudgetDetails {
  budgetRange: {
    min: number;
    max: number;
    currency: string;
  };
}

const Budget: React.FC<BudgetProps> = ({ 
  onBudgetChange, 
  initialDetails = {
    budgetRange: {
      min: 0,
      max: 0,
      currency: "USD"
    }
  }
}) => {
  // Initialize with default values to ensure budgetRange structure always exists
  const [budgetDetails, setBudgetDetails] = useState<BudgetDetails>({
    budgetRange: {
      min: 0,
      max: 0,
      currency: "USD"
    }
  });

  // Parse initialDetails on component mount or when changed
  useEffect(() => {
    // Handle the case where initialDetails might have a different structure
    if (initialDetails) {
      if (initialDetails.budgetRange) {
        // If budgetRange already exists, use it
        setBudgetDetails(initialDetails);
      } else if (initialDetails.budget !== undefined) {
        // If we have a 'budget' property, convert to budgetRange format
        const budgetValue = parseFloat(initialDetails.budget) || 0;
        setBudgetDetails({
          budgetRange: {
            min: budgetValue,
            max: budgetValue,
            currency: "USD"
          }
        });
      }
    }
  }, [initialDetails]);

  const handleMinBudgetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = value === '' ? 0 : parseInt(value) || 0;
    const updatedDetails = {
      ...budgetDetails,
      budgetRange: {
        ...budgetDetails.budgetRange,
        min: numericValue
      }
    };
    setBudgetDetails(updatedDetails);
    if (onBudgetChange) {
      onBudgetChange(updatedDetails);
    }
  };

  const handleMaxBudgetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = value === '' ? 0 : parseInt(value) || 0;
    const updatedDetails = {
      ...budgetDetails,
      budgetRange: {
        ...budgetDetails.budgetRange,
        max: numericValue
      }
    };
    setBudgetDetails(updatedDetails);
    if (onBudgetChange) {
      onBudgetChange(updatedDetails);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">Budget Range</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <div className="flex flex-col">
            <label htmlFor="minBudget" className="mb-2 text-sm font-medium text-gray-700">Minimum Budget (USD)</label>
            <Input
              id="minBudget"
              name="minBudget"
              type="number"
              placeholder="Enter minimum budget"
              value={budgetDetails.budgetRange.min === 0 ? '' : budgetDetails.budgetRange.min}
              onChange={handleMinBudgetChange}
              min="0"
            />
          </div>
          <div className="flex flex-col">
            <label htmlFor="maxBudget" className="mb-2 text-sm font-medium text-gray-700">Maximum Budget (USD)</label>
            <Input
              id="maxBudget"
              name="maxBudget"
              type="number"
              placeholder="Enter maximum budget"
              value={budgetDetails.budgetRange.max === 0 ? '' : budgetDetails.budgetRange.max}
              onChange={handleMaxBudgetChange}
              min="0"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Budget;