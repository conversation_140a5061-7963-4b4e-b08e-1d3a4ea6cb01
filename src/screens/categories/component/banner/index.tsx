import React from 'react';

// TODO
// image correction

export default function ImageWithTextOverlay() {
  return (
    <div className="relative w-full h-48 sm:h-60 md:h-72">
 
      <div
        className="w-full h-full bg-center brightness-50 bg-cover shadow-lg rounded"
        style={{ backgroundImage: `url(${"/assets/pages/buyer/electronic.jpeg"})` }}
      ></div>

      
      <div className="absolute inset-0 flex flex-col items-center justify-center z-10 px-4">
        <h2 className="text-white text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-center leading-tight">
          Consumer Electronics
        </h2>
        <p className="text-white text-sm sm:text-base md:text-lg font-medium text-center mt-1 sm:mt-2 leading-tight">
          Discover new and trending products
        </p>
      </div>
    </div>
  );
}