"use client";

import * as React from "react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import classNames from "classnames";

interface DualRangeSliderTargetType {
  target: {
    name: string;
    value: number[];
  };
}

interface DualRangeSliderType
  extends React.ComponentProps<typeof SliderPrimitive.Root> {
  onSlide?: (d: DualRangeSliderTargetType) => void;
  startName?: string;
  endName?: string;
  labelPosition?: "top" | "bottom";
  rangeLabel?: (value: number | undefined) => React.ReactNode;
}

const DualRangeSlider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  DualRangeSliderType
>(
  (
    { className, rangeLabel, onSlide, labelPosition = "top", ...props },
    ref
  ) => {
    const initialValue = Array.isArray(props.value)
      ? props.value
      : [props.min, props.max];

    const handleChange = (val: number[]) => {
      if (onSlide) {
        onSlide({
          target: {
            name: name ?? "dual-range-slider",
            value: val,
          },
        });
      }
    };
    return (
      <SliderPrimitive.Root
        ref={ref}
        className={classNames(
          "relative flex w-full touch-none select-none items-center",
          className
        )}
        onValueChange={handleChange}
        {...props}
      >
        <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-secondary">
          <SliderPrimitive.Range className="absolute h-full bg-primary" />
        </SliderPrimitive.Track>
        {initialValue.map((value, index) => (
          <React.Fragment key={index}>
            <SliderPrimitive.Thumb className="relative block h-4 w-4 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
              {rangeLabel && (
                <span
                  className={classNames(
                    "absolute flex w-full justify-center",
                    labelPosition === "top" && "-top-7",
                    labelPosition === "bottom" && "top-4"
                  )}
                >
                  {rangeLabel(value)}
                </span>
              )}
            </SliderPrimitive.Thumb>
          </React.Fragment>
        ))}
      </SliderPrimitive.Root>
    );
  }
);
DualRangeSlider.displayName = "DualRangeSlider";

export { DualRangeSlider };
export type { DualRangeSliderType, DualRangeSliderTargetType };
