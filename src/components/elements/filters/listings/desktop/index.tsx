"use client";

import useScrolled from "@/lib/hooks/useScrolled";
import AdvancedFilter from "./advanced";
import CategoryFilter from "./category";
import CompanyTypeFilter from "./company-type";
import LocationSearchFilter from "./location";
import RatingFilter from "./ratings";
import SortFilter from "./sort";
import VerifiedFilter from "./verified";
import classNames from "classnames";

const ListingFilter = ({ className }: { className?: string }) => {
  const isScrolled = useScrolled();

  return (
    <div
      className={classNames(
        "hidden md:flex items-center flex-wrap gap-3 bg-white py-6 px-0 z-30 transition-all",
        isScrolled &&
          "shadow-md sticky top-header-sm sm:top-header-md md:top-header !px-4",
        className
      )}
    >
      <LocationSearchFilter />
      <CategoryFilter />
      <RatingFilter />
      <CompanyTypeFilter />
      <VerifiedFilter />
      <AdvancedFilter />
      {isScrolled && <SortFilter />}
    </div>
  );
};

export default ListingFilter;
