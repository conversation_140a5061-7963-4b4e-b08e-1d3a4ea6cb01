"use client";

import SideMenu from "@/components/pages/user/side-menu";
import { sellerMenuData } from "@/components/pages/user/side-menu/data";
import { BuyererMenuData } from "@/components/pages/user/side-menu/buyer";
import BreadcrumbRender from "@/components/ui/breadcrumb/render";
// import CreateRouter from "@/router/helper/create-router";
// import routes from "@/router/routes/types/user/dashboard/routes";
import { usePathname } from "next/navigation";

const UserDashboard = () => {
  const pathname = usePathname();
  const isBuyer = pathname?.includes("/buyer");
  return (
    <main className="py-12 bg-white md:bg-light/20">
      <div className="container">
        <div className="flex items-start gap-6">
          <SideMenu menuData={isBuyer ? BuyererMenuData : sellerMenuData} />
          <div className="flex-1 w-full h-full">
            <BreadcrumbRender className="mb-4 font-medium" />

            {/* TODO */}
            {/* Handle Child routes */}
            {/* <CreateRouter routeData={routes} /> */}
          </div>
        </div>
      </div>
    </main>
  );
};

export default UserDashboard;
