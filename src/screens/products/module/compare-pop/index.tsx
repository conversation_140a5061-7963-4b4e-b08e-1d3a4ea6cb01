import MAIN_LOGO_ICON from "/assets/branding/icon.svg";
import { Button } from "@/components/ui/button";
import classNames from "classnames";
import { X } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";

const ComparePop = () => {
const router = useRouter();
  const [show, setShow] = useState<boolean>(true);

  if (!show) {
    return null;
  }

  return (
    <div
      className={classNames("fixed bottom-0 left-0 right-0 bg-dark py-5 z-30")}
    >
      <div className="container">
        <div className="w-full flex flex-wrap sm:flex-nowrap items-center justify-between gap-3">
          <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="relative flex items-center gap-2 bg-white rounded p-1.5"
              >
                <div className="flex-1 aspect-square max-w-10 p-0.5 border border-stone-200 rounded flex items-center justify-center">
                  <img
                    src={MAIN_LOGO_ICON}
                    alt="Aalyana"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="flex-1 h-full leading-3 pe-0">
                  <h6 className="text-wrap text-[0.81rem] leading-4 text-font">
                    Accenture Solutions Private Limited.
                  </h6>
                </div>

                <button
                  title="Remove"
                  className="absolute top-1 right-1 z-10 text-font hover:text-main hover:drop-shadow transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>

          <div className="flex items-center gap-4">
            <Button
              variant="main"
              className="ms-2 py-4 !px-10 !text-base"
              onClick={() => router.push("/products/compare/details")}
            >
              Compare
            </Button>
            <button
              title="Remove"
              className="text-white hover:text-main hover:drop-shadow transition-colors"
              onClick={() => setShow(false)}
            >
              <X className="h-7 w-7" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComparePop;
