import { ReactNode } from "react";

export interface DropdownReferenceType {
  toggle: (v: boolean) => void;
  open: boolean;
}

export interface DropdownType {
  children?: ReactNode;
  content?: ReactNode;
  dropdownStyle?: any;
  inlineAlign?: "left";
  verticalAlign?: "top";
  hover?: boolean;
  className?: string;
  contentClassName?: string;
  actionClassName?: string;
  onClose?: (data?: any) => void;
  onToggle?: (val: boolean) => void;
  open?: boolean;
}
