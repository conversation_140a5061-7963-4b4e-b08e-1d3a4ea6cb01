import FACEBOOK from "/assets/footer/social/facebook.svg";
import INSTA from "/assets/footer/social/insta.svg";
import LINKEDIN from "/assets/footer/social/linkedln.svg";
import XAPP from "/assets/footer/social/x.svg";
import YOUTU<PERSON> from "/assets/footer/social/youtube.svg";

const socialData: any[] = [
  {
    title: "Facebook",
    icon: FACEBOOK,
    url: "http://facebook.com",
  },
  {
    title: "Instagram",
    icon: INSTA,
    url: "http://instagram.com",
  },
  {
    title: "Linkedln",
    icon: LINKEDIN,
    url: "http://linkedin.com",
  },
  {
    title: "X",
    icon: XAPP,
    url: "http://x.com",
  },
  {
    title: "Youtube",
    icon: YOUTUBE,
    url: "http://youtube.com",
  },
];

const handleNavigate = (url: string) => {
  try {
    window.open(url, "_blank");
  } catch (error) {
    console.error(error);
  }
};

const FooterSocial = () => {
  return (
    <div className="flex items-center flex-wrap gap-2">
      {socialData?.map((sc, index) => (
        <button
          key={index}
          className="flex items-center justify-center"
          onClick={() => handlerouter.push(sc?.url)}
        >
          <img
            src={sc?.icon}
            alt={sc?.title}
            className="w-11 h-11 object-contain"
          />
        </button>
      ))}
    </div>
  );
};

export default FooterSocial;
