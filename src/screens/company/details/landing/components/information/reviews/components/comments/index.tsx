"use client";

import ReviewListFilter from "@/components/elements/filters/reviews/list";
import { Button } from "@/components/ui/button";
import CommentCard from "./card";

const ReviewComments = () => {
  return (
    <div>
      <ReviewListFilter
        rating={"3"}
        sort={"3"}
        actions={{
          onAddClick: () => {},
          onMediaClick: () => {},
          onRatingsChange: () => {},
          onSortChange: () => {},
        }}
      />

      <div className="mt-6 flex flex-col flex-start gap-5 [&>*]:w-full">
        {Array.from({ length: 4 }).map((_, index) => (
          <CommentCard key={index} />
        ))}

        <div className="flex items-center justify-center">
          <Button className="mt-2" variant="ghost-brown">
            Loading more
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReviewComments;
