"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import { RootDispatch } from "@/redux/store";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { thunks } from "../../@redux/thunk";
import { usePathname, useRouter } from "next/navigation";

// Sample data to match the image
const rfqData = [
  {
    rfqId: "#7461",
    title: "Plastic Ball 250 Pieces",
    dateCreated: "14 March 2025",
    status: "Close",
    responses: "2653",
  },
  {
    rfqId: "#7462",
    title: "Plastic Ball 250 Pieces",
    dateCreated: "14 March 2025",
    status: "Pending",
    responses: "2653",
  },
  {
    rfqId: "#7463",
    title: "Plastic Ball 250 Pieces",
    dateCreated: "14 March 2025",
    status: "Open",
    responses: "2653",
  },
  {
    rfqId: "#7464",
    title: "Plastic Ball 250 Pieces",
    dateCreated: "14 March 2025",
    status: "Open",
    responses: "2653",
  },
  {
    rfqId: "#7465",
    title: "Plastic Ball 250 Pieces",
    dateCreated: "14 March 2025",
    status: "Open",
    responses: "2653",
  },
  {
    rfqId: "#7466",
    title: "Plastic Ball 250 Pieces",
    dateCreated: "14 March 2025",
    status: "Open",
    responses: "2653",
  },
  {
    rfqId: "#7467",
    title: "Plastic Ball 250 Pieces",
    dateCreated: "14 March 2025",
    status: "Open",
    responses: "2653",
  },
];

const headerData = [
  {
    label: "RFQ ID",
    key: "rfqId",
    className: "!max-w-[10%] !text-center",
  },
  {
    label: "PRODUCT/SERVICE NAME",
    key: "title",
    className: "!max-w-[30%] !text-center ",
  },
  {
    label: "DATE Created",
    key: "Date Created",
    className: "!max-w-[18%] !text-center",
  },
  {
    label: "STATUS",
    key: "status",
    className: "!max-w-[12%] !text-center",
  },
  {
    label: "RESPONSES",
    key: "responses",
    className: "!max-w-[10%] !text-center ",
  },
  {
    label: "ACTIONS",
    key: "actions",
    className: "!max-w-[30%] !text-center",
  },
];

interface RFQState {
  data: any;
  loading: boolean;
  meta: any;
}

interface RootState {
  user_buyer_rfq: {
    getRFQDetails: RFQState;
  };
}

const RFQListPage = () => {
  const dispatch = useDispatch<RootDispatch>();
  const state = useSelector((state: RootState) => state.user_buyer_rfq) || {};
  const rfqState = state?.getRFQDetails || {
    data: null,
    loading: false,
    meta: null,
  };
  const [rfqItems, setRfqItems] = useState(rfqData);
  const [filteredData, setFilteredData] = useState(rfqData);
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    console.log("Fetching RFQ data...");
    dispatch(thunks.getRFQDetails());
  }, [dispatch]);

  useEffect(() => {
    console.log("Current RFQ State:", rfqState);
    if (rfqState?.data) {
      setRfqItems(rfqState.data);
      setFilteredData(rfqState.data);
    }
  }, [rfqState]);

  useEffect(() => {
    let newFilteredData;

    if (pathname.includes("/Open")) {
      newFilteredData = rfqItems.filter(
        (item) => item.status.toLowerCase() === "open"
      );
    } else if (pathname.includes("/Close")) {
      newFilteredData = rfqItems.filter(
        (item) => item.status.toLowerCase() === "close"
      );
    } else if (pathname.includes("/Pending")) {
      newFilteredData = rfqItems.filter(
        (item) => item.status.toLowerCase() === "pending"
      );
    } else {
      newFilteredData = rfqItems;
    }

    setFilteredData(newFilteredData);
    console.log("Filtered RFQ Data:", newFilteredData);
  }, [pathname, rfqItems]);

  const handleView = (id: any) => {
    router.push(`/dashboard/buyer/manage/detail/${id}`);
  };

  const handleEdit = (id: any) => {
    console.log("Edit RFQ:", id);
  };

  const handleDelete = (id: any) => {
    console.log("Delete RFQ:", id);
  };

  const handleStatusChange = (id: any, newStatus: string) => {
    const updatedItems = rfqItems.map((item) =>
      item.rfqId === id ? { ...item, status: newStatus } : item
    );

    setRfqItems(updatedItems);
    console.log(`Status of RFQ ${id} updated to ${newStatus}`);
  };

  const handleAddNew = () => {
    console.log("Add new RFQ");
  };

  return (
    <div className="py-6 px-4 overflow-x-hidden">
      <div className="rounded-lg overflow-hidden">
        <ListCardHeader
          data={headerData}
          className="py-3 font-medium text-gray-700"
        />
        <div className="flex flex-col gap-2 p-2">
          {filteredData.length > 0 ? (
            filteredData.map((rfq, index) => (
              <ListCard
                key={index}
                rfqId={rfq.rfqId}
                title={rfq.title}
                dateCreated={rfq.dateCreated}
                status={rfq.status}
                responses={rfq.responses}
                hideImage={true}
                onView={() => handleView(rfq.rfqId)}
                onEdit={() => handleEdit(rfq.rfqId)}
                onDelete={() => handleDelete(rfq.rfqId)}
                onStatusChange={(newStatus) =>
                  handleStatusChange(rfq.rfqId, newStatus)
                }
                className="px-2"
                classNames={{
                  rfqId: "!text-center !max-w-[10%] ",
                  title: "!text-center !max-w-[30%] ",
                  dateCreated: "!text-center !max-w-[18%]",
                  status: "!text-center !max-w-[12%] ",
                  responses: "!text-center !max-w-[10%] ",
                }}
                actionClasses={{
                  container: "flex flex-row items-center gap-2 justify-center",
                  view: "!max-w-[30%]  ",
                  edit: " !max-w-[30%] ",
                  delete: "!max-w-[30%] ",
                }}
              />
            ))
          ) : (
            <div className="py-8 text-center text-gray-500">
              No RFQ items found for this status.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RFQListPage;
