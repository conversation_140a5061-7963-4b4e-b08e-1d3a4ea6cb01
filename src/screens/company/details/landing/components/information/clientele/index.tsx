const Clientele = () => {
  const clients = [
    "Shopify",
    "Stripe",
    "HubSpot",
    "Salesforce",
    "Duolingo",
    "Coinbase",
    "Slack",
    "Notion",
    "Amazon",
    "Slack",
    "Notion",
    "Amazon",
  ];

  return (
    <>
      <div className="mx-auto bg-[#FAFAFA] w-full">
        <h2 className="text-base font-bold mb-8">Clientele</h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {clients.map((client, index) => (
            <div
              key={index}
              className="bg-white shadow-md text-center p-5  rounded-md"
            >
              <span className="text-lg">{client}</span>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default Clientele;
