import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from "../@api/update";
import { Actions } from "./slice";

export const getCategories = createAsyncThunk<
  any,
  { search?: string; page?: number; limit?: number } | undefined,
  { state: RootState }
>(
  "categories/getCategories",
  async (params = {}, { rejectWithValue, getState }) => {
    try {
      const { search = "", ...rest } = params;

      const response = await API.getCategories({ search, ...rest });
      const existingCategories =
        (getState() as RootState)?.user_company?.categories?.data || [];

      if (response?.data && Array.isArray(response?.data)) {
        const mergedMap = [...existingCategories, ...response.data].reduce(
          (map, item) => map.set(item.id, item),
          new Map()
        );

        response.data = Array.from(mergedMap.values());
      }

      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const getSubCategories = createAsyncThunk<
  any,
  | { category_id?: string; search?: string; page?: number; limit?: number }
  | undefined,
  { state: RootState }
>(
  "sub_categories/getSubSubCategories",
  async (params, { rejectWithValue, dispatch, getState }) => {
    try {
      if (params?.category_id) {
        dispatch(Actions.setSubCategories(null));
      }

      const response = await API.get_sub_Categories(params);

      const existingCategories =
        (getState() as RootState)?.user_company?.sub_categories?.data || [];

      if (response?.data && Array.isArray(response?.data)) {
        const mergedMap = [...existingCategories, ...response.data].reduce(
          (map, item) => map.set(item.id, item),
          new Map()
        );

        response.data = Array.from(mergedMap.values());
      }

      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const getSubSubCategories = createAsyncThunk<
  any,
  | { sub_category_id?: string; search?: string; page?: number; limit?: number }
  | undefined,
  { state: RootState }
>(
  "sub_sub_categories/getSubSubCategories",
  async (params, { rejectWithValue, dispatch, getState }) => {
    try {
      if (params?.sub_category_id) {
        dispatch(Actions.setSubSubCategories(null));
      }

      const response = await API.get_sub_sub_Categories(params);

      const existingCategories =
        (getState() as RootState)?.user_company?.sub_sub_categories?.data || [];

      if (response?.data && Array.isArray(response?.data)) {
        const mergedMap = [...existingCategories, ...response.data].reduce(
          (map, item) => map.set(item.id, item),
          new Map()
        );

        response.data = Array.from(mergedMap.values());
      }

      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const getDynamicFieldsByCategory = createAsyncThunk<
  any,
  string,
  { state: RootState }
>(
  "fieldsByCategory/getDynamicFieldsByCategory",
  async (category_id, { rejectWithValue }) => {
    try {
      const response = await API.getDynamicFieldsByCategory(category_id);
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const getCompanies = createAsyncThunk<
  any,
  { searchTerm?: string; page?: string; limit?: string } | undefined,
  { state: RootState }
>("companyList", async (pagination, { rejectWithValue }) => {
  try {
    const response: any = await API.getCompanies({
      searchTerm: pagination?.searchTerm,
      page: pagination?.page,
      limit: pagination?.limit,
    });

    window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const getCompany = createAsyncThunk<any, void, { state: RootState }>(
  "companyDetails/getCompany",
  async (_, { rejectWithValue }) => {
    try {
      const response = await API.getCompany();
      window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const updateCompanyDetails = createAsyncThunk<
  any,
  FormData,
  { state: RootState }
>(
  "companyDetails/updateCompanyDetails",
  async (formValue, { rejectWithValue, getState }) => {
    const companyDetails = (getState() as RootState)?.user_company
      ?.companyDetails;

    try {
      const response: any = await API.updateCompanyDetails(formValue);

      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });

      const successPayload = {
        ...response,
        data: companyDetails?.data,
      };

      return successPayload;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      const rejectPayload = {
        data: companyDetails?.data,
        meta: error?.response?.data || {
          message: error?.message || "Failed to fetch business details",
        },
      };

      const rejected = companyDetails?.data
        ? rejectPayload
        : rejectWithValue(error?.response?.data || error?.message);
      return rejected;
    }
  }
);

export const companyEmailVerifyInitiate = createAsyncThunk<
  any,
  { email: string },
  { state: RootState }
>(
  "emailVerificationInitiate/companyEmailVerifyInitiate",
  async (payload, { rejectWithValue }) => {
    try {
      const response: any = await API.companyEmailVerifyInitiate(payload);

      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const companyEmailVerify = createAsyncThunk<
  any,
  { email_otp: string },
  { state: RootState }
>("emailVerify/companyEmailVerify", async (payload, { rejectWithValue }) => {
  try {
    const response: any = await API.companyEmailVerify(payload);

    toast.success(response?.meta?.message as string, {
      classNames: {
        toast: "!bg-green-500 !text-white",
      },
    });

    return response;
  } catch (error: any) {
    toast.error(error?.meta?.message as string, {
      classNames: {
        toast: "!bg-red-500 !text-white",
      },
    });

    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const thunks = {
  getCategories,
  getSubCategories,
  getSubSubCategories,
  getCompanies,
  getCompany,
  updateCompanyDetails,
  companyEmailVerifyInitiate,
  companyEmailVerify,
};
