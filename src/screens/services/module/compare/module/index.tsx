"use client";

import React from "react";
import CompareTabs from "./components/tabs";
import CompareAddMore from "./components/add-more";
import ComparedList from "./components/list";
import { Button } from "@/components/ui/button";
import { useSelector } from "react-redux";
import { comparedList } from "@/screens/company/compare/@redux/selectors";
import { useRouter } from "next/navigation";

const Compare = () => {
const router = useRouter();
  const compared = useSelector(comparedList);

  return (
    <section
      className="w-screen h-screen bg-center bg-no-repeat bg-clip-padding bg-cover"
      style={{
        backgroundImage: `url(https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg)`,
      }}
    >
      <div className="w-screen h-screen bg-gradient-to-b from-[#3E0F08]/95 via-[#332522]/95 to-[#031C33]/95">
        <div className="container">
          <CompareTabs />
          <ComparedList />
          <CompareAddMore />

          {compared?.length > 1 && (
            <div className="flex items-center justify-center mt-2">
              <Button
                className="px-8 py-5 text-base"
                variant="main-revert"
                onClick={() => router.push("details")}
              >
                Compare
              </Button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Compare;
