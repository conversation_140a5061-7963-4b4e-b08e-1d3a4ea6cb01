import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import {
  languages,
  paymentMethods,
  regions,
} from "@/constants/user/seller/company/update";
import { companyDetails } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";

const Contact = () => {
  const dispatch = useDispatch();
  const { data: values } = useSelector(companyDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
  ) => {
    try {
      const payload: any = {};
      payload[e?.target?.name] = e?.target?.value;
      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          type="text"
          name="contact_person"
          label="Contact Person"
          onChange={handleChange}
          value={values?.contact_person}
          placeholder="Representative Full Name"
        />

        <Input
          type="text"
          name="designation"
          label="Designation"
          onChange={handleChange}
          value={values?.designation}
          placeholder="Representative job title"
        />

        <Input
          type="text"
          name="business_email"
          label="Email"
          onChange={handleChange}
          value={values?.business_email}
          placeholder="Business Email ID"
        />

        <Input
          type="text"
          name="phone_number"
          label="Phone"
          onChange={handleChange}
          value={values?.phone_number}
          placeholder="Business phone"
          countrySelector={{
            defaultValue: "+91",
            name: "phone_number_code",
            onChange: handleChange,
            value: values?.phone_number_code,
          }}
        />

        <Input
          type="text"
          name="whatsapp_number"
          label="Whatsapp Number"
          onChange={handleChange}
          value={values?.whatsapp_number}
          placeholder="Business Whatsapp"
          countrySelector={{
            defaultValue: "+91",
            name: "whatsapp_number_code",
            onChange: handleChange,
            value: values?.whatsapp_number_code,
          }}
        />

        <Input
          type="text"
          name="business_address"
          label="Business Address"
          onChange={handleChange}
          value={values?.business_address}
          placeholder="Full office Address"
        />

        <Input
          type="text"
          name="area_name"
          label="Area Name"
          onChange={handleChange}
          value={values?.area_name}
          placeholder="Business Locality"
        />

        <Input
          type="text"
          name="land_mark"
          label="Landmark"
          onChange={handleChange}
          value={values?.land_mark}
          placeholder="Business landmark (optional)"
        />

        <Input
          type="text"
          name="city"
          label="City"
          onChange={handleChange}
          value={values?.city}
          placeholder="Business location city"
        />

        <Input
          type="text"
          name="state"
          label="State"
          onChange={handleChange}
          value={values?.state}
          placeholder="Business location state"
        />

        <Input
          type="text"
          name="country"
          label="Country"
          onChange={handleChange}
          value={values?.country}
          placeholder="Business location country"
        />

        <Input
          type="text"
          name="pin_code"
          label="Pincode"
          onChange={handleChange}
          value={values?.pin_code}
          placeholder="Enter pincode"
        />
      </div>
      <div className="grid grid-cols-1 gap-6">
        <MultiSelect
          onValueChange={handleChange}
          defaultValue={values?.language}
          value={values?.language}
          name="language"
          label="Language Spoken"
          placeholder="Select Language"
          variant="inverted"
          maxCount={3}
          options={languages}
        />

        <Input
          type="text"
          name="web_url"
          label="Website"
          onChange={handleChange}
          value={values?.web_url}
          placeholder="Enter Website url"
        />

        <MultiSelect
          onValueChange={handleChange}
          defaultValue={values?.region_served}
          value={values?.region_served}
          name="region_served"
          label="Regions Served"
          placeholder="Select Language"
          variant="inverted"
          maxCount={3}
          options={regions}
        />
      </div>
    </>
  );
};

export default Contact;
