import { Button } from "@/components/ui/button";
import { Dropzone } from "@/components/ui/input/dropzone";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { ChangeEvent, useState } from "react";

const GetAQuote = () => {
  const [values, setValues] = useState<any>({});

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    try {
      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col items-start gap-5">
      <h3 className="font-bold mb-1">Get a Quote</h3>
      <div className="w-full flex-col sm:flex-row flex items-center gap-4 [&>*]:w-full [&>*]:flex-1">
        <Input
          type="text"
          name="name"
          label="Full Name"
          onChange={handleChange}
          value={values?.name}
          placeholder="Enter your full address"
          labelClassName="mb-1 ml-5"
          wrapperClassName="!rounded-full  "
        />
        <Input
          type="email"
          name="email"
          label="Email Address"
          onChange={handleChange}
          value={values?.email}
          labelClassName="mb-1 ml-5"
          placeholder="Enter your email address"
          wrapperClassName="!rounded-full  "
        />

        <Input
          type="text"
          name="phone"
          label="Phone"
          onChange={handleChange}
          value={values?.phone}
          labelClassName="mb-1 ml-5"
          placeholder="Enter your phone number"
          wrapperClassName="!rounded-full  "
          countrySelector={{
            defaultValue: "+91",
            onValueChange: (e) => console.log({ e }),
          }}
        />
      </div>
      <Textarea
        name="message"
        label="Inquiry Message"
        onChange={handleChange}
        value={values?.message}
        placeholder="Enter your Message"
        className="min-h-20"
        labelClassName="mb-1 ml-5"
        wrapperClassName="!rounded-lg"
        containerClassName="w-full"
      />

      <Dropzone
        onDrop={(files, event) => console.log("onDrop!", files, event)}
        onFileDelete={(file) => console.log({ file })}
      />

      <Button
        type="submit"
        variant="ghost-brown"
        className="w-full rounded-full"
      >
        Send Enquiry
      </Button>
    </div>
  );
};

export default GetAQuote;
