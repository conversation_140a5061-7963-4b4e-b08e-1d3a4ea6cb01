'use client';

import { useEffect } from 'react';
import { MultiSelect, type MultiSelectTarget } from "@/components/ui/input/multi-select";
import { businessDetails, sub_sub_categories } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/slice";
import { useDispatch, useSelector } from "react-redux";
import { getBusinessDetails } from "../../@redux/thunk";
import { RootDispatch } from '@/redux/store';

interface PreferredSettingsProps {
  onValidationChange?: (isValid: boolean) => void;
  isSubmitted?: boolean;
}

interface PreferredSettingsValues {
  payment_methods: string[];
  shipping_terms: string[];
  product_categories: string[];
}

interface DropdownOption {
  label: string;
  value: string;
}

const PreferredSettings = ({ onValidationChange }: PreferredSettingsProps) => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values = {} } = useSelector(businessDetails);
  const { data: subSubCategoryList = [] } = useSelector(sub_sub_categories);

  // Fetch business details when component mounts
  useEffect(() => {
    dispatch(getBusinessDetails())
      .unwrap()
      .then((response) => {
        if (response?.data) {
          const formData = {
            payment_methods: response.data.payment_methods || [],
            shipping_terms: response.data.shipping_terms || [],
            product_categories: normalizeProductCategories(response.data.product_categories || [])
          };
          dispatch(Actions.setFormValues(formData));
        }
      })
      .catch((error) => {
        console.error('Error fetching business details:', error);
      });
  }, [dispatch]);

  const normalizeProductCategories = (categories: any[]): string[] => {
    if (!Array.isArray(categories)) return [];

    return categories.map((cat: any) => {
      if (typeof cat === 'object' && cat?._id) {
        return cat._id;
      }

      if (typeof cat === 'string' && /^[a-f0-9]{24}$/i.test(cat)) {
        return cat;
      }

      if (typeof cat === 'string') {
        const foundCategory = subSubCategoryList.find((item: any) =>
          item.sub_sub_category_name === cat || item.name === cat
        );
        return foundCategory?._id || cat;
      }

      return cat;
    }).filter(Boolean);
  };

  const paymentOptions: DropdownOption[] = [
    { label: "Bank", value: "Bank" },
    { label: "Transfer", value: "Transfer" },
    { label: "PayPal", value: "PayPal" },
    { label: "Credit Card", value: "Credit Card" },
    { label: "Cryptocurrency", value: "Cryptocurrency" }
  ];

  const shippingOptions: DropdownOption[] = [
    { label: "FOB", value: "FOB" },
    { label: "CIF", value: "CIF" },
    { label: "DDU", value: "DDU" },
    { label: "DDP", value: "DDP" }
  ];

  const categoryOptions: DropdownOption[] = subSubCategoryList.map((item: any) => ({
    label: item.sub_sub_category_name || item.name,
    value: item._id,
  }));

  useEffect(() => {
    const isValid = Boolean(
      Array.isArray(values?.payment_methods) && values.payment_methods.length > 0 &&
      Array.isArray(values?.shipping_terms) && values.shipping_terms.length > 0 &&
      Array.isArray(values?.product_categories) && values.product_categories.length > 0
    );
    onValidationChange?.(isValid);
  }, [values, onValidationChange]);

  const handleValueChange = (field: keyof PreferredSettingsValues, event: MultiSelectTarget) => {
    let newValue = Array.isArray(event.target.value) ? event.target.value : [event.target.value];

    if (field === 'product_categories') {
      newValue = normalizeProductCategories(newValue);
    }

    dispatch(Actions.setFormValues({
      [field]: newValue
    }));
  };

  // Helper function to get display values - this converts IDs back to the format MultiSelect expects
  const getDisplayValue = (value: any, field: keyof PreferredSettingsValues) => {
    if (!value || !Array.isArray(value)) return [];

    if (field === 'product_categories') {
      return value.map(item => {
        if (typeof item === 'string' && /^[a-f0-9]{24}$/i.test(item)) {
          return item;
        }

        if (typeof item === 'string') {
          const foundCategory = subSubCategoryList.find((cat: any) =>
            cat.sub_sub_category_name === item || cat.name === item
          );
          return foundCategory?._id || item;
        }

        // If it's an object, return its _id
        if (typeof item === 'object' && item?._id) {
          return item._id;
        }

        return item;
      }).filter(Boolean);
    }

    return value.map(item => {
      if (typeof item === 'object' && item !== null) {
        return item.name || item.value || item._id || '';
      }
      return item;
    });
  };

  return (
    <div className="flex flex-col gap-6 mt-4">
      <MultiSelect
        onValueChange={(e) => handleValueChange('payment_methods', e)}
        name="payment_methods"
        label="Payment Methods"
        placeholder="Select Payment Methods"
        variant="inverted"
        maxCount={5}
        options={paymentOptions}
        defaultValue={getDisplayValue(values?.payment_methods, 'payment_methods')}
        value={getDisplayValue(values?.payment_methods, 'payment_methods')}
      />

      <MultiSelect
        onValueChange={(e) => handleValueChange('shipping_terms', e)}
        name="shipping_terms"
        label="Shipping Terms"
        placeholder="Select Shipping Terms"
        variant="inverted"
        maxCount={5}
        options={shippingOptions}
        defaultValue={getDisplayValue(values?.shipping_terms, 'shipping_terms')}
        value={getDisplayValue(values?.shipping_terms, 'shipping_terms')}
      />

      <MultiSelect
        onValueChange={(e) => handleValueChange('product_categories', e)}
        name="product_categories"
        label="Preferred Product Categories"
        placeholder="Select product categories"
        variant="inverted"
        maxCount={5}
        options={categoryOptions}
        defaultValue={getDisplayValue(values?.product_categories, 'product_categories')}
        value={getDisplayValue(values?.product_categories, 'product_categories')}
      />
    </div>
  );
};

export default PreferredSettings;