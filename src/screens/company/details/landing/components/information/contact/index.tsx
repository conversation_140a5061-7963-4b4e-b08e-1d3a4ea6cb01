import { <PERSON><PERSON> } from "@/components/ui/button";
import classNames from "classnames";
import { FaFacebookF, FaInstagram, FaLinkedinIn } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { IoIosGlobe } from "react-icons/io";
import { SlLocationPin } from "react-icons/sl";
import useOpenExternalUrl from "@/lib/hooks/useOpenExternalUrl";

const ContactInformation = () => {
  const openUrl = useOpenExternalUrl();

  return (
    <div className="flex flex-wrap sm:flex-nowrap items-start gap-5 [&>*]:w-full">
      <div className="flex flex-col items-start gap-6 [&>*]:w-full">
        <h3 className="font-bold">Get in Touch</h3>

        <div className="hidden md:flex flex-col items-start gap-3 [&>*]:w-full">
          {/* Email removed */}
          {/* <div className="flex items-center gap-3">
            <div className="rounded-full bg-white shadow flex items-center justify-center p-2">
              <MdOutlineMail size={20} />
            </div>
            <span className="font-normal text-base"><EMAIL></span>
          </div> */}

          <div className="flex items-center gap-3">
            <div className="rounded-full bg-white shadow flex items-center justify-center p-2">
              <IoIosGlobe size={20} />
            </div>
            <button
              onClick={() => openUrl("www.example.com")}
              className="font-normal text-base"
            >
              www.example.com
            </button>
          </div>
        </div>

        {/* Social icons removed */}
        {/* <SocialNav className="hidden sm:flex" /> */}

        <Button variant="ghost-brown" className="max-w-max hidden sm:block">
          Contact Us
        </Button>
      </div>

      <div className="h-full flex items-start flex-col gap-4 [&>*]:w-full">
        <div className="order-2 sm:order-1 flex-1 overflow-hidden">
          <iframe
            title="map"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d6139551.528322523!2d12.712159999999999!3d41.29085!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12d4fe82448dd203%3A0xe22cf55c24635e6f!2sItaly!5e0!3m2!1sen!2sin!4v1737648310290!5m2!1sen!2sin"
            loading="lazy"
            className="border-none h-full min-h-48 w-full"
            allowFullScreen={false}
            referrerPolicy="no-referrer-when-downgrade"
          ></iframe>
        </div>
        <div className="order-1 sm:order-2 flex items-start gap-3">
          <div className="rounded-full bg-white shadow flex items-center justify-center p-2">
            <SlLocationPin size={20} />
          </div>
          <span className="font-normal text-sm">
            1234 Elmwood Avenue, Suite 567, Downtown Business District,
            Springfield, IL 62704, United States
          </span>
        </div>
      </div>

      <SocialNav className="block sm:hidden justify-center mt-3" />
    </div>
  );
};

const SocialNav = ({ className }: { className?: string }) => {
  return (
    <div className={classNames("flex items-center gap-3", className)}>
      <button className="hover:shadow-lg rounded-full bg-[#F5F5F5] text-main shadow flex items-center justify-center p-2">
        <FaFacebookF size={20} />
      </button>
      <button className="hover:shadow-lg rounded-full bg-[#F5F5F5] text-main shadow flex items-center justify-center p-2">
        <FaLinkedinIn size={20} />
      </button>
      <button className="hover:shadow-lg rounded-full bg-[#F5F5F5] text-main shadow flex items-center justify-center p-2">
        <FaXTwitter size={20} />
      </button>
      <button className="hover:shadow-lg rounded-full bg-[#F5F5F5] text-main shadow flex items-center justify-center p-2">
        <FaInstagram size={20} />
      </button>
    </div>
  );
};

export default ContactInformation;
