"use client";

import classNames from "classnames";
import { Image } from "lucide-react";
import { ChangeEvent, FC, InputHTMLAttributes, ReactNode, useRef } from "react";
import { FileDrop, FileDropProps } from "react-file-drop";
import { MdOutlineFileUpload } from "react-icons/md";
import { InputLabel } from "../components/label";
import { FilePreview } from "../file-preview";

type DropzoneInputTargetType = {
  target: {
    files: FileList;
    name: string;
  };
};

const isDropzoneInputTargetType = (e: any): e is DropzoneInputTargetType => {
  return e && typeof e === "object" && Boolean(e?.target?.files);
};

interface DropzoneType extends Omit<FileDropProps, "children"> {
  children?: ReactNode;
  containerClassName?: string;
  labelClassName?: string;
  filePreviewClassName?: string;
  contentClassName?: string;
  name?: string;
  label?: string;
  formats?: string;
  className?: string;
  fileSrcKey?: string;
  preview?: boolean;
  inputProps?: InputHTMLAttributes<HTMLInputElement>;
  onChange?: (
    e: ChangeEvent<HTMLInputElement> | DropzoneInputTargetType
  ) => void;
  value?: File[];
  onFileDelete?: (file: File | any, name: string) => void;
}

const Dropzone: FC<DropzoneType> = ({
  className,
  children,
  contentClassName,
  containerClassName,
  labelClassName,
  filePreviewClassName,
  name = "Dropzone",
  label,
  formats,
  value,
  preview,
  onChange,
  onFileDelete,
  inputProps,
  fileSrcKey,
  ...rest
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const onFileInputChange = (
    event: ChangeEvent<HTMLInputElement> | FileList | null
  ) => {
    if (!event) return;

    if (onChange) {
      if (event instanceof FileList) {
        onChange({
          target: {
            files: event,
            name,
          },
        });
      } else {
        onChange(event);
      }
    }
  };

  const onTargetClick = () => {
    if (fileInputRef?.current) {
      fileInputRef?.current?.click();
    }
  };

  return (
    <>
      <input
        {...inputProps}
        onChange={onFileInputChange}
        name={name}
        ref={fileInputRef}
        type="file"
        className="hidden"
      />
      <div
        className={classNames(
          "dropzone w-full flex flex-col gap-1.5 items-center [&>*]:w-full bg-white hover:bg-slate-50 transition-colors",
          containerClassName
        )}
      >
        {label && (
          <InputLabel className={classNames("ml-5", labelClassName)}>
            {label}
          </InputLabel>
        )}
        <FileDrop
          onTargetClick={onTargetClick}
          className={classNames("w-full h-full", className)}
          onDrop={(files) => onFileInputChange(files)}
          {...rest}
        >
          <DropContentWrapper className={contentClassName}>
            {preview && value && (
              <FilePreview
                files={value}
                fileSrcKey={fileSrcKey}
                onDelete={(file) => onFileDelete?.(file, name)}
                className={filePreviewClassName}
              />
            )}
            {children ?? <DropPlaceholder formats={formats} />}
          </DropContentWrapper>
        </FileDrop>
      </div>
    </>
  );
};

const DropContentWrapper = ({
  children,
  className,
}: {
  children?: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={classNames(
        "drop-content-wrapper relative w-full h-full border border-dashed border-stone-500 rounded-lg px-2.5 py-5 cursor-pointer",
        className
      )}
    >
      {children}
    </div>
  );
};

const DropPlaceholder = ({ formats }: { formats?: string }) => {
  return (
    <div className="w-full h-full flex items-center justify-center cursor-pointer">
      <div className="flex flex-col items-center justify-center gap-1.5">
        <MdOutlineFileUpload size={25} />
        <h5 className="text-sm font-semibold">
          Drag And Drop Or{" "}
          <span role="button" className="text-main">
            Choose File
          </span>
        </h5>
        <span className="text-slate-500 text-xs">
          {formats ?? "JPEG, PNG, PDF"}
        </span>
      </div>
    </div>
  );
};

const InlinePlaceholder = ({ className }: { className?: string }) => {
  return (
    <div
      className={classNames(
        "w-full h-full flex items-center gap-1.5 justify-center cursor-pointer text-xs",
        className
      )}
    >
      <div className="flex items-center gap-1 text-main">
        <Image className="w-4 h-4" />
        <span>Upload file</span>
      </div>
      <span>or drag and drop</span>
    </div>
  );
};

export { Dropzone, InlinePlaceholder, isDropzoneInputTargetType };
export type { DropzoneInputTargetType };
