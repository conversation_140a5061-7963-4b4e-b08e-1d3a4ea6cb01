import image1 from "/assets/pages/portfolio/p_01.png";
import image2 from "/assets/pages/portfolio/p_02.png";
import image3 from "/assets/pages/portfolio/p_03.png";
import image4 from "/assets/pages/portfolio/p_04.png";
import image5 from "/assets/pages/portfolio/p_05.png";
import image6 from "/assets/pages/portfolio/p_06.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { useState } from "react";

const ImagesData = [image1, image2, image3, image4, image5, image6];

const ServiceGallery = () => {
  const images = ImagesData;
  const [currentSlide, setCurrentSlide] = useState(0);

  return (
    <div className="w-full max-w-full max-h-[460px] relative">
      <Carousel
        className="w-full"
        setApi={(api) => {
          api?.on("select", () => {
            setCurrentSlide(api.selectedScrollSnap());
          });
        }}
      >
        <CarouselContent>
          {images.map((image, index) => (
            <CarouselItem key={index}>
              <div className="flex items-center justify-center h-[460px]">
                <img
                  src={image}
                  alt={`Product ${index + 1}`}
                  className="object-cover h-full w-full"
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        
        {/* Override default positions to match the image */}
        <CarouselPrevious 
          className="left-2 top-1/2 -translate-y-1/2 bg-white hover:bg-white hover:shadow-md opacity-80 hover:opacity-100" 
        />
        <CarouselNext 
          className="right-2 top-1/2 -translate-y-1/2 bg-white hover:bg-white hover:shadow-md opacity-80 hover:opacity-100" 
        />
      </Carousel>
    </div>
  );
};

export default ServiceGallery;