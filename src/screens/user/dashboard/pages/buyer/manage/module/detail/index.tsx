'use client';

import { LockedR<PERSON> } from "@/components/elements/card/list-card";
import ReviewsStars from "@/components/elements/icon/reviews-stars";
import { Button } from "@/components/ui/button";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { MdOutlineVerified } from "react-icons/md";
import { TbXboxXFilled } from "react-icons/tb";
import { useParams } from "next/navigation";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootDispatch } from "@/redux/store";
import { getRFQDetail } from "../../@redux/thunk";
import { getRFQDetailSelector } from "../../@redux/selector";

// Columns data for product type
const productColumnData = {
  proposal: [
    {
      key: "category",
      label: "Category",
    },
    {
      key: "quantity",
      label: "Quantity / Volume Needed",
    },
    {
      key: "budget",
      label: "Budget Range",
    },
    {
      key: "delivery",
      label: "Delivery Location",
    },
    {
      key: "urgency",
      label: "Requirement Urgency",
    },
    {
      key: "supplier_type",
      label: "Preferred Supplier Type",
    },
    {
      key: "payment_terms",
      label: "Preferred Payment Terms",
    },
    {
      key: "product_requirement",
      label: "Specific Product Requirement",
    },
    {
      key: "product_specification",
      label: "Product specification",
    },
  ],
  contact: [
    {
      key: "contact_name",
      label: "Contact Name",
    },
    {
      key: "email",
      label: "Email Address",
    },
    {
      key: "company_name",
      label: "Company Name",
    },
    {
      key: "contact_number",
      label: "Contact Number",
    },
    {
      key: "additional_notes",
      label: "Additional Notes",
    },
  ],
};
const serviceColumnData = {
  proposal: [
    {
      key: "category",
      label: "Category",
    },
    {
      key: "budget",
      label: "Budget Range",
    },
    {
      key: "location",
      label: "Preferred Countries",
    },
    {
      key: "languages",
      label: "Language Preferences",
    },
    {
      key: "team_size",
      label: "Team Size",
    },
    {
      key: "skills",
      label: "Required Skills",
    },
    {
      key: "duration",
      label: "Time Duration",
    },
    {
      key: "service_description",
      label: "Service Description",
    },
  ],
  contact: [
    {
      key: "contact_name",
      label: "Contact Name",
    },
    {
      key: "email",
      label: "Email Address",
    },
    {
      key: "company_name",
      label: "Company Name",
    },
    {
      key: "contact_number",
      label: "Contact Number",
    },
    {
      key: "additional_notes",
      label: "Additional Notes",
    },
  ],
};

const checkList = [
  {
    label: "Identify Verified",
    success: true,
  },
  {
    label: "Payment Verified",
    success: false,
  },
  {
    label: "Email Verified",
    success: true,
  },
  {
    label: "Profile Verified",
    success: true,
  },
  {
    label: "Phone Verified",
    success: true,
  },
];

const OpportunityDetails = () => {
  const params = useParams();
  const id = params?.id as string;
  const dispatch = useDispatch<RootDispatch>();
  const rfqDetailData = useSelector(getRFQDetailSelector);

  useEffect(() => {
    if (id) {
      dispatch(getRFQDetail(id));
    }
  }, [dispatch, id]);
  const companyDetail = ListingData[0];
  const opportunityData = rfqDetailData?.data?.opportunitie;
  const opportunityType = opportunityData?.type || "product";
  const columnData = opportunityType === "service" ? serviceColumnData : productColumnData;
  let data = {};

  if (opportunityType === "service") {
    data = {
      category: opportunityData?.category || "-",
      budget: opportunityData?.serviceDetails?.budgetRange ?
        `${opportunityData.serviceDetails.budgetRange.min || 0}-${opportunityData.serviceDetails.budgetRange.max || 0} ${opportunityData.serviceDetails.budgetRange.currency || "USD"}` : "-",
      location: opportunityData?.serviceDetails?.preferredCountries?.join(", ") || "-",
      languages: opportunityData?.serviceDetails?.languagePreferences?.join(", ") || "-",
      team_size: opportunityData?.serviceDetails?.teamSize || "-",
      skills: opportunityData?.serviceDetails?.requiredSkills?.join(", ") || "-",
      duration: opportunityData?.serviceDetails?.timeDuration ? `${opportunityData.serviceDetails.timeDuration} days` : "-",
      service_description: opportunityData?.serviceDetails?.serviceDescription || "-",
    };
  } else {
    data = {
      category: opportunityData?.category || "",
      quantity: opportunityData?.productDetails?.quantity ? `${opportunityData.productDetails.quantity} ${opportunityData.productDetails.volume || ""}` : "-",
      budget: opportunityData?.productDetails?.budgetRange ?
        `${opportunityData.productDetails.budgetRange.min || 0}-${opportunityData.productDetails.budgetRange.max || 0} ${opportunityData.productDetails.budgetRange.currency || "USD"}` : "-",
      delivery: opportunityData?.productDetails?.targetCountries?.join(", ") || "-",
      urgency: opportunityData?.productDetails?.requirementUrgency || "-",
      supplier_type: opportunityData?.productDetails?.preferredProviderType || "-",
      payment_terms: opportunityData?.productDetails?.paymentTerms || "-",
      product_requirement: opportunityData?.productDetails?.detailedDescription || "-",
      product_specification: opportunityData?.productDetails?.additionalRequirements || "-",
    };
  }
  const contactInfo = {
    contact_name: opportunityData?.contactInfo?.name || "-",
    email: opportunityData?.contactInfo?.email || "-",
    company_name: opportunityData?.contactInfo?.companyName || "-",
    contact_number: opportunityData?.contactInfo?.phone || "-",
    additional_notes: opportunityData?.contactInfo?.notes || "-",
  };

  return (
    <div className="w-full h-full">
      <div className="flex flex-col md:flex-row items-start justify-between gap-4 my-6 md:my-5">
        <div className="flex items-center gap-3 flex-wrap">
          <h3 className="text-lg font-medium break-words">
            {opportunityData?.title || "Website Design and Development"}
          </h3>
          <span
            className={classNames({
              "text-green-400":
                opportunityData?.status?.toLowerCase() === "open" ||
                opportunityData?.status?.toLowerCase() === "active",
              "text-red-400":
                opportunityData?.status?.toLowerCase() === "rejected" ||
                opportunityData?.status?.toLowerCase() === "closed",
              "text-yellow-400":
                opportunityData?.status?.toLowerCase() === "pending",
              "text-gray-400": !opportunityData?.status,
            })}
          >
            ({opportunityData?.status || "open"})
          </span>
        </div>

        <div className="flex flex-col items-start gap-0.5">
          <span className="text-xs">Total Response</span>
          <span className="text-base font-bold">
            {opportunityData?.proposalCount || 0}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[1fr_minmax(250px,auto)] gap-4">
        {/* Left Box */}
        <div className="order-2 md:order-1 bg-white shadow-box p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-2">
            {opportunityType === "service"
              ? "Service Request Details"
              : "Product Request Details"}
          </h3>

          <div className="w-full overflow-x-auto">
            <table className="w-full table-auto text-sm break-words [&_td]:align-top [&_td]:py-1.5 [&_td]:break-words">
              <tbody>
                {columnData?.proposal?.map((col, index) => {
                  const current = (data as any)?.[col?.key];
                  return (
                    <tr key={index}>
                      <td className="whitespace-nowrap pr-2">{col?.label}</td>
                      <td className="px-2">:</td>
                      <td className="break-words">
                        {col.key === "service_description" ? (
                          <div
                            dangerouslySetInnerHTML={{ __html: current }}
                          />
                        ) : (
                          current
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          <div className="w-full mt-6">
            <h3 className="text-lg font-semibold mb-2">Contact Information</h3>

            <div className="w-full overflow-x-auto">
              <table className="w-full table-auto text-sm break-words [&_td]:align-top [&_td]:py-1.5 [&_td]:break-words">
                <tbody>
                  {columnData?.contact?.map((cont, index) => {
                    return (
                      <tr key={index}>
                        <td className="whitespace-nowrap pr-2">{cont?.label}</td>
                        <td className="px-2">:</td>
                        <td className="break-words">
                          <LockedRender />
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          <div className="flex items-center justify-start md:justify-end flex-wrap gap-2 mt-7 [&>*]:rounded-md">
            <Button variant="main-revert">Submit Proposal</Button>
            <Button variant="destructive">Decline</Button>
          </div>
        </div>

        {/* Right Box */}
        <div className="order-1 md:order-2 bg-white shadow-box p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-6">About Client</h3>

          <div className="flex flex-col items-start gap-3.5 [&>*]:w-full">
            <h4 className="font-semibold break-words">
              {opportunityData?.contactInfo?.companyName || "Company Name"}
            </h4>

            <div className="flex items-center gap-1.5 flex-wrap break-words">
              <img
                src={companyDetail?.location?.flag}
                alt="flag"
                className="w-5 h-auto object-contain"
              />
              <div className="flex flex-wrap gap-1 text-xs text-stone-700 break-words">
                <span>
                  {opportunityData?.geoLocation?.coordinates?.[0] ||
                    companyDetail?.location?.address}
                  ,
                </span>
                <span>
                  {opportunityData?.geoLocation?.coordinates?.[1] ||
                    companyDetail?.location?.label}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <ReviewsStars rating={4} />
              <span className="font-light text-base text-stone-700">4/5</span>
            </div>

            {(opportunityData?.isVerified || companyDetail?.verified) && (
              <div className="flex items-center gap-2 mb-2">
                <div className="hidden sm:flex bg-[#9F9795] text-white px-2 py-1 items-center gap-1.5 rounded-sm">
                  <img
                    src="/assets/pages/verified-white.svg"
                    alt="verified"
                    className="w-3 h-auto object-contain"
                  />
                  <span className="text-[0.6rem] font-medium">Verified</span>
                </div>
              </div>
            )}

            <div className="flex flex-col gap-3 [&>*]:w-full">
              {checkList?.map((ch, index) => (
                <div
                  key={index}
                  className="flex items-start justify-between gap-2.5 [&>svg]:w-5 [&>svg]:h-5"
                >
                  <span className="text-sm font-light break-words">
                    {ch?.label}
                  </span>
                  {ch?.success ? (
                    <MdOutlineVerified className="fill-green-500" />
                  ) : (
                    <TbXboxXFilled className="fill-red-500" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>

  );
};

export default OpportunityDetails;