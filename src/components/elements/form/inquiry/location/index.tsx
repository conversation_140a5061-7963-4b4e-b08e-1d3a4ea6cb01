"use client";

import { HighlightedText } from "@/components/elements/text/highlight";
import { useState } from "react";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { locations } from "@/constants/location";

const DeliveryDeadline = () => {
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);

  // Convert locations array to format expected by MultiSelect
  const locationOptions = locations.map((location) => ({
    label: location.label,
    value: location.code,
    // You can add an icon if available in your location data
    // icon: location.icon ? location.icon : undefined
  }));

  // Handle value change in MultiSelect
  const handleLocationChange = (target: MultiSelectTarget) => {
    setSelectedLocations(target.target.value);
  };

  return (
    <div className="min-w-[40vw] min-h-[30vh] h-full flex-1 p-10 flex items-start justify-center">
      <div className="w-full flex flex-col items-center justify-center gap-4">
        <h5 className="text-2xl font-bold">
          <HighlightedText
            title=" Where should it be delivered?"
            highlight={["delivered?"]}
          />
        </h5>

        <div className="w-full">
          <MultiSelect
            options={locationOptions}
            placeholder="e.g. Tirana, Albania"
            onValueChange={handleLocationChange}
            defaultValue={selectedLocations}
            className="rounded-full"
            containerClassName="w-full"
            variant="default"
            maxCount={3}
            animation={0.3}
          />
        </div>
      </div>
    </div>
  );
};

export default DeliveryDeadline;
