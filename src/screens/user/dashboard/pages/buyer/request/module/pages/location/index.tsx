'use client';

import { MultiSelect } from "@/components/ui/input/multi-select";
import React, { useState } from 'react';

interface LocationProps {
  onLocationChange?: (details: LocationDetails) => void;
  initialDetails?: LocationDetails;
}

interface LocationDetails {
  countries: string[];
}

const Location: React.FC<LocationProps> = ({
  onLocationChange,
  initialDetails = {
    countries: ["india", "america"]
  }
}) => {
  const [locationDetails, setLocationDetails] = useState<LocationDetails>(initialDetails);
  
  const countryOptions = [
    { label: "India", value: "india" },
    { label: "New Zealand", value: "new_zealand" },
    { label: "America", value: "america" },
    { label: "Austria", value: "austria" },
    { label: "Canada", value: "canada" },
    { label: "Australia", value: "australia" }
  ];

  const handleCountryChange = (event: any) => {
    const selectedCountries = event.target.value;
    const updatedDetails = {
      ...locationDetails,
      countries: selectedCountries
    };
    setLocationDetails(updatedDetails);
    if (onLocationChange) {
      onLocationChange(updatedDetails);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">Where should it be delivered?</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <MultiSelect
            name="countries"
            options={countryOptions}
            placeholder="Select countries"
            onValueChange={handleCountryChange}
            defaultValue={locationDetails.countries}
          />
        </div>
      </div>
    </div>
  );
};

export default Location;