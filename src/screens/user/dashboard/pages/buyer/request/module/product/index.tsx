"use client";

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Actions } from '@/screens/user/dashboard/pages/buyer/request/@redux/slice';
import { productDetails } from '@/screens/user/dashboard/pages/buyer/request/@redux/selector';
import Quantity from '../pages/quantity';
import Requirement from '../pages/requirement';
import ProductDetails from '../pages/details';
import Budget from '../pages/budget';
import Provider from '../pages/provider';
import Urgency from '../pages/urgency';
import Attachment from '../pages/attachment';
import OTP from '../pages/otp';
import ServiceLocation from '../pages/location';
import AdditionalInfo from '../pages/additional';

type StepType = 'selection' | 'category' | 'quantity' | 'location' | 'urgency' | 'provider' | 'budget' | 'requirement' | 'attachment' | 'additional' | 'otp' | 'skills' | 'language' | 'team' | 'duration';

interface ProductProps {
  currentStep: StepType;
  onCategoryChange: (category: string) => void;
  categorySelected: string;
  onValidationChange?: (isValid: boolean) => void;
}

const Product: React.FC<ProductProps> = ({
  currentStep,
  onCategoryChange,
  categorySelected,
  onValidationChange
}) => {
  const dispatch = useDispatch();
  const productDetailsState = useSelector(productDetails);
  const formData = productDetailsState?.formData || {};
  const productType = formData.type || {};

  const [isCurrentStepValid, setIsCurrentStepValid] = useState(false);

  useEffect(() => {
    if (!productType.name || productType.name !== "product") {
      dispatch(Actions.updateFormData({
        field: 'type',
        data: {
          name: "product",
          timestamp: new Date().toISOString(),
          isProduct: true
        }
      }));
    }
  }, [currentStep, dispatch, formData, productType.name]);

  useEffect(() => {
    validateCurrentStep();
  }, [currentStep, formData]);

  useEffect(() => {
    if (onValidationChange) {
      onValidationChange(isCurrentStepValid);
    }
  }, [isCurrentStepValid, onValidationChange]);

  const validateCurrentStep = () => {
    let isValid = false;

    switch (currentStep) {
      case 'category':
        isValid = !!formData.product?.title;
        break;
      case 'quantity':
        isValid = !!formData.quantity?.amount && formData.quantity.amount > 0;
        break;
      case 'location':
        isValid = !!formData.location?.countries && formData.location.countries.length > 0;
        break;
      case 'urgency':
        isValid = !!formData.urgency?.urgency;
        break;
      case 'provider':
        isValid = !!formData.provider?.providerTypes && formData.provider.providerTypes.length > 0;
        break;
      case 'budget':
        isValid = !!formData.budget?.budgetRange?.min && !!formData.budget?.budgetRange?.max;
        break;
      case 'requirement':
        isValid = !!formData.requirement?.text;
        break;
      case 'additional':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const phoneRegex = /^\d{10}$/;

        isValid = !!formData.additional?.contactName &&
          !!formData.additional?.email &&
          emailRegex.test(formData.additional.email) &&
          !!formData.additional?.phoneNumber &&
          phoneRegex.test(formData.additional.phoneNumber);
        break;
      default:
        isValid = true;
    }

    setIsCurrentStepValid(isValid);
  };

  const handleProductDetailsChange = (details: any) => {
    dispatch(Actions.updateFormData({ field: 'product', data: details }));

    dispatch(Actions.updateFormData({
      field: 'title',
      data: details.title || ""
    }));
  };

  const handleQuantityChange = (details: any) => {
    dispatch(Actions.updateFormData({ field: 'quantity', data: details }));
  };

  const handleRequirementChange = (details: any) => {
    dispatch(Actions.updateFormData({ field: 'requirement', data: details }));
  };

  const handleLocationChange = (details: any) => {
    dispatch(Actions.updateFormData({ field: 'location', data: details }));
  };

  const handleUrgencyChange = (details: any) => {
    dispatch(Actions.updateFormData({ field: 'urgency', data: details }));
  };

  const handleProviderChange = (details: any) => {
    dispatch(Actions.updateFormData({ field: 'provider', data: details }));
  };

  const handleBudgetChange = (details: any) => {
    dispatch(Actions.updateFormData({ field: 'budget', data: details }));
  };

  const handleAdditionalInfoChange = (details: any) => {
    dispatch(Actions.updateFormData({ field: 'additional', data: details }));
  };

  const renderComponent = () => {
    switch (currentStep) {
      case 'category':
        return (
          <ProductDetails
            onProductDetailsChange={handleProductDetailsChange}
            initialDetails={formData.product || {
              title: ""
            }}
          />
        );
      case 'quantity':
        return (
          <Quantity
            onQuantityChange={handleQuantityChange}
            initialDetails={formData.quantity || {
              amount: 0,
              unit: "kg"
            }}
          />
        );
      case 'location':
        return (
          <ServiceLocation
            onLocationChange={handleLocationChange}
            initialDetails={formData.location || {
              countries: ["india", "america"]
            }}
          />
        );
      case 'urgency':
        return (
          <Urgency
            onUrgencyChange={handleUrgencyChange}
            initialDetails={formData.urgency || {
              urgency: ""
            }}
          />
        );
      case 'provider':
        return (
          <Provider
            onProviderChange={handleProviderChange}
            initialDetails={formData.provider || {
              providerTypes: ["manufacturer", "any"]
            }}
          />
        );
      case 'budget':
        return (
          <Budget
            onBudgetChange={handleBudgetChange}
            initialDetails={formData.budget || {
              budgetRange: {
                min: 0,
                max: 0,
                currency: "€"
              }
            }}
          />
        );
      case 'requirement':
        return (
          <Requirement
            onRequirementChange={handleRequirementChange}
            initialDetails={formData.requirement || {
              text: ""
            }}
          />
        );
      case 'attachment':
        return <Attachment />;
      case 'additional':
        return (
          <AdditionalInfo
            onAdditionalInfoChange={handleAdditionalInfoChange}
            initialDetails={formData.additional || {
              contactName: "",
              email: "",
              phoneCode: "+91",
              phoneNumber: "",
              companyName: "",
              notes: ""
            }}
          />
        );
      case 'otp':
        return <OTP />;
      default:
        return null;
    }
  };

  return (
    <div className="mb-8">
      {renderComponent()}
    </div>
  );
};

export default Product;