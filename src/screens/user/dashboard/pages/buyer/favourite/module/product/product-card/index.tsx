import RATING_STAR from "/assets/pages/rating-star.svg";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { FaRegHeart } from "react-icons/fa";
import { useRouter } from "next/navigation";

const ProductCard = () => {
const router = useRouter();

  return (
    <div
      role="button"
      onClick={() => router.push("/products/details/123")}
      className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden w-full"
    >
      {/* Product Image */}
      <div className="relative w-full aspect-square">
        <img
          src="https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg"
          alt="Apple HomePod"
          className="w-full h-full object-cover"
        />
        {/* Heart Button */}
        <button className="absolute right-4 top-4 bg-white hover:bg-main hover:text-white transition-all h-8 w-8 rounded-full flex items-center justify-center">
          <FaRegHeart size={16} className="mt-0.5" />
        </button>
      </div>

      {/* Product Info */}
      <div className="p-4 flex flex-col gap-2">
        {/* Product Title */}
        <h3 className="text-sm font-bold text-gray-900 line-clamp-2">
          Apple HomePod, 2nd Generation, Smart Speaker, Midnight
        </h3>

        {/* Ratings */}
        <div className="flex items-center gap-2 mt-1">
          <img
            src={RATING_STAR}
            alt="rating star"
            className="aspect-auto w-4 h-4 object-contain"
          />
          <div className="flex items-center flex-wrap gap-2 text-xs font-semibold text-gray-700">
            <span className="border-r border-slate-300 pr-2">4.5/5</span>
            <span>150 Reviews</span>
          </div>
        </div>

        {/* Price */}
        <div className="flex items-center gap-2 mt-1">
          <span className="text-lg font-bold text-gray-900">$381.99</span>
          <span className="text-sm text-gray-400 line-through">$371.99</span>
        </div>

        {/* Seller Info */}
        <div className="flex items-center gap-2 mt-1">
          <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-xs">P&G</span>
          </div>
          <span className="text-xs font-medium">Procter & Gamble</span>
        </div>

        {/* Location - Using SVG image */}
        <div className="flex items-center gap-1.5 mt-1">
          <img
            src="https://upload.wikimedia.org/wikipedia/en/thumb/4/41/Flag_of_India.svg/1200px-Flag_of_India.svg.png"
            alt="flag"
            className="aspect-auto w-4 h-auto object-contain"
          />
          <span className="text-xs text-gray-600">India Mumbai</span>
        </div>

        {/* Minimum Order */}
        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs text-gray-700">Min Order:</span>
          <span className="text-xs text-gray-600">500 Pieces</span>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 mt-3">
          <Button
            variant="outline"
            className="bg-white text-orange-500 border-orange-500 hover:bg-orange-50 flex-1 h-8 rounded-full text-xs px-2"
          >
            Get Quote
          </Button>

          <div className="flex items-center gap-1 border border-gray-300 px-2 flex-1 h-8 rounded-full">
            <Checkbox id="compare" className="border-gray-400 h-3 w-3" />
            <label
              htmlFor="compare"
              className="text-xs text-gray-700 whitespace-nowrap"
            >
              Add to Compare
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
