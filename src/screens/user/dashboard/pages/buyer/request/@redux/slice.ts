import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createThunkCase, APIResponseType } from "@/redux/helper/thunk";
import { AsyncThunk } from "@reduxjs/toolkit";
import { thunks } from "./thunk";

interface RFQState {
  productDetails: APIResponseType & {
    formData?: any;
  };
  serviceDetails: APIResponseType & {
    formData?: any;
  };
}

// Initial state setup for product and service RFQ details including form fields
const initialState: RFQState = {
  productDetails: {
    data: null,
    loading: false,
    meta: null,
    formData: {
      title: "",
      product: {},
      quantity: {},
      location: {},
      urgency: {},
      provider: {},
      budget: {},
      requirement: {},
      additional: {},
      attachment: {}
    }
  },
  serviceDetails: {
    data: null,
    loading: false,
    meta: null,
    formData: {
      title: "",
      serviceDetails: {},
      serviceLocation: {},
      language: {},
      team: {},
      skills: {},
      duration: {},
      serviceBudget: {},
      serviceRequirement: {},
      serviceAdditional: {}
    }
  },
};

export const slice = createSlice({
  name: "user_buyer_rfq",
  initialState,
  reducers: {
    // Reset productDetails state to initial values
    resetProductDetails: (state) => {
      state.productDetails = initialState.productDetails;
    },
    // Update a specific field in productDetails.formData
    updateFormData: (state, action: PayloadAction<{field: string; data: any}>) => {
      const { field, data } = action.payload;
      state.productDetails.formData = {
        ...state.productDetails.formData,
        [field]: data
      };
    },
    // Reset serviceDetails state to initial values
    resetServiceDetails: (state) => {
      state.serviceDetails = initialState.serviceDetails;
    },
    // Update a specific field in serviceDetails.formData
    updateServiceFormData: (state, action: PayloadAction<{field: string; data: any}>) => {
      const { field, data } = action.payload;
      state.serviceDetails.formData = {
        ...state.serviceDetails.formData,
        [field]: data
      };
    }
  },
  extraReducers: (builder) => {
    // Attach async thunk lifecycle handlers for RFQ-related thunks
    Object.entries(thunks).forEach(([_, thunk]) => {
      createThunkCase(builder, thunk as AsyncThunk<any, any, any>, {
        productDetails: initialState.productDetails,
        serviceDetails: initialState.serviceDetails
      });
    });
  },
});

export const Actions = slice.actions;
export default slice.reducer;
