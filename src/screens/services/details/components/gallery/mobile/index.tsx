import {
  Carousel,
  CarouselBullet,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import classNames from "classnames";

const ImagesData = [
  "/assets/pages/portfolio/p_01.png",
  "/assets/pages/portfolio/p_02.png",
  "/assets/pages/portfolio/p_03.png",
  "/assets/pages/portfolio/p_04.png",
  "/assets/pages/portfolio/p_05.png",
  "/assets/pages/portfolio/p_06.png"
];

const ServiceGalleryMobile = () => {
  const images = ImagesData;

  return (
    <div className="slider-container flex flex-row gap-3.5 max-h-[460px]">
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full max-w-full"
      >
        <CarouselContent>
          {images.map((img, index) => {
            return (
              <CarouselItem key={index} className="max-h-[460px]">
                <div
                  key={index}
                  className={classNames(
                    "aspect-square w-full h-full transition-all"
                  )}
                >
                  <img
                    src={img}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselBullet
          containerClassName="mt-4"
          activeClassName="!bg-black !w-3.5"
        />
      </Carousel>
    </div>
  );
};

export default ServiceGalleryMobile;
