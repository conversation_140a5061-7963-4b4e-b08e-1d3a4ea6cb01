import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";

const data = [
  {
    title: "Store rating",
    value: (
      <div className="flex items-center gap-3">
        <img
          src={RATING_STAR}
          alt="rating star"
          className="aspect-auto w-3.5 md:w-6 h-3.5 md:h-6 object-contain"
        />

        <span>4.5/5</span>
      </div>
    ),
  },
  {
    title: "On-time delivery rate",
    value: "99.0%",
  },
  {
    title: "Online revenue",
    value: "$120,000+",
  },
  {
    title: "Floorspace",
    value: "400m²",
  },
  {
    title: "Employees",
    value: "800-1100 Employees",
  },
  {
    title: "Experience",
    value: "20 years in business",
  },
  {
    title: "Min Order",
    value: "900 Pieces",
  },
];

const ProductSupplierDetails = () => {
const router = useRouter();
  return (
    <div className="relative w-full">
      <h3 className="font-bold mb-4">Know your supplier</h3>

      <div className="bg-gradient-to-l from-[#FFF5F6] via-[#FDFDFD]  to-[#FFFAF4] p-5 rounded-md">
        {/* Profile card */}
        <div className="flex items-center gap-4">
          <img
            src="https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg"
            alt="supplier"
            className="aspect-square w-12 h-12 object-cover rounded-md"
          />
          <div className="flex flex-col gap-1.5">
            <button
              className="hover:underline text-sm"
              onClick={() => router.push("/company/details")}
            >
              Lorem Ipsum Lorem Ipsum Lorem Ipsum
            </button>

            {/* Verified */}
            <div className="flex items-center gap-2 mb-2">
              <div className="flex items-center gap-2 flex-wrap">
                <div className="bg-[#9F9795] text-white px-2 py-1 flex items-center justify-start gap-1.5 w-max rounded-tl-sm rounded-bl-sm rounded-tr-md rounded-br-md">
                  <img
                    src={"/assets/pages/verified-white.svg"}
                    alt="verified"
                    className="aspect-auto max-w-3 w-3 h-auto object-contain"
                  />
                  <span className="text-[0.6rem] font-medium">Verified</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bullets */}
        {Array?.isArray(data) && (
          <div className="table sm:grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-5 gap-y-8 mt-8">
            {data?.map((bullet, index) => (
              <div
                key={index}
                className="relative group flex-1 table-row sm:flex flex-row sm:flex-col flex-start gap-1.5"
              >
                <span className="table-cell p-2 sm:p-0 sm:block text-sm font-normal">
                  {bullet?.title}
                </span>
                <span className="table-cell p-2 sm:p-0 sm:block text-sm font-semibold whitespace-nowrap overflow-hidden text-ellipsis">
                  {bullet?.value}
                </span>
              </div>
            ))}
          </div>
        )}

        <div className="flex flex-col gap-4 my-7 py-7 border-y border-stone-200">
          <h5 className="font-bold text-sm">Services</h5>
          <div className="flex flex-wrap gap-2">
            {[...data, ...data]?.map((_, index) => (
              <Badge
                key={index}
                variant="outline"
                className="!rounded-full text-stone-600"
              >
                {"Innovation"}
              </Badge>
            ))}
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <h5 className="font-bold text-sm">Order with confident</h5>
          <div className="flex flex-wrap gap-3">
            <Button variant="outline" className="!rounded-full">
              View Product
            </Button>
            <Button
              variant="main"
              className="!rounded-full"
              onClick={() => router.push("/company/details")}
            >
              View Company
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductSupplierDetails;
