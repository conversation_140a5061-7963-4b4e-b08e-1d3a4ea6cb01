"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  Dropzone,
  DropzoneInputTargetType,
  InlinePlaceholder,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { TextEditor } from "@/components/ui/input/texteditor";
import { InputTargetType } from "@/components/ui/input/types";
import {
  deleteMediaHandler,
  normalizeToStrings,
  removeEmptyValues,
  removeKeysFromObject,
} from "@/lib/utils/helper";
import { RootDispatch } from "@/redux/store";
import { portfolioDetails } from "@/screens/user/dashboard/pages/seller/listing/module/portfolio/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/portfolio/@redux/slice";
import {
  addUpdatePortfolio,
  fetchPortfolio,
} from "@/screens/user/dashboard/pages/seller/listing/module/portfolio/@redux/thunk";
import classNames from "classnames";
import { AlertCircle } from "lucide-react";
import { useParams, usePathname, useRouter } from "next/navigation";
import { ChangeEvent, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const AddPortfolio = () => {
  const dispatch = useDispatch<RootDispatch>();
  const router = useRouter();
  const { data: values, meta, loading } = useSelector(portfolioDetails);
  const pathname = usePathname();
  const params = useParams();
  const portfolio_id = params?.id;

  useEffect(() => {
    if (portfolio_id) {
      dispatch(fetchPortfolio(portfolio_id as string));
    } else {
      dispatch(Actions.setPortfolioDetails(null));
    }

    return () => {
      dispatch(Actions.setPortfolioDetails(null));
    };
  }, [dispatch, portfolio_id, pathname]);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values?.[e?.target?.name])
          ? Array.from(values?.[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setPortfolioDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const handleCancel = () => {
    try {
      dispatch(Actions.setPortfolioDetails(null));
      router.back();
    } catch (error) {
      console.error(error);
    }
  };

  const handleSave = () => {
    try {
      if (values) {
        let payload = {
          ...values,
          sub_sub_category: normalizeToStrings(
            values?.sub_sub_category,
            "string"
          ),
        };

        payload = removeEmptyValues(
          removeKeysFromObject(payload, [
            "_id",
            "business_id",
            "created_by",
            "createdAt",
            "updatedAt",
          ])
        );

        const data = new FormData();
        Object.entries(payload).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (
              Array.isArray(value) &&
              value.some((fl) => fl instanceof File)
            ) {
              value.forEach((file: any) => {
                if (file instanceof File) {
                  data.append(key, file);
                }
              });
            } else if (value instanceof File || value instanceof Blob) {
              data.append(key, value);
            } else if (
              Array.isArray(value) &&
              value.some((fl) => typeof fl === "string")
            ) {
              value.forEach((str) => {
                data.append(key, str);
              });
            } else {
              data.append(key, String(value));
            }
          }
        });

        dispatch(
          addUpdatePortfolio({ portfolio_id: portfolio_id as string, data })
        )
          .unwrap()
          .then((res) => {
            if (res?.type === "success") {
              router.push("/dashboard/seller/listing/portfolio");
            }
          })
          .catch((error) => {
            console.error(error);
          });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleDeleteMedia = async (file: any, name: string) => {
    const updatedFiles = await deleteMediaHandler(values, file, name);
    dispatch(Actions.setPortfolioDetails({ [name]: updatedFiles }));
  };

  console.log({ values });

  if (portfolio_id && !loading && !values) {
    return (
      <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
        <AlertCircle /> {meta?.message}
      </div>
    );
  }

  return (
    <main className="bg-white rounded-lg md:shadow-lg md:p-4 w-full h-full">
      <div className="flex flex-col gap-9 [&>*]:w-full">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex flex-col gap-6 [&>*]:w-full">
            <h6 className="border-b border-stone-200 text-start px-1 py-3.5">
              Edit Work
            </h6>

            <Input
              type="text"
              name="work_title"
              label="Work Title"
              onChange={handleChange}
              value={values?.work_title}
              placeholder="Work title"
            />

            <div className="flex-1 flex flex-col gap-3 [&>*]:w-full">
              <InputLabel>Images</InputLabel>
              <Dropzone
                containerClassName={classNames(
                  "flex-1",
                  values?.portfolio_images?.length > 0 &&
                    "[&_.drop-content-wrapper]!:h-max"
                )}
                onChange={(e) => handleChange(e)}
                onFileDelete={(file, name) => handleDeleteMedia(file, name)}
                name="portfolio_images"
                value={values?.portfolio_images}
                preview
                inputProps={{
                  multiple: true,
                }}
              >
                {Array.isArray(values?.portfolio_images) &&
                values?.portfolio_images?.length > 0 ? (
                  <InlinePlaceholder className="mt-6" />
                ) : null}
              </Dropzone>
            </div>
          </div>
          <div className="flex flex-col gap-6 [&>*]:w-full">
            <h6 className="border-b border-stone-200 text-start px-1 py-3.5">
              Client Information
            </h6>

            <Input
              type="text"
              name="client_name"
              label="Client Name"
              onChange={handleChange}
              value={values?.client_name}
              placeholder="Client Name"
            />

            <Input
              type="text"
              name="location"
              label="location"
              onChange={handleChange}
              value={values?.location}
              placeholder="Location"
            />

            <Select
              name="sector"
              label="Sector"
              onChange={handleChange}
              value={values?.sector}
              placeholder="Sector"
              options={[{ label: "sector 1", value: "sector 1" }]}
            />

            <Select
              name="audience"
              label="Audience"
              onChange={handleChange}
              value={values?.audience}
              placeholder="Audience"
              options={[{ label: "audience 1", value: "audience 1" }]}
            />
          </div>
        </div>
        <MultiSelect
          onValueChange={handleChange}
          defaultValue={values?.experties}
          name="experties"
          label="Expertise"
          placeholder="Select Expertise"
          variant="inverted"
          maxCount={3}
          options={[]}
          addNew
        />

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>About Client</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Brief introduction, history, and company mission/vision
            </span>
          </div>
          <TextEditor
            name="about_client"
            value={values?.about_client}
            onChange={handleChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            label="Client Overview (Detailed)"
            maxLength={500}
            showCharCount
          />
        </div>

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Challenge</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Brief introduction, history, and company mission/vision
            </span>
          </div>
          <TextEditor
            name="challenge"
            value={values?.challenge}
            onChange={handleChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            label="Challenge (Detailed)"
            maxLength={500}
            showCharCount
          />
        </div>

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Solution</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Brief introduction, history, and company mission/vision
            </span>
          </div>
          <TextEditor
            name="solution"
            value={values?.solution}
            onChange={handleChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            label="Solution (Detailed)"
            maxLength={500}
            showCharCount
          />
        </div>

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Services or Products used</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Brief introduction, history, and company mission/vision
            </span>
          </div>
          <TextEditor
            name="services_products_used"
            value={values?.services_products_used}
            onChange={handleChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            label="Used Product or Services (Detailed)"
            maxLength={500}
            showCharCount
          />
        </div>

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Key Achievements</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Brief introduction, history, and company mission/vision
            </span>
          </div>
          <TextEditor
            name="key_achievements"
            value={values?.key_achievements}
            onChange={handleChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            label="Add Key (Detailed)"
            maxLength={500}
            showCharCount
          />
        </div>

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Metrics & Data</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Brief introduction, history, and company mission/vision
            </span>
          </div>
          <TextEditor
            name="metrics_data"
            value={values?.metrics_data}
            onChange={handleChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            label="Metrics Data (Detailed)"
            maxLength={500}
            showCharCount
          />
        </div>
      </div>

      <div className="flex items-center justify-end gap-3 mt-7 md:mt-10">
        <Button
          className="min-w-28 rounded-md"
          variant="destructive"
          onClick={() => handleCancel()}
        >
          Cancel
        </Button>
        <Button
          className="min-w-28 rounded-md"
          variant="main-revert"
          onClick={() => handleSave()}
        >
          Save
        </Button>
      </div>
    </main>
  );
};

export default AddPortfolio;
