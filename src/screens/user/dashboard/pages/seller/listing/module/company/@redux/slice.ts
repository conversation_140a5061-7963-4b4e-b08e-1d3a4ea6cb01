import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import { thunks } from "./thunk";

interface APTStateType {
  categories: APIResponseType;
  sub_categories: APIResponseType;
  sub_sub_categories: APIResponseType;
  fieldsByCategory: APIResponseType;
  companyList: APIResponseType;
  companyDetails: APIResponseType;
  emailVerificationInitiate: APIResponseType;
  emailVerify: APIResponseType;
}

const APIState: APTStateType = {
  categories: {
    data: null,
    loading: true,
    meta: null,
  },
  sub_categories: {
    data: null,
    loading: true,
    meta: null,
  },
  sub_sub_categories: {
    data: null,
    loading: true,
    meta: null,
  },
  fieldsByCategory: {
    data: null,
    loading: true,
    meta: null,
  },
  companyList: {
    data: null,
    loading: true,
    meta: null,
  },
  companyDetails: {
    data: null,
    loading: true,
    meta: null,
  },
  emailVerificationInitiate: {
    data: null,
    loading: false,
    meta: null,
  },
  emailVerify: {
    data: null,
    loading: false,
    meta: null,
  },
};

interface stateType extends APTStateType {
  currentFormStep: number;
}

const initialState = {
  currentFormStep: 0,
  ...APIState,
} satisfies stateType as stateType;

export const slice = createSlice({
  name: "user_company",
  initialState,
  reducers: {
    setCurrentFormStep: (state, action: PayloadAction<number>) => {
      state.currentFormStep = action?.payload;
    },
    setCategories: (state, action: PayloadAction<APIResponseType | null>) => {
      if (!action?.payload) {
        state.categories = initialState.categories;
      } else {
        state.categories = action.payload;
      }
    },
    setSubCategories: (
      state,
      action: PayloadAction<APIResponseType | null>
    ) => {
      if (!action?.payload) {
        state.sub_categories = initialState.sub_categories;
      } else {
        state.sub_categories = action.payload;
      }
    },
    setSubSubCategories: (
      state,
      action: PayloadAction<APIResponseType | null>
    ) => {
      if (!action?.payload) {
        state.sub_sub_categories = initialState.sub_sub_categories;
      } else {
        state.sub_sub_categories = action.payload;
      }
    },
    setFormValues: (
      state,
      action: PayloadAction<Record<string, any> | null>
    ) => {
      if (action?.payload) {
        state.companyDetails.data = {
          ...state.companyDetails.data,
          ...action?.payload,
        };
      } else {
        state.companyDetails = { ...initialState.companyDetails };
      }
    },
  },
  extraReducers: (builder) => {
    Object.entries(thunks).forEach(([_, thunk]) => {
      createThunkCase(builder, thunk, APIState);
    });
  },
});

export const Actions = slice.actions;
export default slice.reducer;
