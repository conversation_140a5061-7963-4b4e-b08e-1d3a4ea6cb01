"use client";

import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import { Textarea } from "@/components/ui/input/textarea";
import { TextEditor } from "@/components/ui/input/texteditor";
import { InputTargetType } from "@/components/ui/input/types";
import { productDetails } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";

const Specifications = () => {
  const dispatch = useDispatch();
  const { data: values } = useSelector(productDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
  ) => {
    try {
      const payload: any = {};
      payload[e?.target?.name] = e?.target?.value;
      dispatch(Actions.setProductDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Short Description</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Brief Product Summary
          </span>
        </div>
        <Textarea
          name="short_description"
          label="Short Product Summary"
          placeholder="Type here"
          value={values?.short_description ?? ""}
          className="min-h-20"
          onChange={handleChange}
          containerClassName="flex-1"
          maxLength={150}
          showCharCount
        />
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Full Description</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Products features benefits, and usage instruction
          </span>
        </div>
        <TextEditor
          name="full_description"
          value={values?.full_description ?? ""}
          onChange={handleChange}
          containerClassName="flex-1"
          className="flex-1 rounded-md"
          label="Detailed description of product features & benefits"
          maxLength={500}
          showCharCount
        />
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Warranty</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Sort details about your company
          </span>
        </div>
        <TextEditor
          name="warranty_description"
          value={values?.warranty_description ?? ""}
          onChange={handleChange}
          containerClassName="flex-1"
          className="flex-1 rounded-md"
          label="Brief product warranty"
          maxLength={500}
          showCharCount
        />
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>After Sales Services</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Brief introduction, after-sale service
          </span>
        </div>
        <TextEditor
          name="after_sale_services_description"
          value={values?.after_sale_services_description ?? ""}
          onChange={handleChange}
          containerClassName="flex-1"
          className="flex-1 rounded-md"
          label="Support and after-sales services"
          maxLength={500}
          showCharCount
        />
      </div>
    </>
  );
};

export default Specifications;
