"use client";

import { Minus, Plus } from "lucide-react";
import { ChangeEvent, FC, HTMLAttributes, ReactNode, useState } from "react";

interface QuantityInputType
  extends Omit<HTMLAttributes<HTMLInputElement>, "onChange"> {
  placeholder?: string;
  value?: string;
  onChange?: (count: string) => void;
}

const QuantityInput: FC<QuantityInputType> = ({
  placeholder = "~100",
  onChange,
  value,
  ...rest
}) => {
  const [countValue, setCountValue] = useState<string>("");

  const handleCallback = (count: string) => {
    try {
      if (onChange) {
        onChange(count);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      const inputValue = e?.target?.value;

      if (isNaN(Number(inputValue))) {
        return;
      }

      handleCallback(inputValue);
      setCountValue(inputValue);
    } catch (error) {
      console.error(error);
    }
  };

  const handleIncrease = () => {
    try {
      setCountValue((prev) => {
        let current = Number(prev);
        if (current > -1) {
          current++;
        }

        const finalValue = current?.toString();

        handleCallback(finalValue);
        return finalValue;
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleDecrease = () => {
    try {
      setCountValue((prev) => {
        let current = Number(prev);
        if (current > 1) {
          current--;
        }

        const finalValue = current?.toString();
        handleCallback(finalValue);
        return finalValue;
      });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="relative w-full flex items-center max-w-32 bg-white rounded-full border-slate-100 shadow overflow-hidden">
      <ButtonRender id="decrement-button" onClick={handleDecrease}>
        <Minus size={20} />
      </ButtonRender>
      <input
        type="text"
        id="quantity-input"
        data-input-counter
        className="bg-white border-0 outline-0 px-1 text-center w-full"
        placeholder={placeholder}
        onChange={handleChange}
        value={countValue}
        {...rest}
      />
      <ButtonRender id="increment-button" onClick={handleIncrease}>
        <Plus size={20} />
      </ButtonRender>
    </div>
  );
};

interface ButtonRenderType extends HTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
}
const ButtonRender = ({ children, ...rest }: ButtonRenderType) => (
  <button
    type="button"
    data-input-counter-increment="quantity-input"
    className="hover:bg-main hover:text-white py-1 px-2 h-full"
    {...rest}
  >
    {children}
  </button>
);

export default QuantityInput;
