import MAIN_LOGO from "/assets/branding/logo.svg";
import { But<PERSON> } from "@/components/ui/button";
import { Actions } from "@/screens/elements/@redux/slice";
import { useDispatch } from "react-redux";
import { FC } from "react";
import classNames from "classnames";

interface InquiryCategoryType {
  className?: string;
  onProductClick?: () => void;
  onServiceClick?: () => void;
}

const InquiryCategory: FC<InquiryCategoryType> = ({
  onProductClick,
  onServiceClick,
  className
}) => {
  const dispatch = useDispatch();
  
  const handleClick = () => {
    dispatch(Actions.setCurrentFormStep(2));
  };
  
  return (
    <div className={classNames("w-full h-full flex items-center justify-center pb-20", className)}>
      <div className="w-full max-w-xl flex flex-col items-center justify-center">
        <h1 className="text-3xl font-bold text-center mb-8">What Are You Looking For?</h1>
        
        <div className="flex items-center justify-center gap-6 w-full">
          <Button
            onClick={onProductClick}
            className="text-lg py-2 px-16 rounded-full border border-red-500 text-red-500 hover:bg-red-50 bg-transparent w-40"
            variant="outline"
          >
            Products
          </Button>
          
          <Button
            onClick={onServiceClick}
            className="text-lg py-2 px-16 rounded-full border border-gray-800 text-gray-800 hover:bg-gray-50 bg-transparent w-40"
            variant="outline"
          >
            Services
          </Button>
        </div>
      </div>
    </div>
  );
};

export default InquiryCategory;