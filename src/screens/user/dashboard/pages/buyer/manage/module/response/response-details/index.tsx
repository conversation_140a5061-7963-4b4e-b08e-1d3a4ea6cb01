'use client';

import { Locked<PERSON><PERSON> } from "@/components/elements/card/list-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { MdOutlineVerified } from "react-icons/md";
import { TbXboxXFilled } from "react-icons/tb";
import { useParams } from "next/navigation";
import ReviewsStars from "@/components/elements/icon/reviews-stars";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootDispatch } from "@/redux/store";
import { getRFQDetail, handleProposalAction } from "../../../@redux/thunk";
import { getRFQDetailSelector } from "../../../@redux/selector";

const columnData = {
  proposal: [
    {
      key: "unitPrice",
      label: "Unit Price",
    },
    {
      key: "offerPrice",
      label: "Offer Price",
    },
    {
      key: "proposalType",
      label: "Proposal Type",
    },
    {
      key: "deliveryTime",
      label: "Delivery Time / Lead Time",
    },
    {
      key: "moq",
      label: "MOQ (Minimum Order Quantity)",
    }
  ],
  contact: [
    {
      key: "contact_name",
      label: "Contact Name",
    },
    {
      key: "email",
      label: "Email Address",
    },
    {
      key: "company_name",
      label: "Company Name",
    },
    {
      key: "contact_number",
      label: "Contact Number",
    },
    {
      key: "additional_notes",
      label: "Additional Notes",
    }
  ]
};

const checkList = [
  {
    label: "800-1100 Employees",
    success: true,
  },
  {
    label: "20 Years in business",
    success: true,
  },
  {
    label: "Min. order value : (300 USD)",
    success: true,
  },
  {
    label: "50% Response rate",
    success: true,
  },
  {
    label: "200 pieces MOQ",
    success: true,
  }
];

// Image attachments for mobile, iPad, and laptop
const attachmentImages = [
  {
    name: "Smartphone Design",
    url: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg", // Use placeholder in production, replace with actual URL if needed
    type: "image"
  },
  {
    name: "iPad Design",
    url: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg", // Use placeholder in production, replace with actual URL if needed
    type: "image"
  },
  {
    name: "Laptop Design",
    url: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg", // Use placeholder in production, replace with actual URL if needed
    type: "image"
  },
  {
    name: "Responsive Layout",
    url: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg", // Use placeholder in production, replace with actual URL if needed
    type: "image"
  }
];

const Details = () => {
  const params = useParams();
  const id = params?.id as string;
  const dispatch = useDispatch<RootDispatch>();
  const rfqDetailData = useSelector(getRFQDetailSelector);

  useEffect(() => {
    if (id) {
      dispatch(getRFQDetail(id));
    }
  }, [dispatch, id]);

  const companyDetail = ListingData[0];
  const opportunityData = rfqDetailData?.data?.opportunitie || {};
  const proposalData = rfqDetailData?.data?.proposals?.[0] || {};

  // Show loading state
  if (rfqDetailData?.loading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  const proposalInfo = {
    unitPrice: proposalData?.pricingDetails?.unitPrice ? `$${proposalData.pricingDetails.unitPrice}` : "-",
    offerPrice: proposalData?.pricingDetails?.discountOffered ? `$${proposalData.pricingDetails.discountOffered}` : "-",
    proposalType: proposalData?.pricingDetails?.paymentTerms || "-",
    deliveryTime: proposalData?.deliveryDetails?.leadTime || "-",
    moq: proposalData?.offerDetails?.quantity ? `${proposalData.offerDetails.quantity} ${proposalData.offerDetails.unit}` : "-",
  };

  const handleAction = async (action: 'accept' | 'reject') => {
    if (!id || !proposalData?._id) return;

    try {
      await dispatch(handleProposalAction({
        action,
        proposalId: proposalData._id,
        opportunityId: id
      })).unwrap();
    } catch (error) {
      console.error(`Failed to ${action} proposal:`, error);
    }
  };

  return (
    <div className="w-full h-full">
      <div className="flex flex-col md:flex-row items-start justify-between gap-4 my-6 md:my-5">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-medium">
            {proposalData?.title || "-"}
          </h3>
          <span className={classNames(
            {
              "text-green-400": proposalData?.status?.toLowerCase() === "submitted" || proposalData?.status?.toLowerCase() === "accepted",
              "text-red-400": proposalData?.status?.toLowerCase() === "rejected",
              "text-yellow-400": proposalData?.status?.toLowerCase() === "pending",
            }
          )}>
            ({proposalData?.status || "-"})
          </span>
        </div>

        <div className="flex flex-col items-start gap-0.5">
          <span className="text-xs">Total Response</span>
          <span className="text-base font-bold">{opportunityData?.proposalCount || "-"}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[1fr_minmax(100px,auto)] gap-4">
        <div className="order-2 md:order-1 bg-white shadow-box p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-2">View Response</h3>

          <div className="mb-6">
            <p className="text-sm mb-4">
              {proposalData?.communication?.publicNotes || "-"}
            </p>
          </div>

          {/* 🆕 Horizontal scroll wrapper */}
          <div className="w-full overflow-x-auto">
            <table className="min-w-[600px] table-fixed text-sm [&_td]:align-top [&_td]:py-1.5">
              <tbody>
                {columnData.proposal.map((col, index) => (
                  <tr key={index}>
                    <td className="w-min text-nowrap">{col.label}</td>
                    <td className="px-6">:</td>
                    <td>{proposalInfo[col.key as keyof typeof proposalInfo]}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="w-full mt-6">
            <h3 className="text-lg font-semibold mb-4">Attachment</h3>
            <div className="flex flex-wrap gap-3">
              {attachmentImages.map((item, index) => (
                <div key={index} className="w-32 h-28 bg-gray-100 rounded-md overflow-hidden">
                  {item.type === "image" ? (
                    <div className="relative group">
                      <img
                        src={item.url}
                        alt={item.name}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/0 bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-end justify-center">
                        <div className="text-white text-xs p-1 translate-y-full group-hover:translate-y-0 transition-transform">
                          {item.name}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-full flex flex-col items-center justify-center bg-gray-50">
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#F56565" strokeWidth="2">
                        <path d="M14 3v4a1 1 0 001 1h4M17 21H7a2 2 0 01-2-2V5a2 2 0 012-2h7l5 5v11a2 2 0 01-2 2z" />
                        <path d="M12 11v6M9 14h6" />
                      </svg>
                      <span className="text-xs mt-1 text-center text-gray-600">{item.name}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-start md:justify-end flex-wrap gap-2 mt-7 [&>*]:rounded-md">
            <Button variant="outline" className="border-gray-300 text-gray-700">Make Offer</Button>
            <Button
              variant="main-revert"
              onClick={() => handleAction('accept')}
              disabled={proposalData?.status === 'accepted' || proposalData?.status === 'rejected'}
            >
              Accept
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleAction('reject')}
              disabled={proposalData?.status === 'rejected' || proposalData?.status === 'accepted'}
            >
              Reject
            </Button>
          </div>
        </div>

        <div className="order-1 md:order-2  bg-white shadow-box p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-6">{proposalData?.seller?.business_name || "-"}</h3>

          <div className="flex flex-col items-start gap-3.5 [&>*]:w-full">
            <div className="flex items-center gap-1.5">
              <img
                src={proposalData?.seller?.location?.flag || "https://upload.wikimedia.org/wikipedia/en/thumb/4/41/Flag_of_India.svg/1200px-Flag_of_India.svg.png"}
                alt="flag"
                className="flex-1 aspect-auto max-w-5 w-5 h-auto object-contain"
              />
              <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 text-wrap">
                <span>{proposalData?.seller?.business_type?.join(", ") || "-"}</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <ReviewsStars rating={proposalData?.seller?.rating || 0} />
              <span className="font-light text-base text-stone-700">{proposalData?.seller?.rating || "-"}/5</span>
              <span className="text-gray-600 text-xs">{proposalData?.seller?.reviewCount || "-"} Reviews</span>
            </div>

            {proposalData?.seller?.isVerified && (
              <div className="flex items-center gap-2 mb-2">
                <div className="flex bg-[#9F9795] text-white px-2 py-1 items-center justify-start gap-1.5 w-max max-w-max rounded-sm">
                  <img
                    src="/assets/pages/verified-white.svg"
                    alt="verified"
                    className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
                  />
                  <span className="flex-1 text-[0.6rem] font-medium">
                    Verified
                  </span>
                </div>
                <div className="text-gray-600 text-xs">Sponsored</div>
              </div>
            )}

            <a href={`mailto:${proposalData?.communication?.contactPerson?.email}`} className="text-blue-600 text-sm">
              {proposalData?.communication?.contactPerson?.email || "-"}
            </a>

            <div className="text-sm">{proposalData?.communication?.contactPerson?.phone || "-"}</div>

            <div className="flex items-start flex-col gap-3 [&>*]:w-full">
              {checkList.map((item, index) => (
                <div key={index} className="flex items-start justify-between gap-2.5 [&>svg]:w-5 [&>svg]:h-5">
                  <span className="text-sm font-light">{item.label}</span>
                  {item.success ? (
                    <MdOutlineVerified className="fill-green-500" />
                  ) : (
                    <TbXboxXFilled className="fill-red-500" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Details;