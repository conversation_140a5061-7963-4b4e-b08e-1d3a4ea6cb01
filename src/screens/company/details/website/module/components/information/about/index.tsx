import EditModal from "@/components/elements/form/edit-modal";
import { Skeleton } from "@/components/ui/loader/skeleton";
import classNames from "classnames";
import { Fragment, lazy, Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";
import useAuth from "@/screens/user/auth/@context/useAuth";

const AboutData = [
  {
    path: "company",
    component: lazy(
      () =>
        import("@/screens/company/details/landing/components/information/about")
    ),
    editable: true,
  },
  {
    path: "company",
    component: lazy(
      () =>
        import(
          "@/screens/company/details/landing/components/information/photos"
        )
    ),
    editable: true,
  },
  {
    path: "sets-apart",
    component: lazy(
      () =>
        import(
          "@/screens/company/details/website/module/components/information/about/sets-apart"
        )
    ),
    editable: true,
  },
  {
    path: "clientele",
    component: lazy(
      () =>
        import(
          "@/screens/company/details/landing/components/information/clientele"
        )
    ),
    editable: true,
  },
  {
    path: "features",
    component: lazy(
      () =>
        import(
          "@/screens/company/details/landing/components/information/features"
        )
    ),
    editable: true,
  },
];

const CompanyWebsiteAbout = ({ user }: { user: any }) => {
  console.log({ user });

  return (
    <div className="relative container my-12">
      <div className="grid grid-cols-1 gap-8">
        {AboutData?.map((section, index) => {
          return (
            <Fragment key={index}>
              <ErrorBoundary fallback={<div>Failed to load section</div>}>
                <Suspense fallback={<Skeleton className="w-full h-44" />}>
                  <section
                    id={section.path}
                    className={classNames(
                      "relative w-full sm:scroll-mt-44 sm:shadow-box rounded-md px-6 md:px-8 py-7",
                      {
                        "bg-white": ["company"]?.includes(section?.path),
                        "bg-[#FAFAFA]": [
                          "sets-apart",
                          "features",
                          "clientele",
                        ]?.includes(section?.path),
                      }
                    )}
                  >
                    {section?.editable && (
                      <EditModal className="absolute top-4 right-4" hasAccess>
                        Hey
                      </EditModal>
                    )}
                    <section.component />
                  </section>
                </Suspense>
              </ErrorBoundary>
            </Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default CompanyWebsiteAbout;
