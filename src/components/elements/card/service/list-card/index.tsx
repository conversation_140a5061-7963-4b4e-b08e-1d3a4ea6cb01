import { Checkbox } from "@/components/ui/input/checkbox";
import classNames from "classnames";
import { useRouter } from "next/navigation";
// import ContactNowModal from "../../inquiry";
import { ReactNode } from "react";
import ImageGallery from "./images";

const ServiceListCard = ({
  service,
  inquiryModal,
}: {
  service: any;
  inquiryModal?: ReactNode;
}) => {
  const router = useRouter();

  return (
    <div className="flex-1 w-full h-max bg-white shadow-md hover:shadow-lg">
      <div className="flex flex-col gap-0">
        <div className="flex flex-col sm:flex-row gap-6 px-6 pt-6 pb-4">
          {/* Image Slider */}
          <div className="w-full sm:w-max flex items-start justify-between gap-4">
            <ImageGallery images={service?.images} />
            <CheckboxMapping className="flex sm:hidden" />
          </div>

          {/* title */}
          <div className="flex-1 flex flex-col items-start gap-1 [&>*]:w-full">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center justify-start gap-1.5">
                <h2
                  role="button"
                  className="text-lg font-bold hover:underline text-black hover:drop-shadow"
                  onClick={() => router.push("/service")}
                >
                  {service?.name}
                </h2>
                <div className="flex items-center gap-2 sm:hidden ">
                  <span className="flex-1 text-xs text-stone-600">
                    Sponsored
                  </span>
                </div>
              </div>
              <CheckboxMapping className="hidden sm:flex" />
            </div>

            {/* Reviews */}
            <div className="flex items-center gap-2.5 mb-2">
              <img
                src={"/assets/pages/rating-star.svg"}
                alt="rating star"
                className="aspect-auto w-3 md:w-5 h-3 md:h-5 object-contain"
              />
              <div className="flex item flex-wrap gap-2.5 [&>*]:text-sm [&>*]:font-semibold [&>*]:text-font [&>*]:leading-4">
                <span className="border-r border-slate-300 pr-2.5">
                  {service?.rating?.stars}/5
                </span>
                <span>{service?.rating?.reviewsCount} Reviews</span>
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center gap-3 pr-4 mb-2">
              <span className="font-bold text-[#070707] text-lg">
                ${service?.offerPrice}
              </span>
              {/* Sale Price with strike-through */}
              <span className="font-bold text-[#A2A2A2] text-base line-through">
                ${service?.actualPrice}
              </span>
            </div>

            {/* Description */}
            <div className="mt-1">
              <p
                title={service?.description}
                className="text-sm text-stone-700 line-clamp-2"
              >
                {service?.description}
              </p>
            </div>

            {/* Tags */}
            <div
              className="hidden md:flex items-center flex-wrap gap-2 max-w-full md:max-w-[75%] mt-3 mb-2 overflow-hidden"
              style={{
                WebkitBoxOrient: "vertical",
                WebkitLineClamp: 2,
                maxHeight: "calc(2 * (1.3rem + 0.5rem))",
              }}
            >
              {Array.isArray(service?.tags) &&
                service?.tags?.length > 0 &&
                service?.tags?.slice(0, 10)?.map((tag: any, index: number) => {
                  return (
                    <button
                      key={index}
                      className="flex items-center justify-center px-3 py-0.5 bg-stone-50 hover:bg-stone-100 border border-stone-300 rounded-full text-stone-800 text-xs"
                    >
                      {tag?.title}
                    </button>
                  );
                })}
            </div>
          </div>
        </div>

        {/* Card Footer */}
        <div className="flex flex-col sm:flex-row sm:flex-wrap items-center gap-4 justify-between border-t-0 sm:border-t border-slate-200 px-6 pt-2.5 pb-6 sm:pb-2.5">
          <div className="w-full md:w-max flex items-center justify-center gap-4 [&>*:not(:last-child)]:border-r [&>*:not(:last-child)]:border-slate-300 [&>*:not(:last-child)]:pr-4 [&>*]:text-xs [&>*]:font-light [&>*]:leading-4 bg-stone-50 sm:bg-transparent px-3 sm:px-0 py-4 sm:py-1">
            <p className="flex-1 text-nowrap [&>*]:text-xs flex flex-col sm:flex-row items-center gap-2">
              24/7 Availability
            </p>
            <p className="flex-1 text-nowrap [&>*]:text-xs flex flex-col sm:flex-row items-center gap-2">
              On-Site Service
            </p>
            <p className="flex-1 text-nowrap [&>*]:text-xs flex flex-col sm:flex-row items-center gap-2">
              Available: Mon-Fri, 9 AM - 6 PM
            </p>
          </div>
          {inquiryModal && (
            <div className="w-full md:w-max flex items-center justify-between sm:justify-center gap-3 [&>*]:flex-1 [&>*]:py-5 [&>*]:font-bold sm:[&>*]:font-semibold md:[&>*]:py-4">
              {inquiryModal}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const CheckboxMapping = ({ className }: { className?: string }) => {
  return (
    <Checkbox
      label="Add to Compare"
      labelClassName="whitespace-nowrap"
      containerClassName={classNames("order-1 sm:order-2", className)}
    />
  );
};

export default ServiceListCard;
