"use client";

import { logoutAction } from "@/app/(user)/(auth)/actions";
import { Button } from "@/components/ui/button";
import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { DropdownListDataType } from "@/components/ui/dropdown/list/types";
import useAuth from "@/screens/user/auth/@context/useAuth";
import { apiRoutes } from "@/services/api/routes";
import classNames from "classnames";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { FC, ReactNode } from "react";

export const authMenu = [
  { label: "Sign In", path: "/signin" },
  { label: "Signup", path: "/signup" },
];

export const userMenu = [
  { label: "Dashboard", path: "/dashboard" },
  { label: "Profile Settings" },
  { label: "Messages" },
  { label: "Notifications" },
  { label: "Help Center" },
  { label: "Logout", type: "logout" },
];

const UserProfile = () => {
  const router = useRouter();
  const { auth, logout } = useAuth();

  const handleClick = async (menu: DropdownListDataType) => {
    try {
      if (menu?.path) {
        router.push(menu?.path);
      } else if (menu?.type === "logout") {
        await logoutAction();
        logout();
        router.push("/signin");
      }
    } catch (error) {
      console.error(error);
    }
  };

  const finalMenu = auth?.user ? (
    <UserMenu onSelect={handleClick} />
  ) : (
    <GuestMenu />
  );

  return (
    <Dropdown
      inlineAlign="left"
      content={finalMenu}
      contentClassName="z-20 mt-0"
      hover
    >
      <button
        className={classNames(
          "outline-0 border-0 h-full py-6 px-1 transition-all hover:text-slate-800 flex items-center gap-2"
        )}
      >
        <img
          src={"/assets/header/profile/profile_icon.svg"}
          alt="profile"
          className="w-6 h-6 object-cover flex-1"
        />
        <div className="flex flex-col items-start">
          {/* <span className="text-[0.55rem] font-semibold leading-3">
            Hello, {auth?.user ? getFirstName(auth?.user?.fullName) : "Sign In"}
          </span> */}
          <div className="flex items-center gap-1 flex-1">
            <span className="text-sm font-bold leading-4">
              {auth?.user ? "Account" : "Sign In"}
            </span>
            <img
              src={"/assets/header/profile/chevron_down.svg"}
              alt="drop"
              className="w-2 h-2 object-contain"
            />
          </div>
        </div>
      </button>
    </Dropdown>
  );
};

const UserMenu = ({
  onSelect,
}: {
  onSelect: (menu: DropdownListDataType) => void;
}) => {
  return (
    <DropdownList
      data={userMenu}
      className="!min-w-44 border-0"
      onSelect={(menu) => onSelect(menu)}
    />
  );
};

const GuestMenu = ({ className }: { className?: string }) => {
  const {
    // loading,
    // response,
  } = useAuth();
  const router = useRouter();

  const handleGoogle = async () => {
    window.open(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}${apiRoutes().auth.googleAuth}`,
      "_self"
    );
  };

  const handleFacebook = async () => {
    window.open(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}${apiRoutes().auth.facebookAuth}`,
      "_self"
    );
  };

  const handleLinkedin = async () => {
    window.open(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}${apiRoutes().auth.linkedinAuth}`,
      "_self"
    );
  };

  return (
    <div
      className={classNames(
        "dropdown-menu bg-white relative px-4 py-5 text-font shadow-lg w-full min-w-64 max-h-min overflow-auto focus:outline-0",
        className
      )}
      onClick={(e) => e?.stopPropagation()}
    >
      <div className="flex flex-col w-full flex-start gap-1 text-nowrap">
        <h4 className="font-semibold text-sm">Welcome to Seekler</h4>
        <span className="text-xs">Expand your business reach</span>
        <Button
          onClick={() => router.push("/signin")}
          variant="main-revert"
          className="w-max me-auto mt-3"
        >
          Sign in / Sign up
        </Button>
      </div>

      <div className="flex flex-col w-full flex-start gap-2 mt-4 mb-2 py-4 border-y border-stone-200">
        <span className="text-xs">Or Continue with</span>
        <div className="flex items-center gap-3">
          <ButtonRender onClick={() => handleGoogle()}>
            <img
              src={"/assets/pages/auth/google.svg"}
              alt="google"
              className="object-contain w-8 h-8"
            />
          </ButtonRender>
          <ButtonRender onClick={() => handleFacebook()}>
            <img
              src={"/assets/pages/auth/facebook.svg"}
              alt="facebook"
              className="object-contain w-8 h-8"
            />
          </ButtonRender>
          <ButtonRender onClick={() => handleLinkedin()}>
            <img
              src={"/assets/pages/auth/linkedin.svg"}
              alt="linkedin"
              className="object-contain w-8 h-8"
            />
          </ButtonRender>
        </div>
      </div>

      <div className="flex flex-col w-full flex-start gap-0.5">
        {["Inbox", "RFQ’s", "Settings", "My Account", "Pricing"].map(
          (menu, index) => (
            <Link
              key={index}
              href={menu}
              className="text-sm rounded-md w-full py-1 px-2 hover:bg-slate-200"
            >
              {menu}
            </Link>
          )
        )}
      </div>
    </div>
  );
};

interface ButtonRenderType
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
}
const ButtonRender: FC<ButtonRenderType> = ({ children, ...rest }) => (
  <button
    className="flex items-center justify-center shadow hover:shadow-md rounded-sm bg-slate-100 text-sm font-semibold [&>*]:hover:shadow-box"
    {...rest}
  >
    {children}
  </button>
);

export default UserProfile;
