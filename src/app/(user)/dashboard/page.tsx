import { Loader } from "@/components/ui/loader";
import { getCurrentUser } from "@/app/(user)/(auth)/actions";
import { redirect } from "next/navigation";

export default async function UserDashboardPage() {
  const user = await getCurrentUser();
  const isBuyer = user?.user_role === "buyer";

  if (!user || Object.keys(user).length === 0) {
    redirect("/signin");
  }

  if (isBuyer) {
    redirect("/dashboard/buyer");
  } else {
    redirect("/dashboard/seller");
  }

  return <Loader big center />;
}
