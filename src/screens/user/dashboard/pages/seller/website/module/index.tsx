"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch, SwitchTargetType } from "@/components/ui/input/switch";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { websiteDetails } from "../@redux/selectors";
import {
  Dropzone,
  DropzoneInputTargetType,
  InlinePlaceholder,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/website/@redux/slice";
import { updateWebsite } from "@/screens/user/dashboard/pages/seller/website/@redux/thunk";
import { InputLabel } from "@/components/ui/input/components/label";
import classNames from "classnames";

const Website = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values, meta, loading } = useSelector(websiteDetails);

  const handleChange = (e: SwitchTargetType | DropzoneInputTargetType) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values?.[e?.target?.name])
          ? Array.from(values?.[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setWebsiteDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const handleCancel = () => {
    try {
      dispatch(Actions.setWebsiteDetails(null));
    } catch (error) {
      console.error(error);
    }
  };

  const handleSave = () => {
    try {
      if (values) {
        // TODO
        // Function to simplify this
        const {
          _id,
          business_id,
          created_by,
          createdAt,
          updatedAt,
          __v,
          ...other
        } = values;

        const data = new FormData();
        Object.entries(other).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (
              Array.isArray(value) &&
              value.some((fl) => fl instanceof File)
            ) {
              value.forEach((file: any) => {
                if (file instanceof File) {
                  data.append(key, file);
                }
              });
            } else if (value instanceof File || value instanceof Blob) {
              data.append(key, value);
            } else if (
              Array.isArray(value) &&
              value.some((fl) => typeof fl === "string")
            ) {
              value.forEach((str) => {
                data.append(key, str);
              });
            } else {
              data.append(key, String(value));
            }
          }
        });

        // dispatch(
        //   updateWebsite({ portfolio_id: portfolio_id as string, data })
        // );
      }
    } catch (error) {
      console.error(error);
    }
  };
  console.log("website", {values});

  type FileOrString = File | string;

  const handleFileDelete = (file: FileOrString, fieldName: string) => {};

  return (
    <main className="bg-white rounded-lg md:shadow-lg md:p-4 w-full h-full">
      <div className="grid grid-cols-1 gap-6">
        <div className="flex flex-col gap-6 [&>*]:w-full">
          <h6 className="border-b border-stone-200 text-start px-1 py-3.5">
            Update Website
          </h6>

          <div className="w-full flex items-center justify-between gap-2 shadow-box p-3">
            <InputLabel>Enable Website</InputLabel>
            <Switch
              name="website_enable"
              checked={values?.website_enable}
              onValueChange={handleChange}
              defaultChecked={false}
            />
          </div>

          <div className="flex-1 flex flex-col gap-3 [&>*]:w-full">
            <InputLabel>Website Banner</InputLabel>
            <Dropzone
              containerClassName={classNames(
                "flex-1 [&_.file-preview]:!max-w-full [&_.file-preview]:max-h-96",
                values?.website_banner?.length > 0
                  ? "[&_.drop-content-wrapper]:!h-max"
                  : "[&_.drop-content-wrapper]:!h-64"
              )}
              onChange={(e) => handleChange(e as DropzoneInputTargetType)}
              onFileDelete={(file, fieldName) =>
                handleFileDelete(file, fieldName)
              }
              name="website_banner"
              value={values?.website_banner}
              preview
              inputProps={{
                disabled: values?.website_banner?.length > 0,
              }}
            >
              {values?.website_banner?.length > 0 ? " " : undefined}
            </Dropzone>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 mt-7 md:mt-10">
        <Button
          className="min-w-28 rounded-md"
          variant="destructive"
          onClick={() => handleCancel()}
        >
          Cancel
        </Button>
        <Button
          className="min-w-28 rounded-md"
          variant="main-revert"
          onClick={() => handleSave()}
        >
          Save
        </Button>
      </div>
    </main>
  );
};

export default Website;
