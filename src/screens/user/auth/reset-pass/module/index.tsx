"use client";

import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { Input } from "@/components/ui/input/text";
import { Loader } from "@/components/ui/loader";
import {
  capitalizeWords,
  hasRequiredKeys,
  isEmptyObject,
} from "@/lib/utils/data";
import * as Validation from "@/lib/utils/validation";
import useAuth from "@/screens/user/auth/@context/useAuth";
import { ChangeEvent, FormEvent, useState } from "react";
import { useRouter } from "next/navigation";
import AuthBox from "../../common/auth-box";

// TODO
// CAPTCHA

const ResetPassword = () => {
const router = useRouter();
  const { resetPassword, loading, response } = useAuth();
  const [values, setValues] = useState<any>({});
  const [error, setError] = useState<any>(null);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    try {
      const name = e?.target?.name;
      const value = e?.target?.value;

      const validator = Validation?.[
        `validate${capitalizeWords(name)}` as keyof typeof Validation
      ]?.(value as string);

      if (validator) {
        setError((prev: any) => {
          if (!prev) prev = {};
          if (!validator?.valid) {
            prev[name] = validator?.errors;
          } else {
            delete prev[name];
          }
          return prev;
        });
      }

      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    try {
      e?.preventDefault();
      if (!isEmptyObject(error)) return;

      await resetPassword(values);
      setValues({});
      setTimeout(() => {
        router.push("/signin");
      }, 2000);
    } catch (error) {
      console.error(error);
    }
  };

  const isValid = isEmptyObject(error) && hasRequiredKeys(values, ["email"]);

  return (
    <AuthBox
      title="Reset Your Password"
      subText="Forgot it? We'll help you recover your account."
      contentClassName="!max-w-md"
    >
      {loading && <Loader big center box />}
      {response && (
        <Alert className="mb-4" variant={response?.type}>
          {response?.type && (
            <AlertTitle className="capitalize">{response?.type}</AlertTitle>
          )}
          {response?.meta?.message && (
            <AlertDescription className="font-semibold">
              {response?.meta?.message}
            </AlertDescription>
          )}
        </Alert>
      )}

      <div className="w-full flex flex-col gap-8">
        <form
          className="w-full flex flex-col gap-8"
          onSubmit={(e) => handleSubmit(e)}
        >
          <div className="w-full grid grid-cols-1 gap-6">
            <Input
              type="email"
              name="email"
              label="Email Address or Username"
              onChange={handleChange}
              value={values?.email ?? ""}
              placeholder="Enter your register email or username"
              wrapperClassName="!rounded-full"
              error={error?.email}
            />

            <div className="flex justify-between items-center gap-4">
              <Checkbox label="CAPTCHA" labelClassName="whitespace-nowrap" />
            </div>
          </div>
          <Button
            type="submit"
            variant="main-revert"
            className="w-full rounded-full"
            disabled={!isValid}
          >
            Reset Password
          </Button>
        </form>

        <div className="flex items-center justify-center gap-1.5 text-sm text-center">
          <p>Do you have an Already account?</p>
          <button
            className="font-semibold hover:drop-shadow text-main"
            onClick={() => router.push("/signin")}
          >
            Sign in
          </button>
        </div>
      </div>
    </AuthBox>
  );
};

export default ResetPassword;
