import {
  <PERSON>ertD<PERSON>og,
  Alert<PERSON><PERSON>og<PERSON><PERSON>,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge, Variants } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Image from "@/components/ui/image";
import { InputTargetType, Select } from "@/components/ui/input/select";
import classNames from "classnames";
import { Edit, Eye, Trash2 } from "lucide-react";
import moment from "moment";
import { FC, isValidElement, ReactNode } from "react";
import { IoIosLock } from "react-icons/io";

interface FieldsType {
  id?: string;
  rfqId?: string;
  dateCreated?: ReactNode | string;
  image?: ReactNode | string;
  match?: ReactNode | string;
  title?: ReactNode | string;
  category?: ReactNode | string;
  budget?: ReactNode | string;
  proposals?: ReactNode | string;
  client?: ReactNode | string;
  contact?: ReactNode | string;
  views?: ReactNode | string;
  status?: string;
  date?: ReactNode | Date;
  responses?: ReactNode | string;
  invoice?: ReactNode | string;
  BuyerName?: ReactNode | string;
  payment?: ReactNode | string;
  TotalTransaction?: ReactNode | string;
}

type FieldKeys = keyof FieldsType;

interface ListCardType extends FieldsType {
  className?: string;
  classNames?: Partial<Record<FieldKeys, string>>;
  locked?: Partial<Record<FieldKeys, boolean>>;
  hideImage?: boolean;
  hideAction?: boolean;
  onSelect?: () => void;
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  statusOptions?: { label: string; value: string | boolean }[];
  onStatusChange?: (status: string) => void;
  actionClasses?: {
    view?: string;
    edit?: string;
    delete?: string;
    container?: string;
  };
  additionalStringFields?: {
    label: string;
    className?: string;
    locked?: boolean;
  }[];
  additionalActions?: ReactNode;
}

const ListCard: FC<ListCardType> = (props) => {
  const handleSelect = () => {
    if (props?.onSelect) {
      props?.onSelect?.();
    }
  };

  const handleView = () => {
    if (props?.onView) {
      props?.onView?.();
    }
  };

  const handleEdit = () => {
    if (props?.onEdit) {
      props?.onEdit?.();
    }
  };

  const handleDelete = () => {
    if (props?.onDelete) {
      props?.onDelete?.();
    }
  };

  const handleStatusChange = (e: InputTargetType) => {
    if (props?.onStatusChange) {
      const newStatus = e.target.value;
      props?.onStatusChange(newStatus);
    }
  };

  return (
    <div
      role="button"
      className={classNames(
        "w-full cursor-pointer text-sm flex items-center gap-4 px-4 py-3 border-none shadow-sm hover:shadow-md bg-white rounded-md whitespace-nowrap",
        props?.className
      )}
      onClick={() => handleSelect()}
    >
      {props?.rfqId && (
        <div
          className={classNames("w-full max-w-[10%]", props?.classNames?.rfqId)}
        >
          {props?.rfqId}
        </div>
      )}
      {!props?.hideImage && (
        <ColumnWrapper
          className={classNames("max-w-[10%]", props?.classNames?.image)}
        >
          {isValidElement(props?.image) ? (
            <>{props?.image}</>
          ) : (
            <Image
              src={props?.image as string}
              alt={String(props?.title)}
              width={60}
              height={60}
            />
          )}
        </ColumnWrapper>
      )}
      {props?.match && (
        <ColumnWrapper
          className={classNames("max-w-[10%]", props?.classNames?.match)}
        >
          <Badge
            className="font-bold leading-3 w-max py-2 px-3 capitalize my-1"
            variant={props?.match?.toString()?.toLowerCase() as Variants}
          >
            {props?.match}
          </Badge>
        </ColumnWrapper>
      )}

      {props?.title && (
        <ColumnWrapper
          className={classNames("max-w-[50%]", props?.classNames?.title)}
        >
          <LockedWrapper locked={props?.locked?.title}>
            {typeof props?.title === "string" ? (
              <div className="text-start flex flex-col items-start gap-0.5">
                <h3 className="font-medium text-gray-800">{props?.title}</h3>
                {props?.category && typeof props?.category === "string" && (
                  <span className="text-[0.7rem] text-stone-500">
                    {props?.category}
                  </span>
                )}
              </div>
            ) : (
              props?.title
            )}
          </LockedWrapper>
        </ColumnWrapper>
      )}

      {props?.additionalStringFields && (
        <>
          {Array.isArray(props?.additionalStringFields) &&
            props?.additionalStringFields?.length > 0 &&
            props?.additionalStringFields?.map((field, index) => {
              return (
                <ColumnWrapper
                  key={index}
                  className={classNames("text-center", field?.className)}
                >
                  <LockedWrapper locked={field?.locked}>
                    <span>{field?.label}</span>
                  </LockedWrapper>
                </ColumnWrapper>
              );
            })}
        </>
      )}

      {props?.budget && (
        <ColumnWrapper
          className={classNames(
            "max-w-[10%] text-center",
            props?.classNames?.budget
          )}
        >
          <LockedWrapper locked={props?.locked?.budget}>
            <span>{props?.budget}</span>
          </LockedWrapper>
        </ColumnWrapper>
      )}
      {props?.proposals && (
        <ColumnWrapper
          className={classNames(
            "max-w-[10%] text-center",
            props?.classNames?.proposals
          )}
        >
          <LockedWrapper locked={props?.locked?.proposals}>
            <span>{props?.proposals}</span>
          </LockedWrapper>
        </ColumnWrapper>
      )}
      {props?.client && (
        <ColumnWrapper
          className={classNames(
            "max-w-[10%] text-center",
            props?.classNames?.client
          )}
        >
          <LockedWrapper locked={props?.locked?.client}>
            <span>{props?.client}</span>
          </LockedWrapper>
        </ColumnWrapper>
      )}
      {props?.contact && (
        <ColumnWrapper
          className={classNames(
            "max-w-[10%] text-center",
            props?.classNames?.contact
          )}
        >
          <LockedWrapper locked={props?.locked?.contact}>
            <span>{props?.contact}</span>
          </LockedWrapper>
        </ColumnWrapper>
      )}
      {props?.dateCreated && (
        <div
          className={classNames(
            "w-full max-w-[15%]",
            props?.classNames?.dateCreated
          )}
        >
          {props?.dateCreated}
        </div>
      )}
      {props?.invoice && (
        <div
          className={classNames(
            "w-full max-w-[15%]",
            props?.classNames?.invoice
          )}
        >
          {props?.invoice}
        </div>
      )}
      {props?.BuyerName && (
        <div
          className={classNames(
            "w-full max-w-[15%]",
            props?.classNames?.BuyerName
          )}
        >
          {props?.BuyerName}
        </div>
      )}

      {props?.views && (
        <ColumnWrapper
          className={classNames(
            "max-w-[10%] text-center",
            props?.classNames?.views
          )}
        >
          <LockedWrapper locked={props?.locked?.views}>
            <span>{props?.views}</span>
          </LockedWrapper>
        </ColumnWrapper>
      )}

      {props?.status !== undefined && (
        <ColumnWrapper
          className={classNames(
            "max-w-[10%] text-center",
            props?.classNames?.status
          )}
        >
          <LockedWrapper locked={props?.locked?.status}>
            <div onClick={(e) => e.stopPropagation()}>
              <Select
                placeholder="Status"
                options={props?.statusOptions ?? []}
                value={props?.status}
                onChange={handleStatusChange}
                className={classNames(
                  // Safe conversion to string and then lowercase
                  (() => {
                    const statusStr = String(props?.status || '').toLowerCase();
                    if (statusStr === "active" || statusStr === "open") {
                      return "text-green-500";
                    } else if (statusStr === "closed" || statusStr === "close") {
                      return "text-red-500";
                    } else if (statusStr === "pending") {
                      return "text-yellow-500";
                    } else {
                      return "text-gray-500"; // default color
                    }
                  })()
                )}
              />
            </div>
          </LockedWrapper>
        </ColumnWrapper>
      )}
      {props?.payment && (
        <div
          className={classNames(
            "w-full max-w-[15%]",
            props?.classNames?.payment
          )}
        >
          {props?.payment}
        </div>
      )}
      {props?.TotalTransaction && (
        <div
          className={classNames(
            "w-full max-w-[15%]",
            props?.classNames?.TotalTransaction
          )}
        >
          {props?.TotalTransaction}
        </div>
      )}
      {props?.responses && (
        <ColumnWrapper
          className={classNames(
            "w-full max-w-[10%] text-center",
            props?.classNames?.responses
          )}
        >
          <LockedWrapper locked={props?.locked?.responses}>
            {props?.responses}
          </LockedWrapper>
        </ColumnWrapper>
      )}
      {props?.date && (
        <ColumnWrapper
          className={classNames(
            "max-w-[10%] text-center",
            props?.classNames?.date
          )}
        >
          <LockedWrapper locked={props?.locked?.date}>
            {props?.date instanceof Date ? (
              <span>{moment(props?.date).format("DD/MM/YYYY")}</span>
            ) : (
              props?.date
            )}
          </LockedWrapper>
        </ColumnWrapper>
      )}
      {!props?.hideAction && (
        <div
          className={classNames(
            "w-full max-w-[20%] flex items-center",
            props?.actionClasses?.container
          )}
        >
          {props?.onView && (
            <Button
              variant="plain"
              className={classNames("flex-1 pr-6", props?.actionClasses?.view)}
              onClick={(e) => {
                e.stopPropagation();
                handleView();
              }}
            >
              <Eye />
            </Button>
          )}
          {props?.onEdit && (
            <Button
              variant="plain"
              className={classNames("flex-1", props?.actionClasses?.edit)}
              onClick={(e) => {
                e.stopPropagation();
                handleEdit();
              }}
            >
              <Edit />
            </Button>
          )}
          {props?.onDelete && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="plain"
                  className={classNames(
                    "flex-1 text-red-500",
                    props?.actionClasses?.delete
                  )}
                  onClick={(e) => e.stopPropagation()}
                >
                  <Trash2 />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    requested item
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDelete()}
                    className="text-red-500"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      )}
      {props?.additionalActions}
    </div>
  );
};

interface HeaderDataType {
  label: string;
  key: string;
  className: string;
}

export const ListCardHeader = ({
  className,
  data,
}: {
  className?: string;
  data?: HeaderDataType[];
}) => {
  const titleData = data ?? [
    {
      label: "Image",
      key: "image",
      className: "max-w-[10%]",
    },
    {
      label: "Name",
      key: "name",
      className: "max-w-[50%] text-start",
    },
    {
      label: "Views",
      key: "views",
      className: "max-w-[10%] text-center",
    },
    {
      label: "Status",
      key: "status",
      className: "max-w-[10%] text-center",
    },
    {
      label: "Actions",
      key: "actions",
      className: "max-w-[20%] text-center",
    },
  ];

  return (
    <div
      className={classNames(
        "w-full text-sm flex items-center gap-4 px-4 py-1.5 border-none bg-transparent rounded-md uppercase",
        className
      )}
    >
      {titleData?.map((ele, index) => {
        return (
          <div key={index} className={classNames("w-full", ele?.className)}>
            {ele?.label}
          </div>
        );
      })}
    </div>
  );
};

export const ColumnWrapper = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => {
  return <div className={classNames("w-full", className)}>{children}</div>;
};

export const LockedWrapper = ({
  children,
  locked = false,
}: {
  children: ReactNode;
  locked?: boolean;
}) => {
  return <>{locked ? <LockedRender /> : children}</>;
};

export const LockedRender = ({ className }: { className?: string }) => {
  return (
    <div
      className={classNames(
        "flex items-center justify-center gap-1",
        className
      )}
    >
      <IoIosLock className="w-4 h-4 text-stone-400" />
      <span className="w-full flex-1 max-w-max min-w-12 h-[0.17rem] rounded-full mt-0.5 bg-stone-400" />
    </div>
  );
};

export default ListCard;
