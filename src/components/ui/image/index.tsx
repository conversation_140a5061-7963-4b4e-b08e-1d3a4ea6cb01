"use client";

import classNames from "classnames";
import { useState } from "react";
import { Loader } from "../loader";

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  className?: string;
  imageClassName?: string;
  fallbackSrc?: string;
  width?: number | string;
  height?: number | string;
}

const Image: React.FC<ImageProps> = ({
  src,
  className,
  imageClassName,
  fallbackSrc = "/assets/placeholder/image.svg",
  width = 50,
  height = 50,
  alt,
  ...props
}) => {
  const [imgSrc, setImgSrc] = useState(src || fallbackSrc);
  const [loading, setLoading] = useState(true);

  return (
    <div
      style={{
        width: typeof width === "number" ? `${width}px` : width,
        height: typeof height === "number" ? `${height}px` : height,
      }}
      className={classNames(
        "relative flex items-center justify-center rounded-md bg-gray-200 overflow-hidden",
        className
      )}
    >
      {loading && <Loader center box />}

      <img
        {...props}
        src={imgSrc}
        alt={alt}
        className={classNames(
          "w-full h-full object-cover transition-opacity duration-500",
          loading ? "opacity-0" : "opacity-100",
          imageClassName
        )}
        onLoad={() => setLoading(false)}
        onError={() => {
          setImgSrc(fallbackSrc || "/assets/placeholder/image.svg");
          setLoading(false);
        }}
      />
    </div>
  );
};

export default Image;
