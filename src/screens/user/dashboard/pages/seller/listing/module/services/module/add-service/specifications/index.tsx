"use client";

import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { TextEditor } from "@/components/ui/input/texteditor";
import { InputTargetType } from "@/components/ui/input/types";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import { serviceDetails } from "../../../@redux/selectors";
import { industries } from "@/constants/user/seller/company/update";

const Specifications = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(serviceDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values[e?.target?.name])
          ? Array.from(values[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setServiceDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="w-full min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Short Description</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Brief Service Summary
          </span>
        </div>
        <Textarea
          name="short_description"
          label="Brief Service Summary"
          placeholder="Type here"
          value={values?.short_description}
          className="min-h-20"
          onChange={handleChange}
          containerClassName="flex-1 w-full "
          maxLength={150}
          showCharCount
        />
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="w-full min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Full Description</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Service features benefits, and usage instruction
          </span>
        </div>
        <TextEditor
          name="full_description"
          value={values?.full_description}
          onChange={handleChange}
          containerClassName="flex-1 w-full"
          className="flex-1 rounded-md"
          label="Detailed description of Service features & benefits"
          maxLength={500}
          showCharCount
        />
      </div>

      <MultiSelect
        onValueChange={handleChange}
        name="industry_focus"
        defaultValue={values?.industry_focus}
        value={values?.industry_focus}
        label="Industry Focus"
        placeholder="Select industry"
        variant="inverted"
        maxCount={3}
        options={industries}
      />

      <MultiSelect
        onValueChange={handleChange}
        name="certification"
        defaultValue={values?.certification}
        value={values?.certification}
        label="Certifications"
        placeholder="Choose Certifications"
        variant="inverted"
        maxCount={3}
        options={[]}
        addNew
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* TODO */}
        {/* Spell correct */}
        <Input
          type="text"
          name="experiance"
          label="Experience Level"
          onChange={handleChange}
          value={values?.experiance}
          placeholder="Enter experience"
          containerClassName="flex-1"
          maxLength={70}
          showCharCount
        />
        <Input
          type="text"
          name="service_warranty"
          label="Service Warranty"
          onChange={handleChange}
          value={values?.service_warranty}
          placeholder="Enter Warranty Description"
          containerClassName="flex-1"
          maxLength={70}
          showCharCount
        />
        <Input
          type="text"
          name="after_sale_services"
          label="After-Sales Service"
          onChange={handleChange}
          value={values?.after_sale_services}
          placeholder="Post Service Support & maintenance"
          containerClassName="flex-1"
          maxLength={70}
          showCharCount
        />
      </div>
    </>
  );
};

export default Specifications;
