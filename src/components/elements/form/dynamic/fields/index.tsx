import { DropdownListDataType } from "@/components/ui/dropdown/list/types";
import { InputLabel } from "@/components/ui/input/components/label";
import { InputWrapper } from "@/components/ui/input/components/wrapper";
import { OptionType } from "@/components/ui/input/multi-select";
import { Loader } from "@/components/ui/loader";
import classNames from "classnames";
import { lazy, Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";

const TextField = lazy(() =>
  import("@/components/ui/input/text").then((module) => ({
    default: module.Input,
  }))
);

const Combobox = lazy(() =>
  import("@/components/ui/input/combobox").then((module) => ({
    default: module.Combobox,
  }))
);

const MultiSelect = lazy(() =>
  import("@/components/ui/input/multi-select").then((module) => ({
    default: module.MultiSelect,
  }))
);

const DualRangeSlider = lazy(() =>
  import("@/components/ui/input/dual-range/form-input").then((module) => ({
    default: module.DualRangeSliderFormInput,
  }))
);

const Checkbox = lazy(() =>
  import("@/components/ui/input/checkbox").then((module) => ({
    default: module.Checkbox,
  }))
);

const DateInput = lazy(() =>
  import("@/components/ui/input/date").then((module) => ({
    default: module.DateInput,
  }))
);

const Textarea = lazy(() =>
  import("@/components/ui/input/textarea").then((module) => ({
    default: module.Textarea,
  }))
);

const Switch = lazy(() =>
  import("@/components/ui/input/switch").then((module) => ({
    default: module.Switch,
  }))
);

interface FieldType {
  _id: string;
  type: string;
  fieldName: string;
  helpText: string;
  placeholder: string;
  isRequired: boolean;
  label: string;
  value: any;
  onChange: (e: any) => void;
  multiSelect?: boolean;
  options?: any[];
  min?: number;
  max?: number;
  step?: number;
}

const DynamicFieldRenderer = ({
  field,
  values = [],
  onChange,
}: {
  field: FieldType;
  values: {
    fieldId: string;
    values: string[];
  }[];
  onChange?: (e: any) => void;
}) => {
  const handleChange = (e: any) => {
    try {
      const value = e?.target?.value;
      if (onChange) {
        const payload = {
          fieldId: field?._id,
          values: Array.isArray(value) ? value : [value],
        };
        onChange(payload);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getFieldData = () => {
    try {
      if (!Array.isArray(values)) return undefined;
      return values?.find((v: any) => v?.fieldId === field?._id);
    } catch (error) {
      console.error(error);
      return undefined;
    }
  };
  const fieldData = getFieldData();

  if (!field || !field?.type) return null;

  return (
    <ErrorBoundary fallback={<span>{field?.label} failed to load</span>}>
      {field?.type === "text" && (
        <Suspense fallback={<Loader center />}>
          <TextField
            type="text"
            name={field?._id}
            label={field?.label}
            placeholder={field?.placeholder}
            value={fieldData?.values?.toString() ?? ""}
            onChange={handleChange}
            required={field?.isRequired}
          />
        </Suspense>
      )}
      {field?.type === "number" && (
        <Suspense fallback={<Loader center />}>
          <TextField
            type="text"
            name={field?._id}
            label={field?.label}
            placeholder={field?.placeholder}
            value={fieldData?.values?.toString() ?? ""}
            onChange={handleChange}
            required={field?.isRequired}
          />
        </Suspense>
      )}
      {field?.type === "email" && (
        <Suspense fallback={<Loader center />}>
          <TextField
            type="email"
            name={field?._id}
            label={field?.label}
            placeholder={field?.placeholder}
            value={fieldData?.values?.toString() ?? ""}
            onChange={handleChange}
            required={field?.isRequired}
          />
        </Suspense>
      )}
      {field?.type === "url" && (
        <Suspense fallback={<Loader center />}>
          <TextField
            type="text"
            name={field?._id}
            label={field?.label}
            placeholder={field?.placeholder}
            value={fieldData?.values?.toString() ?? ""}
            onChange={handleChange}
            required={field?.isRequired}
          />
        </Suspense>
      )}
      {field?.type === "dropdown" && (
        <Suspense fallback={<Loader center />}>
          <Combobox
            name={field?._id}
            label={field?.label}
            value={fieldData?.values?.toString() ?? ""}
            placeholder={field?.placeholder}
            onChange={handleChange}
            options={field?.options as DropdownListDataType[]}
            optionKeys={{
              label: "label",
              value: "_id",
            }}
            required={field?.isRequired}
          />
        </Suspense>
      )}
      {field?.type === "multi-dropdown" && (
        <Suspense fallback={<Loader center />}>
          <MultiSelect
            onValueChange={handleChange}
            name={field?._id}
            label={field?.label}
            placeholder={field?.placeholder}
            maxCount={10}
            options={field?.options as OptionType[]}
            optionKeys={{
              label: "label",
              value: "_id",
            }}
            defaultValue={fieldData?.values ?? []}
            value={values?.[field?.fieldName] ?? ""}
            required={field?.isRequired}
            addNew
          />
        </Suspense>
      )}
      {field?.type === "range" && (
        <Suspense fallback={<Loader center />}>
          <DualRangeSlider
            label={field?.label}
            onSlide={(e) => {
              const updatedEvent = {
                target: {
                  ...e?.target,
                  value: [`${e?.target?.value.join(":")}`],
                },
              };
              handleChange(updatedEvent);
            }}
            name={field?._id}
            value={
              fieldData?.values?.toString()?.split(":").map(Number) || [
                field?.min ?? 0,
                field?.max ?? 0,
              ]
            }
            min={field?.min}
            max={field?.max}
            labelPosition="bottom"
            rangeLabel={(range) => range}
            step={field?.step ?? 1}
            required={field?.isRequired}
            wrapperClassName="!border-none"
          />
        </Suspense>
      )}
      {field?.type === "checkbox" && (
        <Suspense fallback={<Loader center />}>
          <Checkbox
            name={field?._id}
            label={field?.label}
            onChange={handleChange}
            checked={
              fieldData?.values?.toString()?.toLowerCase() === "true" || false
            }
            required={field?.isRequired}
          />
        </Suspense>
      )}
      {field?.type === "date" && (
        <Suspense fallback={<Loader center />}>
          <DateInput
            name={field?._id}
            label={field?.label}
            placeholder={field?.placeholder}
            value={
              fieldData?.values?.toString()
                ? new Date(fieldData?.values?.toString())
                : undefined
            }
            onChange={handleChange}
            required={field?.isRequired}
          />
        </Suspense>
      )}
      {field?.type === "textarea" && (
        <Suspense fallback={<Loader center />}>
          <Textarea
            name={field?._id}
            label={field?.label}
            onChange={handleChange}
            value={fieldData?.values?.toString() ?? ""}
            placeholder={field?.placeholder}
            className="min-h-20"
            required={field?.isRequired}
          />
        </Suspense>
      )}
      {field?.type === "boolean" && (
        <Suspense fallback={<Loader center />}>
          <div
            className={classNames(
              "flex flex-col gap-1.5 items-center [&>*]:w-full"
            )}
          >
            <InputWrapper className={classNames("!h-full")}>
              <div className="w-full flex items-center gap-4 justify-between">
                <InputLabel>{field?.label}</InputLabel>
                <Switch
                  name={field?._id}
                  checked={
                    fieldData?.values?.toString()?.toLowerCase() === "true" ||
                    false
                  }
                  onValueChange={handleChange}
                  required={field?.isRequired}
                />
              </div>
            </InputWrapper>
          </div>
        </Suspense>
      )}
    </ErrorBoundary>
  );
};

export default DynamicFieldRenderer;
