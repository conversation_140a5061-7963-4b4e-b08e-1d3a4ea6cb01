"use client";

import { Combobox } from "@/components/ui/input/combobox";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import { InputTargetType } from "@/components/ui/input/types";
import { RootDispatch } from "@/redux/store";
import {
  categories,
  sub_categories,
  sub_sub_categories,
} from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getCategories,
  getSubCategories,
  getSubSubCategories,
} from "../../../../company/@redux/thunk";
import { serviceDetails, suggestedService } from "../../../@redux/selectors";
import ServiceName from "./name";

const ServiceInformation = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(serviceDetails);
  const suggestedServiceData = useSelector(suggestedService);
console.log({ suggestedServiceData });
  const {
    data: categoriesData,
    loading: categoriesLoading,
    meta: categoriesMeta,
  } = useSelector(categories);
  const {
    data: sub_categoriesData,
    loading: subCategoriesLoading,
    meta: subCategoriesMeta,
  } = useSelector(sub_categories);
  const {
    data: sub_sub_categoriesData,
    loading: subSubCategoriesLoading,
    meta: subSubCategoriesMeta,
  } = useSelector(sub_sub_categories);

  const [topCategories, setTopCategories] = useState<{
    category?: string;
    sub_category?: string;
  } | null>(null);

  useEffect(() => {
    if (suggestedServiceData) {
      setTopCategories({
        category: suggestedServiceData?.category_name,
        sub_category: suggestedServiceData?.sub_category_name,
      });
    }
  }, [suggestedServiceData]);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values[e?.target?.name])
          ? Array.from(values[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setServiceDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const handleCategoryChange = (e: InputTargetType) => {
    const name = e?.target?.name;
    setTopCategories((prev) => ({
      ...prev,
      [name]: e?.target?.value,
    }));

    if (name === "category") {
      dispatch(getSubCategories({ parentId: e?.target?.value }));

      const payload: any = {};
      payload.sub_sub_category = "";
      dispatch(Actions.setServiceDetails(payload));
      setTopCategories((prev) => ({
        ...prev,
        sub_category: "",
      }));
    } else if (name === "sub_category") {
      dispatch(getSubSubCategories({ parentId: e?.target?.value }));
      const payload: any = {};
      payload.sub_sub_category = "";
      dispatch(Actions.setServiceDetails(payload));
    }
  };

  return (
    <>
      <ServiceName handleChange={handleChange} values={values} />

      {/* TODO */}
      {/* Value coming as object */}
      {/* <Combobox
        name="sub_sub_category"
        label="Category"
        onChange={handleChange}
        value={
          typeof values?.sub_sub_category === "string"
            ? values?.sub_sub_category
            : (values?.sub_sub_category?._id ?? "")
        }
        placeholder="Select Category"
        options={categoriesData}
        optionKeys={{
          label: "sub_sub_category_name",
          value: "_id",
        }}
      /> */}

      <Combobox
        name="category"
        label="Category"
        onChange={handleCategoryChange}
        value={topCategories?.category || ""}
        placeholder="Select Category"
        options={categoriesData}
        dataLoading={categoriesLoading}
        optionKeys={{
          label: "category_name",
          value: "_id",
        }}
        dataAction={(params) => dispatch(getCategories(params))}
        resetAction={() => dispatch(getCategories())}
        onSearch={(value) => dispatch(getCategories({ search: value }))}
        pagination={categoriesMeta?.pagination}
        onToggle={(open) =>
          open && !categoriesData ? dispatch(getCategories()) : {}
        }
      />

      <Combobox
        name="sub_category"
        label="Sub Category"
        onChange={handleCategoryChange}
        value={topCategories?.sub_category || ""}
        placeholder="Select Category"
        options={sub_categoriesData}
        dataLoading={subCategoriesLoading}
        optionKeys={{
          label: "sub_category_name",
          value: "_id",
        }}
        dataAction={(params) => dispatch(getSubCategories(params))}
        resetAction={() => dispatch(getSubCategories())}
        onSearch={(value) => dispatch(getSubCategories({ search: value }))}
        pagination={subCategoriesMeta?.pagination}
        onToggle={(open) =>
          open && !sub_categoriesData ? dispatch(getSubCategories()) : {}
        }
      />

      <Combobox
        name="sub_sub_category"
        label="Sub Sub Category"
        onChange={handleChange}
        value={values?.sub_sub_category ?? ""}
        placeholder="Select Category"
        options={sub_sub_categoriesData}
        dataLoading={subSubCategoriesLoading}
        optionKeys={{
          label: "sub_sub_category_name",
          value: "_id",
        }}
        dataAction={(params) => dispatch(getSubSubCategories(params))}
        resetAction={() => dispatch(getSubSubCategories())}
        onSearch={(value) => dispatch(getSubSubCategories({ search: value }))}
        pagination={subSubCategoriesMeta?.pagination}
        onToggle={(open) =>
          open && !sub_sub_categoriesData ? dispatch(getSubSubCategories()) : {}
        }
        required
      />
    </>
  );
};

export default ServiceInformation;
