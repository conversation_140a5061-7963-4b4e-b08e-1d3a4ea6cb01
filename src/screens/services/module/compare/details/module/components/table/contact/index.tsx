"use client";

import QuoteRequestForm from "@/components/elements/form/inquiry";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import classNames from "classnames";
import { lazy, ReactNode } from "react";

const formData = [
  {
    title: "Request Quote",
    highlight: ["quote"],
    component: lazy(() => import("@/components/elements/form/inquiry/details")),
  },
  {
    component: lazy(() => import("@/components/elements/form/inquiry/contact")),
    footerType: "navigate",
  },
  {
    component: lazy(() => import("@/components/elements/form/inquiry/otp")),
    footerType: "navigate",
  },
  {
    component: lazy(() => import("@/components/elements/form/inquiry/thanks")),
    footerType: "last",
  },
];

const CompareContact = () => {
  return (
    <div className="shadow-box rounded-lg">
      <div className="flex flex-col [&>*]:w-full">
        <div className="flex flex-col md:flex-row gap-1 md:gap-0 items-start md:items-center text-start">
          <CardRender className="md:max-w-[20%]">
            <span className="text-nowrap text-lg md:text-base font-bold">
              Select to contact
            </span>
          </CardRender>
          <CardRender className="block md:hidden md:max-w-[20%]">
            <Checkbox
              labelClassName="whitespace-nowrap"
              containerClassName={classNames("block md:hidden flex-1")}
              label="Select all"
            />
          </CardRender>
          {[...Array(+4).keys()]?.map((_, index) => {
            return (
              <CardRender
                key={index}
                className="flex items-center justify-start gap-5"
              >
                <Checkbox
                  labelClassName="whitespace-nowrap block md:hidden"
                  containerClassName={classNames("flex-1")}
                  label="Tata Consultancy Services"
                />
              </CardRender>
            );
          })}
          <Button
            variant="main-revert"
            className="max-w-max mx-8 my-5 block md:hidden"
          >
            Get Quote
          </Button>
        </div>
        <div className="hidden md:flex items-center">
          <CardRender className="max-w-[20%]">
            <span className="text-base">Select all</span>
          </CardRender>
          <CardRender className="flex-[4] flex items-center justify-start gap-5">
            <Checkbox labelClassName="whitespace-nowrap" />

            <QuoteRequestForm
              data={formData}
              className="!flex flex-col items-start [&>*]:w-full overflow-visible"
            >
              <Button variant="main-revert" className="max-w-max">
                Get Quote
              </Button>
            </QuoteRequestForm>
          </CardRender>
        </div>
      </div>
    </div>
  );
};

const CardRender = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={classNames(
        "flex-1 px-8 md:px-6 py-3 md:py-6 text-start",
        className
      )}
    >
      {children}
    </div>
  );
};

export default CompareContact;
