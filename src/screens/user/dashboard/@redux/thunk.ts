import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from "../@api/common";

export const deleteMedia = createAsyncThunk<any, string, { state: RootState }>(
  "deleteMedia/fetchPortfolio",
  async (media_id, { rejectWithValue }) => {
    try {
      const response = await API.deleteMedia(media_id);
      window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });

      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const thunks = {
  deleteMedia,
};
