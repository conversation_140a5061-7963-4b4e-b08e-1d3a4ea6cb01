"use client";

import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { useState } from "react";
import { FaCaretDown } from "react-icons/fa";

export const DropdownOptions = [
  {
    label: "Overview",
    key: "overview",
  },
  {
    label: "ratings",
    key: "ratings",
  },
  {
    label: "products",
    key: "products",
  },
  {
    label: "services",
    key: "services",
  },
];

const TypeFilter = () => {
  const [selected, setSelected] = useState<any[]>([DropdownOptions[0]]);

  const selectedRender = (): string => {
    try {
      let baseText = selected?.[0]?.title ?? "Select";

      if (selected?.length > 1) {
        baseText = baseText + ` +${selected?.length - 1}`;
      }

      return baseText;
    } catch {
      return "Select";
    }
  };

  return (
    <div className="flex items-center flex-wrap gap-2">
      <Dropdown
        content={
          <DropdownList
            data={DropdownOptions}
            className="w-full max-w-sm sm:min-w-md md:min-w-80 border-0"
            onSelect={(option) => {
              setSelected((prev) => {
                let data = Array?.from(prev);

                if (data?.find((item) => item?.title === option?.title)) {
                  data = data?.filter((item) => item?.title !== option?.title);
                } else {
                  data?.push(option);
                }

                return data;
              });
            }}
            value={selected}
            highlightSelected
            checkbox
          />
        }
        contentClassName="z-20"
      >
        <button className="py-1.5 px-4 hover:bg-slate-50 transition-all flex items-center justify-center gap-2 border border-stone-500 rounded-md min-w-28">
          <span className="font-medium capitalize">{selectedRender()}</span>
          <FaCaretDown />
        </button>
      </Dropdown>
    </div>
  );
};

export default TypeFilter;
