"use client";

import CallVerifyBox from "@/components/elements/form/call-verify";
import { OTPBox, OTPBoxStatusType } from "@/components/elements/form/otp";
import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  Dropzone,
  DropzoneInputTargetType,
  InlinePlaceholder,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import { InputTargetType } from "@/components/ui/input/types";
import { deleteMediaHandler } from "@/lib/utils/helper";
import { RootDispatch } from "@/redux/store";
import {
  companyDetails,
  emailVerificationInitiate,
  emailVerify,
} from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import {
  companyEmailVerify,
  companyEmailVerifyInitiate,
} from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/thunk";
import { ChangeEvent, useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const Verification = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(companyDetails);
  const { meta: verifyMeta, loading } = useSelector(emailVerify);
  const { meta: verifyInitiateMeta, loading: verifyInitiateLoading } =
    useSelector(emailVerificationInitiate);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        if (e.target?.name === "business_logo") {
          payload[e?.target?.name] = e.target.files?.[0];
        } else {
          const fileArray = Array.from(e.target.files);
          let fileList = Array.isArray(values?.[e?.target?.name])
            ? Array.from(values?.[e?.target?.name])
            : [];

          fileList = fileList.concat(fileArray);
          payload[e?.target?.name] = fileList;
        }
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const initiateEmailVerification = useCallback(() => {
    if (values?.verified_by_email === true) return;

    dispatch(companyEmailVerifyInitiate({ email: values?.business_email }));
  }, [dispatch, values?.business_email, values?.verified_by_email]);

  useEffect(() => {
    initiateEmailVerification();
  }, [dispatch, initiateEmailVerification, values.business_email]);

  useEffect(() => {
    if (values?.email_otp?.length === 6) {
      dispatch(companyEmailVerify({ email_otp: values?.email_otp }));
    }
  }, [dispatch, values?.email_otp]);

  const handleDeleteMedia = async (file: any, name: string) => {
    const updatedFiles = await deleteMediaHandler(values, file, name);
    dispatch(Actions.setFormValues({ [name]: updatedFiles }));
  };

  const isEmailVerified = values?.verified_by_email === true;

  const isVerified = (): OTPBoxStatusType | undefined => {
    try {
      if (isEmailVerified) {
        return "verified";
      }

      return verifyInitiateMeta?.status === 200
        ? "initiated"
        : verifyMeta
          ? verifyMeta?.status === 200
            ? "verified"
            : "failed"
          : undefined;
    } catch {
      return undefined;
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 gap-6">
        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Business Email Verification</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
          </div>
          <OTPBox
            name="email_otp"
            onChange={(value) => handleChange(value)}
            onResendClick={() => initiateEmailVerification()}
            className="flex-1"
            status={isVerified()}
            statusLoading={verifyInitiateLoading || loading}
            disabled={isEmailVerified}
          />
        </div>

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Mobile Number Verification</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
          </div>
          <CallVerifyBox
            input={{
              onChange: (e) => console.log({ e }),
            }}
            className="flex-1"
          />
        </div>

        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
            <div className="flex items-center justify-start gap-1.5">
              <InputLabel>Government Registration Proof</InputLabel>
              <Badge
                className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
                variant="destructive"
              >
                Required
              </Badge>
            </div>
            <span className="text-stone-500 text-[0.7rem]">
              Required, Document Upload (PDF, JPG,PNG)
            </span>
            <span className="text-stone-500 text-[0.7rem]">
              Upload business license
            </span>
          </div>
          <Dropzone
            containerClassName="flex-1"
            onChange={(e) => handleChange(e)}
            name="government_registration_proof"
            value={values?.government_registration_proof}
            preview
            onFileDelete={(file, name) => handleDeleteMedia(file, name)}
            inputProps={{
              multiple: true,
            }}
          >
            <InlinePlaceholder />
          </Dropzone>
        </div>
      </div>
    </>
  );
};

export default Verification;
