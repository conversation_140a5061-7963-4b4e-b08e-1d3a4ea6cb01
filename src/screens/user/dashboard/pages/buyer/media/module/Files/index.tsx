"use client"

import React, { useState } from 'react';
import { Dropzone } from '@/components/ui/input/dropzone';
import { Button } from '@/components/ui/button';
import { MdOutlineFileUpload } from "react-icons/md";
import { Progress } from "@/components/ui/progress";

const Index = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [progress, setProgress] = useState(67); 
  
  const handleFileChange = (e: any) => {
    if (e?.target?.files) {
      const fileList = Array.from(e.target.files) as File[];
      setFiles([...files, ...fileList]);
    }
  };
  
  const handleFileDelete = (fileToDelete: File) => {
    const updatedFiles = files.filter(file => file !== fileToDelete);
    setFiles(updatedFiles);
  };
  
  return (
    <div className="bg-white p-6 rounded-lg">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium">All Files</h2>
        <Button
          variant="main-revert"
          size="default"
          type="button"
          className="w-24 rounded-sm"
        >
          <MdOutlineFileUpload /> Upload
        </Button>
      </div>
      
      <div className="border border-dashed border-gray-300 rounded-lg p-4">
        <Dropzone
          name="mediaFiles"
          onChange={handleFileChange}
          value={files}
          onFileDelete={handleFileDelete}
          preview={true}
          containerClassName="bg-transparent hover:bg-transparent w-full"
          contentClassName="border border-dashed border-gray-300 rounded-lg w-full"
          filePreviewClassName="mt-4 flex flex-wrap gap-4"
        />
        
        <div className="mt-6 relative">
          <div className="text-right mb-1 text-sm">
            <span className="text-[#E94F37] bg-[#ffe6e3] font-medium">{progress}%</span> / 100
          </div>
          <Progress
            value={progress}
            className="w-full !h-2 rounded-sm"
            indicatorClassName="!bg-[#E94F37]"
            showCount={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Index;