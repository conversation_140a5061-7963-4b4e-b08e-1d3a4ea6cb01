"use client";

import { Checkbox } from "@/components/ui/input/checkbox";
import { Input } from "@/components/ui/input/text";
import { filtersData } from "@/constants/filters";
import { getFilteredData } from "@/lib/utils/data";
import classNames from "classnames";
import { ChangeEvent, useState } from "react";
import { FiSearch } from "react-icons/fi";
import { RiErrorWarningLine } from "react-icons/ri";

const MobileAllFilters = () => {
  const [current, setCurrent] = useState<number>(0);
  const [searchValue, setSearchValue] = useState<string>("");

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    try {
      setSearchValue(e?.target?.value);
    } catch (error) {
      console.error(error);
    }
  };

  const filteredMenu = getFilteredData({
    data: filtersData,
    compareValue: searchValue,
    compareParams: ["label"],
  });

  const finalData = Boolean(searchValue)
    ? filteredMenu
    : filtersData?.[current]?.filters;

  return (
    <div className="flex items-start gap-0 h-full overflow-hidden text-sm">
      <div className="flex-1 flex flex-col items-start gap-0 bg-[#F1F3F6] h-full overflow-auto min-w-[40%] max-w-[40%]">
        {filtersData?.map((filter, index) => {
          const isACtive = index === current;
          return (
            <div
              role="button"
              key={index}
              onClick={() => setCurrent(index)}
              className={classNames(
                "w-full px-6 py-3 hover:bg-white transition-all",
                isACtive && "bg-white"
              )}
            >
              {filter?.title}
            </div>
          );
        })}
      </div>
      <div className="flex-2 flex flex-col items-start gap-0 bg-white h-full overflow-auto p-1.5">
        <div className="px-5">
          <Input
            type="text"
            placeholder={"Seach Category"}
            onChange={handleSearch}
            value={searchValue}
            wrapperClassName="rounded-full my-3"
            icon={<FiSearch size={6} />}
          />
        </div>
        {Array.isArray(finalData) && finalData?.length > 0 ? (
          finalData?.map((filter: any, index: number) => {
            return (
              <div key={index} className="px-5 py-5">
                <Checkbox
                  id={filter?.title}
                  label={filter?.title}
                  // checked={Boolean(filter?.checked)}
                />
              </div>
            );
          })
        ) : (
          <div className="flex items-center gap-1 text-red-600 my-3 px-3">
            <RiErrorWarningLine />
            <span className="text-sm">No Options Available</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileAllFilters;
