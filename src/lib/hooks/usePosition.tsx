import { RefObject, useCallback, useEffect, useState } from "react";

const usePosition = (
  triggerRef?: RefObject<HTMLElement | HTMLDivElement | null>,
  contentRef?: RefObject<HTMLElement | HTMLDivElement | null>
) => {
  const [position, setPositions] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
    top: number;
    right: number;
    bottom: number;
    left: number;
  }>({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });
  const [leftPosition, setLeftPosition] = useState<boolean>(false);
  const [topPosition, setTopPosition] = useState<boolean>(true);
  const [absolutePosition, setAbsolutePosition] = useState<{
    top: number;
    left: number;
  }>({ top: 0, left: 0 });

  const calculatePosition = useCallback(() => {
    try {
      const trigger = triggerRef?.current;
      const content = contentRef?.current;
      if (!trigger || !content) return;

      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const rect = trigger.getBoundingClientRect();

      const leftSpace = rect.left;
      const rightSpace = viewportWidth - rect.right;
      const isLeft = leftSpace > rightSpace;
      setLeftPosition(isLeft);

      const topSpace = rect.top;
      const bottomSpace = viewportHeight - rect.bottom;
      const isTop = topSpace > bottomSpace;
      setTopPosition(isTop);

      setPositions({
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
        top: rect.top,
        right: rect.right,
        bottom: bottomSpace ?? rect.bottom,
        left: rect.left,
      });

      const top = isTop
        ? rect.top + window.scrollY
        : rect.bottom + window.scrollY;

      setAbsolutePosition({
        top,
        left: rect.left + window.scrollX,
      });
    } catch (error) {
      console.error(error);
    }
  }, []);

  useEffect(() => {
    const trigger = triggerRef?.current;
    const content = contentRef?.current;

    if (!trigger) return;

    const handleUpdate = () => requestAnimationFrame(calculatePosition);

    window.addEventListener("resize", handleUpdate);
    window.addEventListener("scroll", handleUpdate, true);
    trigger.addEventListener("click", handleUpdate);
    content?.addEventListener("resize", handleUpdate);

    const resizeObserver = new ResizeObserver(() => handleUpdate());
    resizeObserver.observe(trigger);

    let roContent: ResizeObserver | null = null;
    if (content) {
      roContent = new ResizeObserver(() => handleUpdate());
      roContent.observe(content);
    }

    return () => {
      window.removeEventListener("resize", handleUpdate, true);
      window.removeEventListener("scroll", handleUpdate, true);
      trigger.removeEventListener("click", handleUpdate);
      content?.removeEventListener("click", handleUpdate);
      resizeObserver.disconnect();
      roContent?.disconnect();
    };
  }, [calculatePosition, contentRef, triggerRef]);

  return {
    ...position,
    toLeft: leftPosition,
    toTop: topPosition,
    absolutePosition,
  };
};

export default usePosition;
