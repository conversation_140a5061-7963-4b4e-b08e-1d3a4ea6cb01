'use client';

import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState, RootDispatch } from "@/redux/store";
import { getRFQDetail, handleProposalAction } from "../../@redux/thunk";
import { useParams } from "next/navigation";
import ListCard from "@/screens/company/listing/module/components/list/list-card/response-card";
import { AlertCircle } from "lucide-react";
import { ListingData } from "@/constants/listing";

interface PricingDetails {
  totalPrice: number;
  currency: string;
  unitPrice: number;
  discountOffered: number;
  discountType: string;
  paymentTerms: string;
  validUntil: string;
  priceBreakdown: any[];
  paymentMilestones: any[];
}

interface DeliveryDetails {
  estimatedDeliveryDate: string;
  leadTime: string;
  shippingMethod: string;
  incoterms: string;
  pickupFrom: string;
  deliveryTo: string;
  partialDeliveryOption: boolean;
  expeditedOption: boolean;
  milestones: any[];
}

interface OfferDetails {
  productName: string;
  productSpecifications: string;
  quantity: number;
  unit: string;
  deliverables: any[];
  features: string[];
  qualityCertifications: string[];
  warrantyPeriod: string;
  alternatives: any[];
}

interface Communication {
  contactPerson: {
    name: string;
    email: string;
    phone: string;
    role: string;
  };
  publicNotes: string;
  questions: string[];
}

interface Proposal {
  _id: string;
  seller: {
    _id: string;
    phone_number: string;
  };
  title: string;
  pricingDetails: PricingDetails;
  deliveryDetails: DeliveryDetails;
  offerDetails: OfferDetails;
  communication: Communication;
  status: string;
  businessInfo: {
    businessName: string;
    location: string;
    metaDescription: string;
    numberOfEmployees: string;
    rating: {
      score: number;
      count: number;
    };
    yearsInBusiness: number;
  };
}

interface RFQState {
  getRFQDetail: {
    data: {
      opportunitie: any;
      proposals: Proposal[];
      winner: any;
    } | null;
    loading: boolean;
    meta: any;
  };
}

interface TransformedProposal {
  _id: string;
  name: string;
  title: string;
  offerPrice: number;
  actualPrice: number;
  location: {
    flag: string;
    address: string;
    label: string;
  };
  verified: boolean;
  rating: {
    stars: number;
    reviewsCount: number;
  };
  description: string;
  shortIntro: string;
  strengthRange: string;
  businessAge: string;
  minOrderQuantity: number;
  tags: { title: string }[];
  status: string;
  images: string[];
}

const CompanyListings = () => {
  const dispatch = useDispatch<RootDispatch>();
  const params = useParams();
  const id = params?.id as string;
  const rfqState = useSelector((state: RootState & { user_buyer_rfqdetails: RFQState }) => state.user_buyer_rfqdetails);

  const proposals = rfqState.getRFQDetail.data?.proposals || [];

  useEffect(() => {
    if (id) {
      dispatch(getRFQDetail(id));
    }
  }, [dispatch, id]);

  const handleAction = async (action: 'accept' | 'reject', proposalId: string) => {
    if (!id) return;

    try {
      await dispatch(handleProposalAction({
        action,
        proposalId,
        opportunityId: id
      })).unwrap();
    } catch (error) {
      console.error(`Failed to ${action} proposal:`, error);
    }
  };

  // Transform proposal data to match ListCard format
  const transformedProposals: TransformedProposal[] = proposals.map((proposal, index) => {
    // Get static data from ListingData for location and images
    const staticData = ListingData[index % ListingData.length];

    return {
      _id: proposal._id,
      name: proposal.communication.contactPerson.name,
      title: proposal.title,
      offerPrice: proposal.pricingDetails.totalPrice,
      actualPrice: proposal.pricingDetails.unitPrice,
      // Keep location and images static
      location: {
        ...staticData.location,
        address: proposal.businessInfo.location
      },
      verified: true,
      rating: {
        stars: proposal.businessInfo.rating.score,
        reviewsCount: proposal.businessInfo.rating.count
      },
      description: proposal.offerDetails.productSpecifications,
      shortIntro: proposal.businessInfo.metaDescription,
      strengthRange: proposal.businessInfo.numberOfEmployees,
      businessAge: proposal.businessInfo.yearsInBusiness.toString(),
      minOrderQuantity: proposal.offerDetails.quantity,
      tags: proposal.offerDetails.features.map(feature => ({ title: feature })),
      status: proposal.status,
      images: staticData.images // Keep images static
    };
  });

  return (
    <div className="flex flex-col bg-white p-6">
      <div className="flex items-center mb-4">
        <h2 className="text-xl font-semibold">Total Responses: <span>{proposals.length}</span></h2>
      </div>

      <div className="flex flex-col gap-4">
        {proposals.length === 0 ? (
          <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
            <AlertCircle /> No Proposal Available
          </div>
        ) : (
          transformedProposals.map((proposal) => (
            <ListCard
              key={proposal._id}
              company={proposal}
              onAccept={() => handleAction('accept', proposal._id)}
              onReject={() => handleAction('reject', proposal._id)}
              isAccepted={proposal.status === 'accepted'}
              isRejected={proposal.status === 'rejected'}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default CompanyListings;