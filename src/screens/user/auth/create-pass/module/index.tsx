"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input/text";
import { Loader } from "@/components/ui/loader";
import { safeCall } from "@/lib/utils/api";
import {
  capitalizeWords,
  hasRequiredKeys,
  isEmptyObject,
} from "@/lib/utils/data";
import * as Validation from "@/lib/utils/validation";
import { useRouter, useSearchParams } from "next/navigation";
import { ChangeEvent, FormEvent, useState } from "react";
import { createPasswordAction } from "../../@actions";
import AuthBox from "../../common/auth-box";

const CreatePassword = () => {
  const router = useRouter();
  const [loading, serLoading] = useState<boolean>(false);
  const [response, setResponse] = useState<any>(false);
  const [values, setValues] = useState<any>({});
  const [error, setError] = useState<any>(null);
  const searchParams = useSearchParams();
  const resetToken = searchParams.get("reset_token");

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    try {
      const name = e?.target?.name;
      const value = e?.target?.value;

      const validator = Validation?.[
        `validate${capitalizeWords(name)}` as keyof typeof Validation
      ]?.(value as string);

      if (validator) {
        setError((prev: any) => {
          if (!prev) prev = {};
          if (!validator?.valid) {
            prev[name] = validator?.errors;
          } else {
            delete prev[name];
          }
          return prev;
        });
      }

      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    serLoading(true);
    try {
      e?.preventDefault();

      const payload = {
        reset_token: resetToken ?? "",
        password: values?.password as string,
      };
      const res = await safeCall(() => createPasswordAction(payload));
      setResponse(res);
      setValues({});
      setTimeout(() => {
        router.push("/signin");
      }, 2000);
    } catch (error) {
      console.error(error);
    } finally {
      serLoading(true);
    }
  };

  const isValid =
    isEmptyObject(error) &&
    hasRequiredKeys(values, ["password", "confirmPassword"]) &&
    values?.confirmPassword === values?.password;

  return (
    <AuthBox
      title="Create a new password"
      subText="Lorem ipsum dolor sit amet, consetetur sadipscing elitrsed diam nonumy"
      contentClassName="!max-w-md"
    >
      {loading && <Loader big center box />}
      {response && (
        <Alert className="mb-4" variant={response?.type}>
          {response?.type && (
            <AlertTitle className="capitalize">{response?.type}</AlertTitle>
          )}
          {response?.meta?.message && (
            <AlertDescription className="font-semibold">
              {response?.meta?.message}
            </AlertDescription>
          )}
        </Alert>
      )}

      <div className="w-full flex flex-col gap-8">
        <form
          className="w-full flex flex-col gap-8"
          onSubmit={(e) => handleSubmit(e)}
        >
          <div className="w-full grid grid-cols-1 gap-6">
            <Input
              type="password"
              name="password"
              label="New Password"
              onChange={handleChange}
              value={values?.password ?? ""}
              placeholder="Enter New Password"
              wrapperClassName="!rounded-full"
              autoComplete="off"
              error={error?.password}
            />
            <Input
              type="password"
              name="confirmPassword"
              label="Re-Enter New Password"
              onChange={handleChange}
              value={values?.confirmPassword ?? ""}
              placeholder="Re-Enter New Password"
              wrapperClassName="!rounded-full"
              autoComplete="off"
              error={
                values?.confirmPassword !== values?.password
                  ? "Password does not matched"
                  : undefined
              }
            />
          </div>
          <Button
            type="submit"
            variant="main-revert"
            className="w-full rounded-full"
            disabled={!isValid}
          >
            Create Password
          </Button>
        </form>

        <div className="flex items-center justify-center gap-1.5 text-sm text-center">
          <p>Do you have an Already account?</p>
          <button
            className="font-semibold hover:drop-shadow text-main"
            onClick={() => router.push("/signin")}
          >
            Sign in
          </button>
        </div>
      </div>
    </AuthBox>
  );
};

export default CreatePassword;
