import { MenuData } from "./types";

export const sellerMenuData: MenuData = [
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/dashboard.svg",
    title: "Insights",
    activePathname: ["/dashboard/seller"],
    pathname: "/dashboard/seller",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/listing.svg",
    title: "Listing",
    pathname: "/dashboard/seller/listing",
    activePathname: ["/dashboard/seller/listing/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
    title: "Website",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
    title: "Inbox",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    title: "Opportunities",
    pathname: "/dashboard/seller/opportunities",
    activePathname: ["/dashboard/seller/opportunities/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/paid.svg",
    title: "Paid Services",
    pathname: "/dashboard/seller/paid-services",
    activePathname: ["/dashboard/seller/paid-services/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/reviews.svg",
    title: "Reviews",
    pathname: "/dashboard/seller/reviews",
    activePathname: ["/dashboard/seller/reviews"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/media.svg",
    title: "Photos & Media",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/setting.svg",
    title: "Setting",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/support.svg",
    title: "Support",
  },
];
