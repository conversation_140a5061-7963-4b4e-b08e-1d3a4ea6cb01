'use client';

import React, { useState } from 'react';
import { Checkbox } from '@/components/ui/input/checkbox';
import { Mic, Search } from 'lucide-react';
import HeaderSearch from './headersearch';

const LeadsPage = () => {
  // State for checkboxes
  const [criteria, setCriteria] = useState({
    moqMatches: false,
    movMatches: false,
    categoryMatch: false,
    buyerPhone: false,
    buyerEmail: false
  });

  const [leadScore, setLeadScore] = useState({
    perfectMatch: false,
    bestMatch: false,
    goodMatch: false,
    lowMatch: false
  });

  const [purchaseTimeline, setPurchaseTimeline] = useState({
    immediate: false,
    withinWeek: false,
    withinMonth: false,
    withinTwoMonths: false
  });

  const handleCriteriaChange = (e:any) => {
    setCriteria({ ...criteria, [e.target.name]: e.target.value });
  };

  const handleLeadScoreChange = (e:any) => {
    setLeadScore({ ...leadScore, [e.target.name]: e.target.value });
  };

  const handlePurchaseTimelineChange = (e:any) => {
    setPurchaseTimeline({ ...purchaseTimeline, [e.target.name]: e.target.value });
  };

  const handleProductCategoryChange = (selections: any[]) => {
    console.log('Selected product categories:', selections);
  };

  const handleGeographicalChange = (selections: any[]) => {
    console.log('Selected locations:', selections);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto relative">
      <div className="space-y-8">
        {/* Product Categories Section */}
        <div className="relative ">
          <h2 className="text-lg font-medium text-gray-700 mb-3">Product Categories</h2>
          <div className="flex items-center w-full">
            <HeaderSearch 
              className="flex-1" 
              placeholder='product category'
              onSelectionsChange={handleProductCategoryChange}
              optionsData={[{ id: '1', title: 'Electronics' }, { id: '2', title: 'Clothing' }]}
            />
          </div>
        </div>

        {/* Geographical Preferences Section */}
        <div className="relative ">
          <h2 className="text-lg font-medium text-gray-700 mb-3">Geographical Preferences</h2>
          <div className="flex items-center w-full">
            <HeaderSearch 
              className="flex-1" 
              placeholder='Geographical Preferences'
              onSelectionsChange={handleGeographicalChange}
              optionsData={[{ id: '1', title: 'USA' }, { id: '2', title: 'Europe' }]}
            />
          </div>
        </div>

        {/* Criteria Section */}
        <div className="mb-8">
          <h2 className="text-lg font-medium mb-4 text-gray-700">{`Criteria's`}</h2>
          <div className="space-y-4">
            <Checkbox 
              name="moqMatches"
              label="MOQ Matches Offering"
              onChange={handleCriteriaChange}
            />
            <Checkbox 
              name="movMatches"
              label="MOV matches seller's range"
              onChange={handleCriteriaChange}
            />
            <Checkbox 
              name="categoryMatch"
              label="Product/Service category match"
              onChange={handleCriteriaChange}
            />
            <Checkbox 
              name="buyerPhone"
              label="Buyer phone verified"
              onChange={handleCriteriaChange}
            />
            <Checkbox 
              name="buyerEmail"
              label="Buyer email verified"
              onChange={handleCriteriaChange}
            />
          </div>
        </div>

        {/* Lead Score Range Section */}
        <div>
          <h2 className="text-lg font-medium mb-4 text-gray-700">Lead Score Range</h2>
          <div className="space-y-4">
            <Checkbox 
              name="perfectMatch"
              label="Perfect Match"
              onChange={handleLeadScoreChange}
            />
            <Checkbox 
              name="bestMatch"
              label="Best Match"
              onChange={handleLeadScoreChange}
            />
            <Checkbox 
              name="goodMatch"
              label="Good Match"
              onChange={handleLeadScoreChange}
            />
            <Checkbox 
              name="lowMatch"
              label="Low Match"
              onChange={handleLeadScoreChange}
            />
          </div>
        </div>

        {/* Purchase Intent Timeline Section */}
        <div>
          <h2 className="text-lg font-medium mb-4 text-gray-700">Purchase Intent Timeline</h2>
          <div className="space-y-4">
            <Checkbox 
              name="immediate"
              label="Immediate"
              onChange={handlePurchaseTimelineChange}
            />
            <Checkbox 
              name="withinWeek"
              label="Within a Week"
              onChange={handlePurchaseTimelineChange}
            />
            <Checkbox 
              name="withinMonth"
              label="Within a Month"
              onChange={handlePurchaseTimelineChange}
            />
            <Checkbox 
              name="withinTwoMonths"
              label="Within 2 Months"
              onChange={handlePurchaseTimelineChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeadsPage;