import React from 'react';
// import image from '@/pages/seller/component/banner/electronic.jpeg';

// TODO
// image correction

export default function ImageWithTextOverlay() {
  return (
    <div className="relative w-full h-72">
 
      <div
        className="w-full h-full bg-center brightness-50 bg-cover shadow-lg rounded"
        // style={{ backgroundImage: `url(${image})` }}
      ></div>

      
      <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
        <h2 className="text-white text-3xl font-bold px-4 py-2  bg-opacity-50 rounded">
          Consumer Electronic
        </h2>
        <p className="text-white text-lg font-bold px-4 py-2 mt-2  bg-opacity-40 rounded">
          Discover new and Trending Industries
        </p>
      </div>
    </div>
  );
}
