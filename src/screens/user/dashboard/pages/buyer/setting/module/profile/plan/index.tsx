'use client';

import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootDispatch } from "@/redux/store";
import { getProfileDetailsSelector } from "../../../@redux/selector";
import { Button } from "@/components/ui/button";

const CurrentPlan = () => {
  const dispatch = useDispatch<RootDispatch>();
  const profile = useSelector(getProfileDetailsSelector);

  const planName = profile.data?.currentPlan?.plan?.name || "No Plan";
  const buyLeadPlanName = profile.data?.currentPlan?.buyLeadPlan?.name || "No Buy Lead Plan";

  return (
    <div className="bg-white rounded-md shadow-sm p-6 max-w-7xl mx-auto">
      <h2 className="text-lg font-medium mb-6">Current Plan</h2>
      <hr />
      <br />
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-base font-medium text-gray-800">{planName}</h3>
          <p className="text-gray-500 text-sm mt-1">{buyLeadPlanName}</p>
        </div>

        <Button
          variant="main-revert"
          size="lg"
          className="text-red-500 border-red-500 hover:bg-red-50 rounded-md"
        >
          Change Plan
        </Button>
      </div>
    </div>
  );
};

export default CurrentPlan;
