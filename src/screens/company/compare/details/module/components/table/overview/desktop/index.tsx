"use client";
import {
  compareOverview,
  finalOverview,
  overviewValues,
} from "@/constants/compare";
import {
  compareProducts,
  finalProducts,
  productValues,
} from "@/constants/product";
import { selectedColumn } from "@/screens/company/compare/@redux/selectors";
import classNames from "classnames";
import { ReactNode } from "react";
import { useSelector } from "react-redux";

const DesktopOverview = () => {
  const current = useSelector(selectedColumn);
  return (
    <div className="w-full h-full overflow-y-hidden flex">
      <table className="border-collapse table-fixed">
        <tbody>
          {finalOverview.map((_, trIndex) => {
            return (
              <tr key={trIndex} className="">
                <TdRender className="md:text-nowrap">
                  {compareOverview?.[trIndex]?.name}
                </TdRender>
                {[...Array(4).keys()]?.map((_, index) => {
                  const isActive = current === index;
                  if (window?.innerWidth < 768 && !isActive) return null;
                  
                  // FIXED: Access data correctly - trIndex gets the field, content gets the value
                  const productData = overviewValues?.[trIndex]?.content;
                  const fieldName = compareOverview?.[trIndex]?.name;
                  
                  return (
                    <TdRender key={index} className="md:text-nowrap">
                      {Array.isArray(productData) ? (
                        <TagsRender tags={productData as string[]} />
                      ) : (
                        <StatusText
                          content={productData || "N/A"}
                          fieldName={fieldName}
                        />
                      )}
                    </TdRender>
                  );
                })}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

const TdRender = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => (
  <td
    className={classNames(
      "border-y border-l first-of-type:border-l-0 first-of-type:font-bold border-stone-200 text-xs px-6 py-3 md:text-nowrap",
      className
    )}
  >
    {children}
  </td>
);

const TagsRender = ({ tags }: { tags: string[] }) => {
  return (
    <div className="flex flex-wrap gap-1">
      {tags?.map((tag, index) => {
        return (
          <button
            key={index}
            className="flex items-center justify-center px-3 py-0.5 bg-stone-50 hover:bg-stone-100 border border-stone-300 rounded-full text-stone-800 text-xs"
          >
            {tag}
          </button>
        );
      })}
    </div>
  );
};

const StatusText = ({ content, fieldName }: { content: string; fieldName: string }) => {
  // Only apply colors for specific fields
  const shouldApplyColor = fieldName === "Verified" || fieldName === "Availability";
  
  if (!shouldApplyColor) {
    return <span>{content}</span>;
  }
  
  // Color logic for Verified field
  if (fieldName === "Verified") {
    if (content === "Verified") {
      return <span className="text-green-600 font-medium">✓ {content}</span>;
    }
    if (content === "Not Verified") {
      return <span className="text-red-600 font-medium">✗ {content}</span>;
    }
  }
  
  // Color logic for Availability field
  if (fieldName === "Availability") {
    if (content === "Available") {
      return <span className="text-green-600 font-medium">{content}</span>;
    }
    if (content === "Out of stock") {
      return <span className="text-red-600 font-medium">{content}</span>;
    }
  }
  
  return <span>{content}</span>;
};

export default DesktopOverview;