"use client";

import useOutsideAlert from "@/lib/hooks/useOutsideAlert";
import usePosition from "@/lib/hooks/usePosition";
import classNames from "classnames";
import { forwardRef, useEffect, useImperativeHandle, useRef } from "react";
import { createPortal } from "react-dom";
import useDropdown from "./@context";
import DropdownProvider from "./@context/provider";
import { DropdownReferenceType, DropdownType } from "./types";

const DropdownComp = forwardRef<DropdownReferenceType, DropdownType>(
  (
    {
      children,
      content,
      dropdownStyle,
      inlineAlign,
      verticalAlign,
      hover,
      className,
      contentClassName,
      actionClassName,
      onToggle,
      ...rest
    },
    ref
  ) => {
    const actionRef = useRef<HTMLDivElement>(null);
    const wrapperRef = useRef(null);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const {
      open,
      setDropdownOpen,
      handleClose,
      handleDropdown,
      toggleDropdown,
    } = useDropdown();

    const { toLeft, toTop, absolutePosition, width } = usePosition(
      actionRef,
      dropdownRef
    );

    useEffect(() => {
      if (hover) {
        setDropdownOpen(true);
      }
    }, [hover, setDropdownOpen]);

    useImperativeHandle(ref, () => ({
      toggle: (v: boolean) => toggleDropdown(v),
      open,
    }));

    useOutsideAlert([wrapperRef, dropdownRef], handleClose);

    const dropdownTop = absolutePosition.top;

    const dropdownLeft =
      inlineAlign === "left" || toLeft
        ? absolutePosition.left
        : absolutePosition.left +
          width -
          (dropdownRef.current?.offsetWidth || 0);

    return (
      <div
        ref={wrapperRef}
        className={classNames("dropdown relative group", className)}
      >
        <div
          ref={actionRef}
          role="button"
          className={classNames(
            "dropdown-action cursor-pointer",
            actionClassName
          )}
          onClick={(e) => handleDropdown(e)}
        >
          {children}
        </div>
        {open ? (
          hover ? (
            <div
              ref={dropdownRef}
              className={classNames(
                "dropdown-content rounded-base bg-transparent absolute w-auto h-auto overflow-visible z-10",
                hover &&
                  "hidden group-hover:block group-hover:bg-white group-hover:bg-opacity-10",
                toLeft || inlineAlign === "left" ? "right-0" : "left-0",
                toTop || verticalAlign === "top" ? "bottom-full" : "top-full",
                contentClassName
              )}
              style={{
                ...dropdownStyle,
              }}
              {...rest}
            >
              {content}
            </div>
          ) : (
            createPortal(
              <div
                ref={dropdownRef}
                className={classNames(
                  "dropdown-content rounded-base bg-transparent absolute w-auto h-auto overflow-visible z-10",
                  hover &&
                    "hidden group-hover:block group-hover:bg-white group-hover:bg-opacity-10",
                  contentClassName
                )}
                style={{
                  top: dropdownTop,
                  left: dropdownLeft,
                  ...dropdownStyle,
                }}
                {...rest}
              >
                {content}
              </div>,
              document.body
            )
          )
        ) : null}
      </div>
    );
  }
);

DropdownComp.displayName = "DropdownComp";

const Dropdown = forwardRef<DropdownReferenceType, DropdownType>(
  ({ ...rest }, ref) => {
    return (
      <DropdownProvider {...rest}>
        <DropdownComp {...rest} ref={ref} />
      </DropdownProvider>
    );
  }
);

Dropdown.displayName = "Dropdown";

export * from "./types";
export { useDropdown };
export default Dropdown;
