"use client";

import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import { ChangeEvent, useCallback, useEffect, useState, useRef } from "react";
import {
  Dropzone,
  DropzoneInputTargetType,
  InlinePlaceholder,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { InputTargetType } from "@/components/ui/input/types";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import { OTPBox } from "@/components/elements/form/otp";
import { X, FileText } from "lucide-react";
import {
  businessDetails,
  emailVerificationInitiate,
  emailVerify,
} from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/slice";
import { useDispatch, useSelector } from "react-redux";
import {
  getBusinessDetails,
  companyEmailVerifyInitiate,
  companyEmailVerify,
} from "../../@redux/thunk";
import { RootDispatch } from "@/redux/store";

interface VerificationProps {
  onValidationChange?: (isValid: boolean) => void;
  isSubmitted?: boolean;
}

interface RegistrationProof {
  url: string;
  file?: File;
  media_id?: string;
}

interface ProofWithUrl {
  url: string;
  media_id?: string;
}

type ProofType = File | ProofWithUrl;

const Verification = ({
  onValidationChange,
  isSubmitted,
}: VerificationProps) => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values = {} } = useSelector(businessDetails);
  const { meta: verifyMeta, loading } = useSelector(emailVerify);
  const { meta: verifyInitiateMeta, loading: verifyInitiateLoading } =
    useSelector(emailVerificationInitiate);
  const [registrationProofs, setRegistrationProofs] = useState<
    RegistrationProof[]
  >([]);

  //  FIX: Use refs to track if email verification has been initiated and prevent multiple calls
  const emailVerificationInitiated = useRef(false);
  const lastBusinessEmail = useRef<string>("");

  useEffect(() => {
    // Only update if the values have actually changed
    if (values?.government_registration_proof) {
      const proofs = Array.isArray(values.government_registration_proof)
        ? values.government_registration_proof
        : [values.government_registration_proof];

      const proofUrls: RegistrationProof[] = proofs
        .map((proof: any) => {
          if (proof instanceof File) {
            return {
              url: URL.createObjectURL(proof),
              file: proof,
            } as RegistrationProof;
          } else if (proof?.url) {
            return {
              url: proof.url,
              media_id: proof.media_id,
            } as RegistrationProof;
          }
          return null;
        })
        .filter(
          (proof: RegistrationProof | null): proof is RegistrationProof =>
            proof !== null
        );

      // Only update state if it's actually different
      if (
        JSON.stringify(
          proofUrls.map((p) => ({ url: p.url, media_id: p.media_id }))
        ) !==
        JSON.stringify(
          registrationProofs.map((p) => ({ url: p.url, media_id: p.media_id }))
        )
      ) {
        setRegistrationProofs(proofUrls);
      }
    } else if (registrationProofs.length > 0) {
      // Clear if no proofs in values but we have local state
      setRegistrationProofs([]);
    }

    // Cleanup blob URLs on unmount
    return () => {
      registrationProofs.forEach((proof) => {
        if (proof.file) {
          URL.revokeObjectURL(proof.url);
        }
      });
    };
  }, [
    values?.government_registration_proof,
    values?.business_email_verified,
    values?.phone_number_verified,
  ]);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        const newProofs: RegistrationProof[] = fileArray.map((file) => ({
          file,
          url: URL.createObjectURL(file),
        }));

        // 🔥 FIX: Combine existing proofs with new files properly
        const updatedProofs = [...registrationProofs, ...newProofs];
        setRegistrationProofs(updatedProofs);

        // Create the array for Redux - combine existing files and new files
        const allFiles = updatedProofs.map((proof) => {
          if (proof.file) {
            return proof.file; // New file to be uploaded
          } else {
            return { url: proof.url, media_id: proof.media_id }; // Existing uploaded file
          }
        });

        dispatch(
          Actions.setFormValues({
            government_registration_proof: allFiles,
          })
        );
      } else {
        dispatch(
          Actions.setFormValues({
            [e?.target?.name]: e?.target?.value,
          })
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  // FIX: Improved email verification initiation with proper controls
  const initiateEmailVerification = useCallback(() => {
    if (
      values?.business_email &&
      values.business_email !== lastBusinessEmail.current &&
      !emailVerificationInitiated.current &&
      !values?.business_email_verified
    ) {
      emailVerificationInitiated.current = true;
      lastBusinessEmail.current = values.business_email;

      dispatch(companyEmailVerifyInitiate({ email: values.business_email }));
    }
  }, [dispatch, values?.business_email, values?.business_email_verified]);

  // FIX: Only initiate verification when business_email changes and is not already verified
  useEffect(() => {
    if (values?.business_email && !values?.business_email_verified) {
      // Reset the flag if email changed
      if (values.business_email !== lastBusinessEmail.current) {
        emailVerificationInitiated.current = false;
      }
      initiateEmailVerification();
    }
  }, [
    values?.business_email,
    values?.business_email_verified,
    initiateEmailVerification,
  ]);

  // FIX: Reset verification state when email is verified or changes
  useEffect(() => {
    if (values?.business_email_verified) {
      emailVerificationInitiated.current = false;
    }
  }, [values?.business_email_verified]);

  // Handle OTP verification
  useEffect(() => {
    if (values?.email_otp?.length === 6) {
      dispatch(companyEmailVerify({ email_otp: values?.email_otp }));
    }
  }, [dispatch, values?.email_otp]);

  const handleRemoveDoc = (index: number, event?: React.MouseEvent) => {
    event?.stopPropagation();

    const proofToRemove = registrationProofs[index];

    // Clean up blob URL if it's a new file
    if (proofToRemove.file) {
      URL.revokeObjectURL(proofToRemove.url);
    }

    // Remove from local state immediately
    const updatedProofs = registrationProofs.filter((_, i) => i !== index);
    setRegistrationProofs(updatedProofs);

    // Handle deletion of existing files (those with media_id)
    const updatedDeleteMediaIds = proofToRemove.media_id
      ? [
          ...(values.delete_business_registration_certificates || []),
          proofToRemove.media_id,
        ]
      : values.delete_business_registration_certificates || [];

    // Create updated file array for Redux
    const updatedFiles = updatedProofs.map((proof) => {
      if (proof.file) {
        return proof.file; // New file to be uploaded
      } else {
        return { url: proof.url, media_id: proof.media_id }; // Existing uploaded file
      }
    });

    console.log("Removing document:", {
      index,
      proofToRemove,
      updatedProofs,
      updatedFiles,
      updatedDeleteMediaIds,
    });

    // Update Redux state
    dispatch(
      Actions.setFormValues({
        government_registration_proof: updatedFiles,
        ...(proofToRemove.media_id && {
          delete_business_registration_certificates: updatedDeleteMediaIds,
        }),
      })
    );
  };

  // FIX: Manual resend function for OTP box
  const handleResendOTP = useCallback(() => {
    if (values?.business_email) {
      console.log("Manual resend OTP for:", values.business_email);
      emailVerificationInitiated.current = false; // Reset the flag
      dispatch(companyEmailVerifyInitiate({ email: values.business_email }));
      emailVerificationInitiated.current = true; // Set it back after dispatch
    }
  }, [dispatch, values?.business_email]);

  // Helper function to determine file type
  const getFileType = (url: string, file?: File): "image" | "pdf" => {
    if (file) {
      return file.type.startsWith("image/") ? "image" : "pdf";
    }
    // Check URL extension for existing files
    return url.toLowerCase().includes(".pdf") ? "pdf" : "image";
  };

  // Render individual file preview
  const renderFilePreview = (proof: RegistrationProof, index: number) => {
    const fileType = getFileType(proof.url, proof.file);

    return (
      <div key={index} className="relative group">
        <div className="w-24 h-24 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center overflow-hidden">
          {fileType === "image" ? (
            <img
              src={proof.url}
              alt={`Proof ${index + 1}`}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="flex flex-col items-center justify-center p-2">
              <FileText className="w-8 h-8 text-red-500 mb-1" />
              <span className="text-xs text-gray-600 text-center">PDF</span>
            </div>
          )}

          {/* Remove button */}
          <button
            type="button"
            onClick={(e) => handleRemoveDoc(index, e)}
            className="absolute -top-1 right-3 bg-red-500 hover:bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center transition-colors duration-200 shadow-md"
          >
            <X size={12} />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Business Registration Certificate */}
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Govt Registration Proof</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload your government registration document
          </span>
        </div>

        <div className="flex-1 w-full">
          <Dropzone
            containerClassName="flex-1"
            onChange={(e) => handleChange(e)}
            name="government_registration_proof"
            //  MAIN FIX: Only pass NEW files (File objects) to dropzone, not existing ones
            value={registrationProofs
              .filter((proof) => proof.file)
              .map((proof) => proof.file as File)}
            preview={false}
            onFileDelete={(file) => {
              const index = registrationProofs.findIndex(
                (proof) => proof.file === file
              );
              if (index !== -1) {
                handleRemoveDoc(index);
              }
            }}
            inputProps={{
              multiple: true,
              accept: "image/*,.pdf",
            }}
          >
            {registrationProofs.length === 0 ? (
              <InlinePlaceholder />
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-2">
                {registrationProofs.map((proof, index) =>
                  renderFilePreview(proof, index)
                )}
              </div>
            )}
          </Dropzone>
        </div>
      </div>

      {/* Business Email Verification */}
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Business Email Verification</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
        </div>
        <div className="flex-1">
          <OTPBox
            name="email_otp"
            onChange={(value) => handleChange(value)}
            onResendClick={handleResendOTP}
            className="flex-1"
            status={
              values?.business_email_verified
                ? "verified"
                : verifyMeta?.status === 200
                  ? "verified"
                  : verifyInitiateMeta?.status === 200
                    ? "initiated"
                    : verifyMeta && verifyMeta.status >= 400
                      ? "failed"
                      : undefined
            }
            statusLoading={verifyInitiateLoading || loading}
          />
        </div>
      </div>

      {/* Business SMS Verification */}
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Business SMS Verification</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
        </div>
        <div className="flex-1">
          <OTPBox
            name="sms_otp"
            onChange={(value) => handleChange(value)}
            onResendClick={() => {
              // TODO: Implement SMS verification API call
              console.log("SMS verification resend clicked");
            }}
            className="flex-1"
            status={
              // TODO: Add SMS verification status logic
              values?.phone_number_verified ? "verified" : undefined
            }
            statusLoading={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Verification;
