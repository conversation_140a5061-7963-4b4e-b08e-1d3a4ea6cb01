import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "@/redux/root-reducer";

// Select the user_buyer_favourites slice from the root state
const reducer = (state: RootState) => state.user_buyer_favourites;

// Selector to get the status/result of adding a favourite item
export const getAddFavouriteStatus = createSelector(
  [reducer],
  (reducer) => reducer.addFavourite
);

// Selector to get the list of favourite items
export const getFavouritesSelector = createSelector(
  [reducer],
  (reducer) => reducer.getFavourites
);
