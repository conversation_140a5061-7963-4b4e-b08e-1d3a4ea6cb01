import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import classNames from "classnames";
import { FC } from "react";

interface SuggestionsType {
  data: Record<string, any>[];
  className?: string;
  value?: string;
  params?: {
    subText?: string[];
    description?: string[];
  };
  onSelect?: (option: Record<string, any>) => void;
}

const Suggestions: FC<SuggestionsType> = ({
  data,
  params,
  onSelect,
  value,
}) => {
  const handleSelect = (option: Record<string, any>) => {
    try {
      if (onSelect) {
        onSelect(option);
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Command>
      <CommandList>
        <CommandGroup>
          {Array.isArray(data) &&
            data?.length > 0 &&
            data?.map((item, index) => {
              const isSelected = value && value === item?.label;

              return (
                <CommandItem asChild key={index}>
                  <button
                    onClick={() => handleSelect(item)}
                    className={classNames(
                      "cursor-pointer w-full flex flex-col !gap-0.5 items-start text-start hover:bg-primary/10 p-2 rounded-md  active:bg-stone-100 [&:not(:last-child)]:mb-2 overflow-hidden",
                      {
                        "bg-primary/10": isSelected,
                      }
                    )}
                  >
                    <h4 className="capitalize text-sm w-full whitespace-nowrap text-ellipsis overflow-hidden">
                      {item?.label}
                    </h4>
                    {params?.subText?.length && (
                      <p
                        className={classNames(
                          "text-xs text-stone-500 capitalize w-full whitespace-nowrap text-ellipsis overflow-hidden"
                        )}
                      >
                        {params?.subText?.map((prm) => item?.[prm]).join(" > ")}
                      </p>
                    )}
                    {params?.description?.length && (
                      <p className="text-xs text-stone-500 capitalize w-full whitespace-nowrap text-ellipsis overflow-hidden">
                        {params?.description
                          ?.map((prm) => item?.[prm])
                          .join(" > ")}
                      </p>
                    )}
                  </button>
                </CommandItem>
              );
            })}
        </CommandGroup>
      </CommandList>
    </Command>
  );
};

export default Suggestions;
