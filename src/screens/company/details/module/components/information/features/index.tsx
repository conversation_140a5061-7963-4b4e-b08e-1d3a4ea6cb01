import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const featuresData = [
  {
    title: "High-quality standards",
    icon: "/assets/pages/accrordians/1.svg",
  },
  {
    title: "Competitive pricing",
    icon: "/assets/pages/accrordians/2.svg",
  },
  {
    title: "Customized solutions",
    icon: "/assets/pages/accrordians/3.svg",
  },
  {
    title: "Fast delivery",
    icon: "/assets/pages/accrordians/4.svg",
  },
  {
    title: "Exceptional customer",
    icon: "/assets/pages/accrordians/5.svg",
  },
  {
    title: "Sustainable and eco-friendly practices",
    icon: "/assets/pages/accrordians/6.svg",
  },
];

const KeyFeatures = () => {
  return (
    <div className="flex flex-col items-start gap-5 [&>*]:w-full">
      <h3 className="font-bold">Why Choose Us?</h3>

      <Accordion
        type="single"
        collapsible
        className="w-full grid items-start grid-cols-1 sm:grid-cols-2 gap-5"
      >
        {featuresData?.map((fit, index) => {
          return (
            <AccordionItem
              key={index}
              value={fit?.title}
              className="px-6 py-1 bg-white shadow"
            >
              <AccordionTrigger className="w-full !no-underline !py-3">
                <div className="w-max flex-1 flex items-center justify-start gap-5 whitespace-nowrap text-ellipsis overflow-hidden">
                  <div className="flex items-center justify-center rounded-full bg-white shadow w-10 h-10">
                    <img src={fit?.icon} alt="feature-icon" />
                  </div>
                  <span className="flex-1 text-base font-semibold whitespace-nowrap text-ellipsis overflow-hidden max-w-[80%] no-underline">
                    {fit?.title}
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                {`Lorem Ipsum is simply dummy text of the printing and typesetting
                industry. Lorem Ipsum has been the industry's standard dummy
                text ever since the 1500s, when an unknown printer took a galley
                of type and scrambled it to make a type specimen book. It has
                survived not only five centuries, but also the leap into
                electronic typesetting, remaining essentially unchanged. It was
                popularised in the 1960s with the release of Letraset sheets
                containing Lorem Ipsum passages, and more recently with desktop
                publishing software like Aldus PageMaker including versions of
                Lorem Ipsum.rem`}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </div>
  );
};

export default KeyFeatures;
