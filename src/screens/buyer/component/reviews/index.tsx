import React from 'react';
import TestimonialCard from './card';
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem,
  CarouselBullet
} from '@/components/ui/carousel';

const testimonials = [
  {
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
    name: "<PERSON>",
    role: "Designer",
    testimonial: "This is the best design tool I've ever used! This is the best design tool I've ever used! This is the best design tool I've ever used!",
    date: "April 21, 2025"
  },
  {
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
    name: "<PERSON>",
    role: "Developer",
    testimonial: "This is the best design tool I've ever used! This is the best design tool I've ever used! This is the best design tool I've ever used.",
    date: "April 18, 2025"
  },
  {
    avatar: "https://randomuser.me/api/portraits/men/45.jpg",
    name: "<PERSON>",
    role: "Product Manager",
    testimonial: "This is the best design tool I've ever used! This is the best design tool I've ever used! This is the best design tool I've ever used",
    date: "April 15, 2025"
  }
  ,
  // {
  //   avatar: "https://randomuser.me/api/portraits/men/45.jpg",
  //   name: "<PERSON>",
  //   role: "Product Manager",
  //   testimonial: "This is the best design tool I've ever used! This is the best design tool I've ever used! This is the best design tool I've ever used",
  //   date: "April 15, 2025"
  // }
];

const TestimonialsSection = () => {
  return (
    <div className="px-4 py-10 md:px-8 flex justify-start lg:justify-center">
      <div className="w-full lg:max-w-[95%] 2xl:max-w-[90%] mx-auto"> {/* Added 2XL support */}
        {/* Heading (optional) */}
        <h2 className="text-xl md:text-3xl lg:text-[46px] font-bold text-gray-800 mb-6 lg:mb-10">
          What Our Users Say
        </h2>

        {/* Desktop Grid - Hidden on small screens */}
        <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-5">
          {testimonials.map((item, index) => (
            <TestimonialCard
              key={index}
              avatar={item.avatar}
              name={item.name}
              role={item.role}
              testimonial={item.testimonial}
              date={item.date}
            />
          ))}
        </div>

        {/* Mobile Carousel - Visible only on small screens */}
        <div className="block sm:hidden relative -mx-1">
          <Carousel opts={{ align: "start", containScroll: false, dragFree: true }}>
            <CarouselContent className="pl-16">
              {testimonials.map((item, index) => (
                <CarouselItem
                  key={index}
                  className="basis-[80%] pl-4"
                >
                  <TestimonialCard
                    avatar={item.avatar}
                    name={item.name}
                    role={item.role}
                    testimonial={item.testimonial}
                    date={item.date}
                  />
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="flex justify-center gap-2 mt-6">
              {testimonials.map((_, index) => (
                <span 
                  key={index} 
                  className="h-2 w-2 rounded-full bg-gray-300"
                />
              ))}
            </div>
          </Carousel>
        </div>
      </div>
    </div>
  );
};

export default TestimonialsSection;