import image1 from "/assets/pages/portfolio/p_01.png";
import image2 from "/assets/pages/portfolio/p_02.png";
import image3 from "/assets/pages/portfolio/p_03.png";
import image4 from "/assets/pages/portfolio/p_04.png";
import image5 from "/assets/pages/portfolio/p_05.png";
import image6 from "/assets/pages/portfolio/p_06.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import classNames from "classnames";
import useEmblaCarousel from "embla-carousel-react";
import { useEffect, useState } from "react";

const ImagesData = [image1, image2, image3, image4, image5, image6];

// TODO
// Zoom image on zoom click

const ProductGallery = () => {
  const images = ImagesData;
  const [activeIndex, setActiveIndex] = useState(0);
  const [embRef, embApi] = useEmblaCarousel({
    loop: false,
    slidesToScroll: 1,
    skipSnaps: false,
    dragFree: false,
  });

  const [_, previewApi] = useEmblaCarousel({
    loop: false,
    slidesToScroll: 1,
    skipSnaps: false,
    dragFree: true,
  });

  useEffect(() => {
    if (embApi && previewApi) {
      embApi.on("select", () => {
        const selectedIndex = embApi.selectedScrollSnap();
        setActiveIndex(selectedIndex);
        previewApi?.scrollTo(selectedIndex);
      });
    }
  }, [embApi, previewApi]);

  // Enable in case of auto-scroll //
  // useEffect(() => {
  //   const intervalId = setInterval(() => {
  //     const newIndex = (activeIndex + 1) % images.length;
  //     setActiveIndex(newIndex);
  //     previewApi?.scrollTo(newIndex);
  //     embApi?.scrollTo(newIndex);
  //   }, 3000);
  //   return () => clearInterval(intervalId);
  // }, [activeIndex, images.length, embApi, previewApi]);

  const handlePreviewClick = (index: number) => {
    setActiveIndex(index);
    embApi?.scrollTo(index);
  };

  return (
    <div className="slider-container flex flex-row gap-3.5 max-h-[460px]">
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full max-w-max"
        orientation="vertical"
      >
        <CarouselContent className="-mt-4 h-full max-h-[460px]">
          {images.map((img, index) => {
            const isActive = index === activeIndex;
            return (
              <CarouselItem
                key={index}
                className="cursor-pointer pt-1.5 basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/6 mx-1"
              >
                <div
                  key={index}
                  onClick={() => handlePreviewClick(index)}
                  className={classNames(
                    "rounded-md overflow-hidden w-20 h-[5.5rem] p-1 border shadow-sm hover:shadow-box hover:scale-125 transition-all",
                    {
                      grayscale: !isActive,
                      "border-stone-500": isActive,
                      "border-stone-100": !isActive,
                    }
                  )}
                >
                  <img
                    src={img}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover rounded-md"
                  />
                </div>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselPrevious className="top-0 !bg-white hover:shadow-md" />
        <CarouselNext className="bottom-0 !bg-white hover:shadow-md" />
      </Carousel>

      <div className="flex-1 flex justify-center w-full relative">
        <div ref={embRef} className="main-carousel w-full overflow-hidden">
          <div className="flex items-center h-full max-h-[460px]">
            {images.map((image, index) => (
              <div
                key={index}
                className={`carousel-item flex justify-center rounded-sm h-full`}
                style={{
                  flex: "0 0 100%",
                  maxWidth: "100%",
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <img
                  src={image}
                  alt={`Main ${index + 1}`}
                  className="object-cover w-full h-full"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductGallery;
