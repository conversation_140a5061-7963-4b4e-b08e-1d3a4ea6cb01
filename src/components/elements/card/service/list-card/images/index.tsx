import {
  Carousel,
  CarouselBullet,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import type { EmblaPluginType } from "embla-carousel";
import { useEffect, useMemo, useState } from "react";

const ImageGallery = ({ images }: { images: string[] }) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [hovered, setHovered] = useState<boolean>(false);

  useEffect(() => {
    if (!api) {
      return;
    }

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const Plugins = useMemo(() => {
    const plugin = [] as EmblaPluginType[];

    if (hovered) {
      plugin.push(
        Autoplay({
          delay: 2000,
        })
      );
    }
    return plugin;
  }, [hovered]);

  return (
    <div className="relative w-max h-full max-w-24 sm:max-w-56 max-h-56 rounded-base overflow-hidden shadow">
      <Carousel
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        setApi={setApi}
        plugins={Plugins}
        className="w-full max-w-max sm:max-w-xs"
      >
        <CarouselContent>
          {Array.isArray(images) &&
            images?.length > 0 &&
            images?.map((img, index) => (
              <CarouselItem key={index}>
                <div className="flex items-center justify-center w-full h-full aspect-square">
                  <img
                    src={img}
                    alt="img"
                    className="w-full h-full aspect-square object-cover cursor-grab active:cursor-grabbing"
                  />
                </div>
              </CarouselItem>
            ))}
        </CarouselContent>
        <CarouselBullet
          currentSlide={current}
          className="!border-white"
          containerClassName="absolute bottom-4"
        />
      </Carousel>
    </div>
  );
};

export default ImageGallery;
