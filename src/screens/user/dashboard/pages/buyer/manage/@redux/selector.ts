import { RootState } from "@/redux/root-reducer";
import { createSelector } from "reselect";

export const reducer = (state: RootState) => state.user_buyer_rfqdetails;

export const getRFQListSelector = createSelector(
  [reducer],
  (reducer) => reducer.getRFQList 
);

export const getRFQDetailSelector = createSelector(
  [reducer],
  (reducer) => reducer.getRFQDetail
);

export const updateRFQStatusSelector = createSelector(
  [reducer],
  (reducer) => reducer.updateRFQStatus
);

export const updateRFQDetailsSelector = createSelector(
  [reducer],
  (reducer) => reducer.updateRFQDetails
);