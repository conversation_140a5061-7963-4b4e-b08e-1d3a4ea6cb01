"use client";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const Listing = () => {
  const router = useRouter();

  useEffect(() => {
    router.push("/dashboard/buyer/support/faq/general");
  }, [router]);

  return null;
};

const TabsData = [
  {
    title: "General FAQs",
    path: "/dashboard/buyer/support/faq/general",
  },
  {
    title: "Seller FAQs",
    path: "/dashboard/buyer/support/faq/seller",
  },
  {
    title: "Buyer FAQs",
    path: "/dashboard/buyer/support/faq/buyer",
  },
  {
    title: "PlatForm Feature FAQs",
    path: "/dashboard/buyer/support/faq/platform",
  },
  {
    title: "Miscellaneous FAQs",
    path: "/dashboard/buyer/support/faq/miscellaneous",
  },
];

export const ListingTabs = () => {
  const router = useRouter();
  const pathname = usePathname();

  const initialActive = TabsData?.[0]?.path;
  const [active, setActive] = useState<string | undefined>(initialActive);

  useEffect(() => {
    const current = TabsData?.find((item) => pathname?.includes(item?.path));
    setActive(current?.path);
  }, [pathname]);

  return (
    <Tabs
      defaultValue={initialActive}
      value={active}
      className="overflow-auto no-scrollbar bg-white py-1.5 border-b border-stone-300 mb-5"
    >
      <TabsList className="flex-nowrap">
        {TabsData?.map((tab, index) => {
          return (
            <TabsTrigger
              key={index}
              value={tab?.path}
              className="flex-1 uppercase before:data-[state=active]:-bottom-2.5"
              onClick={() => router.push(tab?.path)}
            >
              {tab?.title}
            </TabsTrigger>
          );
        })}
      </TabsList>
    </Tabs>
  );
};

export default Listing;
