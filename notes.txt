API
- access token & refresh token combination.

# Fixes
- src/screens/user/dashboard/pages/buyer/bussiness/@redux/slice.ts use redux helper for builder
- all UI component should be named imported { Component }

website update api
product/service status update api

3 level category for company, 

send same category objects in suggestion api as well seller/meta-template/product/fetch/6854068953576c4b4d6f94c1
category search not working correctly for both multi select & combobox
categories should be called in useEffect for 1st load
  category data should be cleared on unmounting phase
  add reset button for combobox
  error messages should be clear

  in company, industry category & sub category should be object only as it single select 