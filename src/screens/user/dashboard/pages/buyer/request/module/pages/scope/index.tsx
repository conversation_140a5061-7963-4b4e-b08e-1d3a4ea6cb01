'use client';

import { TextEditor } from '@/components/ui/input/texteditor'
import React, { useState } from 'react'

interface RequirementDetails {
  scope?: string;
}

interface RequirementProps {
  onRequirementChange?: (details: RequirementDetails) => void;
  initialDetails?: RequirementDetails;
}

const Index: React.FC<RequirementProps> = ({
  onRequirementChange,
  initialDetails = {
    scope: ""
  }
}) => {
  const [requirementDetails, setRequirementDetails] = useState<RequirementDetails>(initialDetails);

  const handleEditorChange = (e: any) => {
    const updatedDetails = {
      ...requirementDetails,
      scope: e.target?.value || e
    };
    setRequirementDetails(updatedDetails);
    if (onRequirementChange) {
      onRequirementChange(updatedDetails);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">Scope of work?</h1>
      <div className="w-full max-w-md sm:max-w-xl md:max-w-2xl lg:max-w-3xl mx-auto">
        <div className="w-full space-y-4">
          <TextEditor 
            onChange={handleEditorChange}
            value={requirementDetails.scope}
          />
          <div className="text-right text-gray-500 text-xs sm:text-sm">
            (Min 3, Max 100 chars)
          </div>
        </div>
      </div>
    </div>
  );
}

export default Index