'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useRouter } from "next/navigation";
// import ContactNowModal from "../../inquiry";
import ImageGallery from "@/screens/company/listing/module/components/list/list-card/images";
import { FaRegHeart } from "react-icons/fa6";

const ListCard = ({
  company,
  onUnfavourite
}: {
  company: (typeof ListingData)[0];
  onUnfavourite?: () => void;
}) => {
  const router = useRouter();

  const isFavourite = true;

  return (
    <div className="flex-1 w-full h-max bg-white shadow-md hover:shadow-lg">
      <div className="flex flex-col gap-0">
        <div className="flex flex-col sm:flex-row gap-6 px-6 pt-6 pb-4">
          <div className="relative w-full sm:w-max flex items-start justify-between gap-4">
            <div className="relative w-full">
              <ImageGallery images={company?.images} />

              <button
                className={`absolute right-2 top-2 z-10 transition-all h-8 w-8 rounded-full flex items-center justify-center shadow group
        ${isFavourite ? "bg-red-500" : "bg-white hover:bg-red-500"}`}
                onClick={(e) => {
                  e.stopPropagation();
                  if (isFavourite && onUnfavourite) {
                    onUnfavourite();
                  }
                }}
              >
                <FaRegHeart
                  size={16}
                  className={`mt-0.5 transition-colors
          ${isFavourite ? "text-white" : "text-gray-400 group-hover:text-white"}`}
                />
              </button>
            </div>

            <CheckboxMapping className="flex sm:hidden" />
          </div>
          <div className="flex-1 flex flex-col items-start gap-1 [&>*]:w-full">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center justify-start gap-1.5">
                <h2
                  role="button"
                  className="text-xl font-bold hover:underline text-black hover:drop-shadow"
                  onClick={() => router.push("/companies/details")}
                >
                  {company?.name}
                </h2>
                <div className="flex items-center gap-2 sm:hidden ">
                  <img
                    src="/assets/pages/verified-gray.svg"
                    alt="flag"
                    className="aspect-auto w-5 h-auto object-contain"
                  />
                  <span className="flex-1 text-xs text-stone-600">
                    Sponsored
                  </span>
                </div>
              </div>
              <CheckboxMapping className="hidden sm:flex" />
            </div>

            <div className="flex items-center gap-1.5 mt-1 mb-2">
              <img
                src={company?.location?.flag}
                alt="flag"
                className="flex-1 aspect-auto max-w-5 w-5 h-auto object-contain"
              />
              <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 ">
                <span>
                  {company?.location?.address}
                  {", "}
                </span>
                <span>{company?.location?.label}</span>
              </div>
            </div>
            {company?.verified && (
              <div className="flex items-center gap-2 mb-2">
                <div className="hidden sm:flex bg-[#9F9795] text-white px-2 py-1 items-center justify-start gap-1.5 w-max max-w-max rounded-tl-sm rounded-bl-sm rounded-tr-md rounded-br-md">
                  <img
                    src="/assets/pages/verified-white.svg"
                    alt="flag"
                    className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
                  />
                  <span className="flex-1 text-[0.6rem] font-medium">
                    Verified
                  </span>
                </div>
                <span className="text-xs text-stone-600">Sponsored</span>
              </div>
            )}
            <div className="flex flex-col xs:flex-row gap-2 mb-2 w-full flex-wrap sm:flex-nowrap">
              {/* Prices */}
              <div className="flex items-center gap-3 w-full sm:w-auto">
                <span className="font-bold text-[#070707] text-2xl">
                  ${company?.offerPrice}
                </span>
                <span className="font-bold text-[#A2A2A2] text-xl line-through">
                  ${company?.actualPrice}
                </span>
              </div>

              {/* Rating */}
              <div className="flex items-center gap-2.5 w-full sm:w-auto flex-wrap">
                <img
                  src={"/assets/pages/rating-star.svg"}
                  alt="rating star"
                  className="w-5 h-5 object-contain"
                />
                <span className="text-base font-semibold text-font border-r border-slate-300 pr-2.5">
                  {company?.rating?.stars}/5
                </span>
                <span className="text-base font-semibold text-font">
                  {company?.rating?.reviewsCount} Reviews
                </span>
              </div>
            </div>

            {/* Tags */}
            <div
              className="hidden md:flex items-center flex-wrap gap-2 max-w-full md:max-w-[75%] mt-3 mb-2 overflow-hidden"
              style={{
                WebkitBoxOrient: "vertical",
                WebkitLineClamp: 2,
                maxHeight: "calc(2 * (1.3rem + 0.5rem))",
              }}
            >
              {Array.isArray(company?.tags) &&
                company?.tags?.length > 0 &&
                company?.tags?.slice(0, 10)?.map((tag, index) => {
                  return (
                    <button
                      key={index}
                      className="flex items-center justify-center px-3 py-0.5 bg-stone-50 hover:bg-stone-100 border border-stone-300 rounded-full text-stone-800 text-xs"
                    >
                      {tag?.title}
                    </button>
                  );
                })}
            </div>

            {/* Description */}
            <div className="mt-1">
              <p
                title={company?.description}
                className="text-[0.92rem] text-stone-700 line-clamp-2"
              >
                {company?.description}
              </p>
            </div>
          </div>
        </div>

        {/* Card Footer */}
        <div className="flex flex-col sm:flex-row sm:flex-wrap items-center sm:items-center gap-4 sm:gap-0 justify-between border-t-0 sm:border-t border-slate-200 px-6 pt-2.5 pb-6 sm:pb-2.5">
          {/* Info Section */}
          <div className="w-full sm:w-max flex flex-col sm:flex-row items-start sm:items-center justify-start sm:justify-center gap-3 sm:gap-4 bg-stone-50 sm:bg-transparent px-3 sm:px-0 py-4 sm:py-1 text-xs font-light leading-4">
            <p className="flex items-center gap-1 sm:gap-2">
              <span className="font-semibold">{company?.strengthRange}</span>
              <span>Employees</span>
            </p>
            <p className="flex items-center gap-1 sm:gap-2">
              <span className="font-semibold">{company?.businessAge}</span>
              <span>Years in business</span>
            </p>
            <p className="flex items-center gap-1 sm:gap-2">
              <span className="font-semibold">{company?.minOrderQuantity}</span>
              <span>Min Order Value (USD)</span>
            </p>
          </div>

          {/* Button Section */}
          <div className="w-full sm:w-auto flex flex-col sm:flex-row gap-3 sm:gap-3">
            <Button variant="outline" size="sm" className="w-full sm:w-auto">
              Portfolio
            </Button>
            <Button variant="outline" size="sm" className="w-full sm:w-auto">
              Website
            </Button>
            <Button variant="outline" size="sm" className="w-full sm:w-auto">
              Get Quote
            </Button>
          </div>
        </div>

      </div>
    </div>
  );
};

const CheckboxMapping = ({ className }: { className?: string }) => {
  return (
    <Checkbox
      label="Add to Compare"
      labelClassName="whitespace-nowrap"
      containerClassName={classNames("order-1 sm:order-2", className)}
    />
  );
};

export default ListCard;