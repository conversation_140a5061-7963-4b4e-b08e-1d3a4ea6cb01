"use client";

import ProductCard from "@/components/elements/card/product";
import ListingFilter from "@/components/elements/filters/listings/desktop";
import Compare from "@/screens/company/listing/module/components/compare-pop";
import ComparePop from "@/screens/products/module/compare-pop";

const Services = () => {
  return (
    <div className="bg-white">
      <div className="container mx-auto p-4 sm:p-6">
        <ListingFilter />
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
          <ProductCard />
          <ProductCard />
          <ProductCard />
        </div>
        <div className="flex items-center justify-center mt-8 mb-4">
          <span className="flex items-center justify-center gap-2 bg-brown text-white rounded-full px-4 py-2.5 text-sm font-medium whitespace-nowrap max-w-max">
            Loading more...
          </span>
        </div>
      </div>
      <ComparePop/>
    </div>
  );
};

export default Services;