"use client";

import React from "react";
import Card from "./card";
import {
  Users,
  UserCheck,
  Database,
  Layout,
  LightbulbIcon,
  Globe,
  Filter,
} from "lucide-react";
import { useRouter } from "next/navigation";

interface FeatureItem {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  description: string;
  backgroundColor: string;
}

const BusinessFeatures: React.FC = () => {
const router = useRouter();
  const features: FeatureItem[] = [
    {
      icon: <Users size={40} />,
      title: "Expand",
      subtitle: "Your Reach",
      description:
        "Reach thousands of local and international buyers actively searching for your products or services.",
      backgroundColor: "#DFEEFB",
    },
    {
      icon: <UserCheck size={40} />,
      title: "Verified",
      subtitle: "Buyers",
      description:
        "Connect with trusted and verified buyers for seamless transactions.",
      backgroundColor: "#F9D8E0",
    },
    {
      icon: <Database size={40} />,
      title: "Grow",
      subtitle: "Your Sales",
      description:
        "Receive bulk inquiries, generate leads, and boost your business revenue.",
      backgroundColor: "#ECECEC",
    },
    {
      icon: <Users size={40} />,
      title: "Show Your",
      subtitle: "Products/Services",
      description:
        "Create a professional seller profile to display your products, services, certifications, and achievements.",
      backgroundColor: "#E3FDFA",
    },
    {
      icon: <Layout size={40} />,
      title: "Free Business",
      subtitle: "Website",
      description:
        "Get a free, customizable business website to establish your online presence.",
      backgroundColor: "#E3E7FD",
    },
    {
      icon: <LightbulbIcon size={40} />,
      title: "Smart",
      subtitle: "Insights",
      description:
        "Access detailed analytics to track your buyer engagement and inquiries.",
      backgroundColor: "#F5F5DC",
    },
    {
      icon: <Globe size={40} />,
      title: "Global",
      subtitle: "Exposure",
      description:
        "Expand your reach beyond borders and explore export/import opportunities effortlessly.",
      backgroundColor: "#F9EBD8",
    },
    {
      icon: <Filter size={40} />,
      title: "Lead Generation",
      subtitle: "Simplified",
      description:
        "Receive targeted inquiries and bulk requests directly from potential buyers.",
      backgroundColor: "#FBDFF5",
    },
  ];

  return (
    <div className="container xl:ml-24 2xl:ml-32 mx-auto px-4 2xl:max-w-[90%]">
      {" "}
      {/* Added 2XL max-width */}
      {/* Centered Heading Text and Button */}
      <div className="text-center mb-11">
        <h1 className="text-[17px] lg:text-4xl font-bold px-4 max-w-4xl mx-auto">
          Showcase your products, reach verified buyers, & watch your sales
          soar.
        </h1>
        <button
          onClick={() => router.push("/free-listing")}
          className="mt-6 px-8 py-3 bg-[#29100C] text-white rounded-full font-medium hover:bg-gray-800 transition-colors"
        >
          Create Your Seller Profile
        </button>
      </div>
      {/* Features Grid - Using consistent gap sizes */}
      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 2xl:grid-cols-5 gap-4 lg:gap-x-0 lg:gap-y-5 2xl:gap-6">
        {features.map((feature, index) => (
          <Card
            key={index}
            icon={feature.icon}
            title={feature.title}
            subtitle={feature.subtitle}
            description={feature.description}
            backgroundColor={feature.backgroundColor}
          />
        ))}
      </div>
    </div>
  );
};

export default BusinessFeatures;
