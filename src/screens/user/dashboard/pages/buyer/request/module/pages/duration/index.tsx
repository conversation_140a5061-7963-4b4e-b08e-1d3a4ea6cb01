'use client';

import { Input } from "@/components/ui/input/text";
import React, { useState } from 'react';

interface TimeDuration {
  duration: string;
}

interface TimeDurationProps {
  onDurationChange?: (details: TimeDuration) => void;
  initialDetails?: TimeDuration;
}

const TimeDurationInput: React.FC<TimeDurationProps> = ({
  onDurationChange,
  initialDetails = {
    duration: ""
  }
}) => {
  const [durationDetails, setDurationDetails] = useState<TimeDuration>(initialDetails);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const updatedDuration = {
      ...durationDetails,
      [name]: value
    };
    setDurationDetails(updatedDuration);
    if (onDurationChange) {
      onDurationChange(updatedDuration);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col  mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">Enter the estimated time duration needed.</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <Input
            name="duration"
            placeholder="Enter time duration (e.g., 2 hours, 3 days)"
            value={durationDetails.duration}
            onChange={handleInputChange}
          />
        </div>
      </div>
    </div>
  );
};

export default TimeDurationInput;