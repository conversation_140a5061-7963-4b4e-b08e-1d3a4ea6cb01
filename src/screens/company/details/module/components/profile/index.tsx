import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { ListingData } from "@/constants/listing";
import ImageGallery from "./images";
import classNames from "classnames";
import { FC } from "react";

const company = ListingData?.[0];

interface CompanyProfileType {
  hideCompare?: boolean;
}

const CompanyProfile: FC<CompanyProfileType> = ({ hideCompare }) => {
  return (
    <div className="w-full bg-gradient-to-r from-[#FFFCFC] to-[#FFF7F5] pt-8 pb-6">
      <div className="container">
        <div className="flex flex-col sm:flex-row items-start gap-5">
          <div className="w-full sm:w-max flex items-start justify-between gap-4">
            <ImageGallery />
            {/* <CheckboxMapping className="flex sm:hidden" /> */}
          </div>
          <div className="flex-1 flex flex-col gap-5">
            <div className="w-full flex-1 flex flex-col sm:flex-row items-start justify-between gap-4">
              <div className="w-full order-2 sm:order-1 flex items-start flex-col gap-3">
                <div className="flex items-center justify-start gap-1.5">
                  <h2
                    role="button"
                    className="text-xl md:text-3xl font-bold text-black hover:drop-shadow"
                  >
                    {company?.name}
                  </h2>
                  <img
                    src={VERIFIES_GRAY}
                    alt="flag"
                    className="block sm:hidden flex-1 aspect-auto w-5 h-auto object-contain"
                  />
                </div>

                {/* Location */}
                <div className="flex items-center gap-1.5 mt-1">
                  <img
                    src={company?.location?.flag}
                    alt="flag"
                    className="flex-1 aspect-auto max-w-6 w-6 h-auto object-contain"
                  />
                  <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 ">
                    <span>
                      {company?.location?.address}
                      {", "}
                    </span>
                    <span>{company?.location?.label}</span>
                  </div>
                </div>

                {/* Reviews */}
                <div className="flex items-center gap-2.5">
                  <img
                    src={"/assets/pages/rating-star.svg"}
                    alt="flag"
                    className="aspect-auto w-5 h-5 object-contain"
                  />
                  <div className="flex item flex-wrap gap-2.5 [&>*]:text-sm [&>*]:font-medium [&>*]:text-font [&>*]:leading-4">
                    <span className="border-r border-slate-300 pr-2.5">
                      {company?.rating?.stars}/5
                    </span>
                    <span>{company?.rating?.reviewsCount} Reviews</span>
                  </div>
                </div>

                {/* Verified */}
                {company?.verified && (
                  <div className="hidden sm:flex my-1 bg-[#9F9795] text-white px-2 py-1.5  items-center justify-start gap-1.5 w-max max-w-max rounded-tl-sm rounded-bl-sm rounded-tr-md rounded-br-md">
                    <img
                      src={"/assets/pages/verified-white.svg"}
                      alt="flag"
                      className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
                    />
                    <span className="flex-1 text-xs font-medium">Verified</span>
                  </div>
                )}
                {/* short intro  */}
                <div className="text-sm text-stone-600">
                  <p style={{ color: "#29100C" }}>{company.shortIntro}</p>
                </div>
              </div>
              {!hideCompare && <CheckboxMapping className="flex sm:hidden" />}
            </div>

            <div className="w-full flex items-center justify-end">
              <div className="flex items-center flex-wrap gap-3">
                <Button variant="ghost-brown" className="font-medium px-6">
                  Contact Us
                </Button>
                <Button
                  variant="ghost-brown-revert"
                  className="font-medium px-6"
                >
                  Visit Website
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const CheckboxMapping = ({ className }: { className?: string }) => {
  return (
    <Checkbox
      label="Add to Compare"
      labelClassName="whitespace-nowrap"
      containerClassName={classNames("order-1 sm:order-2", className)}
    />
  );
};

export default CompanyProfile;
