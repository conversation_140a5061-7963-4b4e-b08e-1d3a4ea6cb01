"use client";

import { Badge } from "@/components/ui/badge";
import InputLabel from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import {
  paymentMethods,
  workShifts,
} from "@/constants/user/seller/company/update";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import { serviceDetails } from "../../../@redux/selectors";

const Pricing = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(serviceDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values[e?.target?.name])
          ? Array.from(values[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setServiceDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="min-w-min flex flex-col text-start gap-2">
        <div className="flex items-center justify-start gap-1.5">
          <InputLabel>Currency & Price</InputLabel>
          <Badge
            className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
            variant="destructive"
          >
            Required
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Input
          type="text"
          name="min_price"
          label="Min Price"
          onChange={handleChange}
          value={values?.min_price}
          placeholder="Min Price"
          containerClassName="flex-1"
        />
        <Input
          type="text"
          name="max_price"
          label="Max Price"
          onChange={handleChange}
          value={values?.max_price}
          placeholder="Enter Price"
          containerClassName="flex-1"
        />
        <MultiSelect
          onValueChange={handleChange}
          defaultValue={values?.billing_methods}
          value={values?.billing_methods}
          name="billing_methods"
          label="Billing Method"
          placeholder="Select Method"
          variant="inverted"
          maxCount={3}
          options={paymentMethods}
        />
        <Input
          type="text"
          label="Minimum Order Value"
          placeholder="Enter Value"
          name="min_order_value"
          value={values?.min_order_value}
          onChange={handleChange}
        />
        <Select
          name="availability"
          label="Availability Schedule"
          onChange={handleChange}
          value={values?.availability}
          placeholder="Select Availability"
          options={[
            { label: "Monday to Friday", value: "mon-fri" },
            { label: "Monday to Saturday", value: "mon-sat" },
            { label: "Monday to Sunday", value: "mon-sun" },
          ]}
        />
        <Select
          name="work_hour"
          label="Operating hours"
          onChange={handleChange}
          defaultValue={values?.work_hour}
          value={values?.work_hour}
          placeholder="Select Hours"
          options={workShifts}
        />
        <MultiSelect
          onValueChange={handleChange}
          name="booking_options"
          defaultValue={values?.booking_options}
          value={values?.booking_options}
          label="Booking Option"
          placeholder="Select type"
          variant="inverted"
          maxCount={3}
          options={[
            { label: "Online", value: "online" },
            { label: "Offline", value: "offline" },
          ]}
        />
        <MultiSelect
          onValueChange={handleChange}
          name="payment_terms"
          defaultValue={values?.payment_terms}
          value={values?.payment_terms}
          label="Payment Terms"
          placeholder="Select methods"
          variant="inverted"
          maxCount={3}
          options={paymentMethods}
        />
      </div>
    </>
  );
};

export default Pricing;
