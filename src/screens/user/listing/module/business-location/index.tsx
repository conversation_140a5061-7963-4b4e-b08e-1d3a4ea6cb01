import { Tags } from "@/components/ui/tags";
import { useState } from "react";

const BusinessLocation = () => {
  const [tagInputValue, setTagInputValue] = useState<string>("");
  const [tags, setTags] = useState<any[]>([]);

  const handleAdd = () => {
    try {
      if (!tagInputValue) return;

      setTags((prev) => {
        const current = Array?.from(prev);
        current.push({ title: tagInputValue });
        return current;
      });
      setTagInputValue("");
    } catch (error) {
      console.error(error);
    }
  };

  const handleDelete = (tag: any) => {
    try {
      setTags((prev) => {
        let current = Array?.from(prev);
        current = current?.filter((item) => item?.title !== tag?.title);
        return current;
      });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Tags
      data={tags}
      onDelete={(tag) => handleDelete(tag)}
      input={{
        type: "text",
        name: "businessLocation",
        label: "Business Location",
        onChange: (e) => setTagInputValue(e?.target.value),
        onActionClick: () => handleAdd(),
        value: tagInputValue,
        placeholder: "Enter your Business Location",
        wrapperClassName: "!rounded-full",
      }}
    />
  );
};

export default BusinessLocation;
