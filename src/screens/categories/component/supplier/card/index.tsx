import React from 'react';
import { Star } from 'lucide-react';

interface ImageType {
  src: string;
  alt: string;
}

interface ProductCardProps {
  name: string;
  logoLine1: string;
  logoLine2: string;
  rating: string | number;
  reviewCount: number;
  isVerified: boolean;
  images: ImageType[];
}

export default function ProductCard({
  name,
  logoLine1,
  logoLine2,
  rating,
  reviewCount,
  isVerified,
  images
}: ProductCardProps) {
  return (
    <div className="p-4 rounded-lg border shadow-lg w-full">
      <div className="flex items-start space-x-4 mb-4">

        <div className="bg-gray-800 rounded-lg p-2 w-12 h-12 flex items-center justify-center">
          <div className="text-xs text-white">
            <div className="text-green-400 font-semibold text-xs">{logoLine1}</div>
            <div className="text-white text-xs">{logoLine2}</div>
          </div>
        </div>

        <div className="flex-1">
          <h3 className="font-bold text-lg">{name}</h3>

          <div className="flex items-center mt-1">
            <div className="flex items-center">
              <div className="bg-red-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center">
                <Star size={12} fill="white" stroke="white" />
              </div>
              <span className="ml-1 font-medium">{rating}/5</span>
            </div>

            <span className="text-gray-600 ml-2">{reviewCount} Reviews</span>
          </div>

          {isVerified && (
            <div className="flex items-center gap-2 mt-2">
              <div className="bg-gray-900 text-white px-2 py-1 flex items-center justify-start gap-1.5 w-max rounded-md">
                <img
                  src={"/assets/pages/verified-white.svg"}
                  alt="verified"
                  className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
                />
                <span className="flex-1 text-xs font-medium">
                  Verified
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-4 gap-2 mt-4">
        {images.map((image: ImageType, index: number) => (
          <div key={index} className="overflow-hidden rounded-lg">
            <img src={image.src} alt={image.alt} className="w-full h-full object-cover" />
          </div>
        ))}
      </div>
    </div>
  );
}