'use client';

import { MultiSelect } from "@/components/ui/input/multi-select";
import React, { useState } from 'react';

interface ProviderProps {
  onProviderChange?: (details: ProviderDetails) => void;
  initialDetails?: ProviderDetails;
}

interface ProviderDetails {
  providerTypes: string[];
}

const Provider: React.FC<ProviderProps> = ({
  onProviderChange,
  initialDetails = {
    providerTypes: []
  }
}) => {
  const [providerDetails, setProviderDetails] = useState<ProviderDetails>(initialDetails);
  
  const providerOptions = [
    { label: "Manufacturer", value: "manufacturer" },
    { label: "Wholesaler", value: "wholesaler" },
    { label: "Distributor", value: "distributor" },
    { label: "Any", value: "any" }
  ];

  const handleProviderTypeChange = (event: any) => {
    const selectedProviderTypes = event.target.value;
    const updatedDetails = {
      ...providerDetails,
      providerTypes: selectedProviderTypes
    };
    setProviderDetails(updatedDetails);
    if (onProviderChange) {
      onProviderChange(updatedDetails);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">Select service provider</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <MultiSelect
            name="providerTypes"
            options={providerOptions}
            placeholder="Select provider types"
            onValueChange={handleProviderTypeChange}
            defaultValue={providerDetails.providerTypes}
          />
        </div>
      </div>
    </div>
  );
};

export default Provider;