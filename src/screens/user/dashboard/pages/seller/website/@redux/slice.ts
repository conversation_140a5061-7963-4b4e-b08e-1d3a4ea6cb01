import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { WebsiteUpdateType } from "../module/types";
import { thunks } from "./thunk";

interface APTStateType {
  websiteDetails: APIResponseType;
}

const APIState: APTStateType = {
  websiteDetails: {
    data: null,
    loading: true,
    meta: null,
  },
};

const initialState = {
  ...APIState,
} satisfies APTStateType as APTStateType;

const slice = createSlice({
  name: "user_seller_website",
  initialState,
  reducers: {
    setWebsiteDetails: (
      state,
      action: PayloadAction<WebsiteUpdateType | null>
    ) => {
      if (action?.payload) {
        state.websiteDetails.data = {
          ...(state.websiteDetails.data || {}),
          ...action.payload,
        };
      } else {
        state.websiteDetails = { ...initialState.websiteDetails };
      }
    },
  },
  extraReducers: (builder) => {
    Object.entries(thunks).forEach(([_, thunk]) => {
      createThunkCase(builder, thunk, APIState);
    });
  },
});

export const Actions = slice.actions;
export default slice.reducer;
