"use client";

import ReviewsStars from "@/components/elements/icon/reviews-stars";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { TextEditor } from "@/components/ui/input/texteditor";
import { useState } from "react";

const DashboardReviewsCard = () => {
  const [showEditor, setShowEditor] = useState<boolean>(false);

  const title = "Wasim Khan";
  const rating = 4;
  const time = "5 days ago";

  return (
    <div className="bg-white shadow-md p-6 w-full">
      <div className="flex items-start gap-4">
        <Avatar>
          <AvatarImage src="https://github.com/shadcn.png" alt="user" />
          <AvatarFallback>CN</AvatarFallback>
        </Avatar>

        <div className="flex flex-col items-start gap-2 [&>*]:w-full">
          <h3 className="mt-2 text-base font-medium capitalize">{title}</h3>

          <div className="flex items-center gap-3">
            <ReviewsStars rating={rating} />
            <span className="text-xs text-font/90">{time}</span>
          </div>

          <p className="text-sm text-font/95 mt-4">
            {`Lorem Ipsum is simply dummy text of the printing and typesetting
            industry. Lorem Ipsum has been the industry's standard dummy text
            ever since the 1500s, when an unknown printer took a galley of type
            and scrambled it to make a type specimen book. It has survived not
            only five centuries, but also the leap into electronic typesetting,
            remaining essentially unchanged. It was popularised in the 1960s
            with the release of Letraset sheets containing Lorem Ipsum passages,
            and more recently with desktop publishing software like Aldus
            PageMaker including versions of Lorem Ipsum.rem`}
          </p>

          {showEditor && (
            <div className="w-full max-w-[75%] me-auto mt-6">
              <TextEditor
                name="detailed_business_overview"
                // value={values?.detailed_business_overview}
                // onChange={handleChange}
                containerClassName="flex-1"
                className="flex-1 rounded-md"
              />
            </div>
          )}

          <div className="flex items-center gap-4 mt-4">
            {showEditor ? (
              <>
                <Button variant="main" onClick={() => setShowEditor(false)}>
                  Submit
                </Button>
                <Button variant="outline" onClick={() => setShowEditor(false)}>
                  Cancel
                </Button>
              </>
            ) : (
              <Button
                variant="main"
                className="!px-6"
                onClick={() => setShowEditor(true)}
              >
                Reply
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardReviewsCard;
