'use client';

import { Dropzone } from '@/components/ui/input/dropzone'
import React from 'react'

const FileUpload = () => {
  return (
    <div className="flex flex-col items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 sm:mb-4 text-center">Upload Your Files</h1>
      <p className="text-sm sm:text-base text-center mb-4 sm:mb-6 px-2 sm:px-4">Drag and drop files here or click to browse your device.</p>
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md">
        <Dropzone />
        <div className="text-right text-gray-500 text-xs sm:text-sm mt-1">(Max file size: 10MB)</div>
      </div>
    </div>
  );
}

export default FileUpload;