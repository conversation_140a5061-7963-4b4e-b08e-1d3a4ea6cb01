"use client";

import { FC, MouseEvent, ReactNode, useEffect, useState } from "react";

import { Context } from ".";

interface ContextTypes {
  children?: ReactNode;
  hover?: boolean;
  open?: boolean;
  onToggle?: (data?: any) => void;
}

const DropdownProvider: FC<ContextTypes> = ({
  children,
  hover,
  open,
  onToggle,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);

  useEffect(() => {
    if (open !== undefined) {
      setDropdownOpen(Boolean(open));
    }
  }, [open]);


  const handleCallback = (val: boolean) => {
    try {
      if (onToggle) {
        onToggle(val);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const toggleDropdown = (val: boolean) => {
    try {
      setDropdownOpen(val);

      if (hover) {
        setTimeout(() => {
          setDropdownOpen(true);
        }, 300);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleDropdown = (e: MouseEvent<HTMLDivElement>) => {
    try {
      e?.preventDefault();
      e?.stopPropagation();
      
      if (!hover && open === undefined) {
        setDropdownOpen(!dropdownOpen);
        handleCallback(!dropdownOpen);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleClose = () => {
    try {
      if (!hover) {
        setDropdownOpen(false);

        if (onToggle) {
          setTimeout(() => {
            handleCallback(false);
          });
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const valueGroup = {
    open: dropdownOpen,
    handleDropdown,
    handleClose,
    setDropdownOpen,
    toggleDropdown,
  };

  return (
    <Context.Provider value={{ ...valueGroup }}>{children}</Context.Provider>
  );
};

export default DropdownProvider;
