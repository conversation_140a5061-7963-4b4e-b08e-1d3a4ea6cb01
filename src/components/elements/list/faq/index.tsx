"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  AccordionContentProps,
  AccordionItemProps,
  AccordionMultipleProps,
  AccordionSingleProps,
  AccordionTriggerProps,
} from "@radix-ui/react-accordion";
import { ComponentRef, forwardRef, ReactNode } from "react";

interface FAQItem {
  title: string;
  description: string | ReactNode;
}

interface CommonFAQProps {
  data: FAQItem[];
  itemProps?: AccordionItemProps;
  triggerProps?: AccordionTriggerProps;
  contentProps?: AccordionContentProps;
}

type FrequentlyAskedQuestionsProps = CommonFAQProps &
  (AccordionSingleProps | AccordionMultipleProps);

const FrequentlyAskedQuestions = forwardRef<
  ComponentRef<typeof Accordion>,
  FrequentlyAskedQuestionsProps
>(({ data, itemProps, triggerProps, contentProps, ...rest }, ref) => {
  // Add debugging and validation
  console.log("FAQ Component - Received data:", data);
  
  // Validate data
  if (!data || !Array.isArray(data) || data.length === 0) {
    console.warn("FAQ Component - No valid data provided");
    return (
      <div className="w-full p-4 text-center text-gray-500">
        No frequently asked questions available.
      </div>
    );
  }

  return (
    <Accordion ref={ref} className="w-full" {...rest}>
      {data.map((faq, index) => {
        // Validate individual FAQ item
        if (!faq || typeof faq.title !== 'string' || !faq.description) {
          console.warn(`FAQ Component - Invalid FAQ item at index ${index}:`, faq);
          return null;
        }

        return (
          <AccordionItem
            key={`faq-${index}-${faq.title}`}
            value={`faq-${index}`}
            className="px-6"
            {...itemProps}
          >
            <AccordionTrigger {...triggerProps}>{faq.title}</AccordionTrigger>
            <AccordionContent {...contentProps}>
              {faq.description}
            </AccordionContent>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
});

FrequentlyAskedQuestions.displayName = "FrequentlyAskedQuestions";

export default FrequentlyAskedQuestions;