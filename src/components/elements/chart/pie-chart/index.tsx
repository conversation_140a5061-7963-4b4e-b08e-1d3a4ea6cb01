"use client";

import Chart, { chartColors } from "@/components/ui/chart";
import { ChartData, ChartOptions } from "chart.js/auto";
import { useMemo } from "react";
import colors from "tailwindcss/colors";

interface MainProps extends React.ComponentPropsWithoutRef<"canvas"> {
  width?: number | "auto";
  height?: number | "auto";
}

const PieChart = ({
  width = "auto",
  height = "auto",
  className = "",
}: MainProps) => {
  const props = {
    width: width,
    height: height,
    className: className,
  };
  const colorScheme = "default";
  const darkMode = false;

  const chartData = useMemo(() => {
    return [15, 10, 65];
  }, []);

  const data: ChartData = useMemo(() => {
    return {
      labels: ["Yellow", "Dark"],
      datasets: [
        {
          data: chartData,
          backgroundColor: colorScheme ? chartColors() : "",
          hoverBackgroundColor: colorScheme ? chartColors() : "",
          borderWidth: 5,
          borderColor: darkMode ? colors.stone[700] : colors.white,
        },
      ],
    };
  }, [chartData, darkMode]);

  const options: ChartOptions = useMemo(() => {
    return {
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
      },
    };
  }, []);

  return (
    <Chart
      type="pie"
      width={props.width}
      height={props.height}
      data={data}
      options={options}
      className={props.className}
    />
  );
};

export default PieChart;
