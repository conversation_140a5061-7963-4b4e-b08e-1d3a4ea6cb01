"use client";

import { useRouter } from "next/navigation";

const FooterCopyright = () => {
const router = useRouter();

  return (
    <div className="hidden sm:flex items-center">
      <p className="text-sm font-normal">
        © {new Date().getFullYear()}
        <span
          role="button"
          className="text-white hover:drop-shadow ms-1.5 hover:underline"
          onClick={() => router.push("/")}
        >
          Seekler.net
        </span>
      </p>
    </div>
  );
};

export default FooterCopyright;
