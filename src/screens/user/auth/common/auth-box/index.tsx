"use client";

import MAIN_LOGO from "/assets/branding/logo.svg";
import { useDOMOperator } from "@/lib/hooks/useDOMOperator";
import classNames from "classnames";
import { ReactNode } from "react";
import { useRouter } from "next/navigation";

const AuthBox = ({
  children,
  title,
  subText,
  contentClassName,
  className,
}: {
  children: ReactNode;
  className?: string;
  title?: string;
  subText?: string;
  contentClassName?: string;
}) => {
const router = useRouter();

  useDOMOperator({
    selectors: ["#global-header", "#global-footer"],
    operation: "hide",
  });

  return (
    <main
      className={classNames(
        "bg-white md:bg-gradient-to-r from-[#FFF0ED] to-[#FFF0ED] w-screen h-screen overflow-auto flex flex-col [&>*]:w-full",
        className
      )}
    >
      <header className="w-full h-max py-6 px-6">
        <button onClick={() => router.push("/")}>
          <img
            src={MAIN_LOGO}
            alt="Aalyana"
            className="w-full max-w-28 object-contain mb-1"
          />
        </button>
      </header>
      <div className="w-full h-full mt-0 md:mt-8">
        <div
          className={classNames(
            "relative bg-white md:shadow-lg px-10 pt-1 pb-6 md:py-10 rounded-md max-w-xl mx-auto",
            contentClassName
          )}
        >
          <div className="w-full flex flex-col items-center justify-start gap-3 text-center mb-10 md:max-w-[90%] mx-auto">
            {/* <img
              src={MAIN_LOGO}
              alt="Aalyana"
              className="w-full max-w-28 object-contain mb-1 hidden md:block"
            /> */}
            {title && (
              <h3 className="text-2xl md:text-3xl font-bold">{title}</h3>
            )}
            {subText && <p className="text-center">{subText}</p>}
          </div>
          {children}
        </div>
      </div>
    </main>
  );
};

export default AuthBox;
