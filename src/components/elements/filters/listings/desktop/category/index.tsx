"use client";

import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { DropdownOptions } from "@/constants/listing";
import { useState } from "react";
import { FaCaretDown } from "react-icons/fa";

const CategoryFilter = () => {
  const [selected, setSelected] = useState<any[]>([]);

  return (
    <div className="flex items-center flex-wrap gap-2">
      <Dropdown
        content={
          <DropdownList
            data={DropdownOptions(selected, setSelected)}
            className="w-full max-w-sm sm:min-w-md md:min-w-80 border-0"
            search
            checkbox
          />
        }
        contentClassName="z-20"
      >
        <button className="text-xs py-1 px-4 hover:bg-slate-50 transition-all flex items-center justify-center gap-2 border border-stone-500 rounded-md h-input">
          <span className="text-sm font-medium">Category</span>
          <FaCaretDown />
        </button>
      </Dropdown>
    </div>
  );
};

export default CategoryFilter;
