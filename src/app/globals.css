@import "tailwindcss";

@theme {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --background: #ffffff;
  --foreground: #171717;
  --color-main: #ed4225;
  --color-primary: #ed4225;
  --color-secondary: #031c33;
  --color-success: lime;
  --color-info: cyan;
  --color-warning: #ffb922;
  --color-pending: brown;
  --color-danger: #850000;
  --color-light: #d2e1f1;
  --color-dark: #031c33;
  --color-font: #070707;
  --color-brown: #29100c;

  --spacing-headerCut: "calc(100vh - 102px)";
  --spacing-input: "2.25rem";
  --spacing-header: "110px";
  --spacing-header-md: "97px";
  --spacing-header-sm: "80.06px";
  
  --radius-base: "0.1rem";

  --shadow-box: "0px -3px 16px -2px rgba(0, 0, 0, 0.1)";
  --shadow-box-sm: "0px 0px 8px -2px rgba(0, 0, 0, 0.1)";
  --shadow-box-xs: "0px 0px 4px -2px rgba(0, 0, 0, 0.1)";

  --animate-accordion-down: "accordion-down 0.2s ease-out";
  --animate-accordion-up: "accordion-up 0.2s ease-out";
  --animate-caret-blink: "caret-blink 1.25s ease-out infinite";
  --animate-swing: "swing 2s ease-out infinite";
  --animate-blink: "blink 1s linear infinite";

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes caret-blink {
    0%,
    70%,
    100% {
      opacity: 1;
    }
    20%,
    50% {
      opacity: 0;
    }
  }

  @keyframes swing {
    20% {
      transform: rotate3d(0, 0, 1, 15deg);
    }
    40% {
      transform: rotate3d(0, 0, 1, -10deg);
    }
    60% {
      transform: rotate3d(0, 0, 1, 5deg);
    }
    80% {
      transform: rotate3d(0, 0, 1, -5deg);
    }
    100% {
      transform: rotate3d(0, 0, 1, 0deg);
    }
  }

  @keyframes blink {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 1rem;

  @media (width >= theme(--breakpoint-sm)) {
    padding-inline: 2rem;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100%;
  font-family: var(--font-roboto), sans-serif;
  background-color: var(--background);
  color: var(--color-font);
  line-height: 1.5;
}

/* Buttons and links */
a {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

button {
  cursor: pointer;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Forms */
input,
select,
textarea {
  outline: none;
}

/* Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  margin: 0;
}

/* Accessible outlines */
:focus-visible {
  outline: 1px solid transparent;
  outline-offset: 1px;
}

/* Remove tap highlight on mobile */
button,
a,
input,
textarea,
select {
  -webkit-tap-highlight-color: transparent;
}
