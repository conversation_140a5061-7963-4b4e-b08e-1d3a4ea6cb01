import RATING_STAR from "/assets/pages/rating-star.svg";
import { Button } from "@/components/ui/button";

const ServiceCard = () => {
  return (
    <div
      role="button"
      className="bg-white rounded-2xl shadow-md hover:shadow-lg h-full flex flex-col overflow-hidden"
    >
      <div className="relative overflow-hidden w-full h-48 select-none">
        <img
          src="https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg"
          alt="card-image"
          className="w-full h-full object-cover"
        />
      </div>
      <div className="flex flex-col gap-4 [&>*]:w-full p-4">
        <h5 className="text-base font-bold leading-5">
          Wholesale Borosilicate Heat Resistant Double Wall Glass
        </h5>

        <div className="flex items-center gap-2.5">
          <img
            src={RATING_STAR}
            alt="flag"
            className="aspect-auto w-5 h-5 object-contain"
          />
          <div className="flex item flex-wrap gap-2.5 [&>*]:text-xs [&>*]:font-semibold [&>*]:text-font [&>*]:leading-4">
            <span className="border-r border-slate-300 pr-2.5">4.5/5</span>
            <span>150 Reviews</span>
          </div>
        </div>

        <div className="flex flex-col items-start gap-3 text-xs font-light [&>span]:border-l mt-1 [&>span]:border-slate-300 [&>span]:pl-3">
          <span className="text-stone-800">24/7 Availability</span>
          <span className="text-stone-800">On-Site Service</span>
        </div>

        <Button className="mt-2" variant="main">
          Get Quote
        </Button>
      </div>
    </div>
  );
};

export default ServiceCard;
