import inquiry_received from "/assets/pages/user/dashboard/insights/inquiry_received.svg";
import profile_views from "/assets/pages/user/dashboard/insights/profile_views.svg";
import total_review from "/assets/pages/user/dashboard/insights/total_review.svg";
import StackCard from "@/components/elements/card/stack";
import classNames from "classnames";
import { FC } from "react";

const Data = [
  {
    icon: profile_views,
    title: "Profile Views",
    value: "46443",
  },
  {
    icon: inquiry_received,
    title: "Inquiry received",
    value: "46464",
  },
  {
    icon: inquiry_received,
    title: "Response Rate",
    value: "46464",
  },
  {
    icon: total_review,
    title: "Total Review",
    value: "46464",
  },
];

interface OverviewType {
  className?: string;
}

const Overview: FC<OverviewType> = ({ className }) => {
  return (
    <div
      className={classNames(
        "lg:bg-white shadow-box grid grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-0",
        className
      )}
    >
      {Data?.map((msr, index) => {
        return (
          <StackCard
            key={index}
            icon={msr?.icon}
            className="!shadow-box lg:!shadow-none lg:border-r last-of-type:border-0 border-stone-200"
          >
            <div className="flex flex-col gap-1">
              <span className="text-sm text-stone-500">{msr?.title}</span>
              <h4 className="text-base font-bold">{msr?.value}</h4>
            </div>
          </StackCard>
        );
      })}
    </div>
  );
};

export default Overview;
