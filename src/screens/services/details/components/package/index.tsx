"use client"; 

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowDown, ArrowUp } from "lucide-react";

// Define the type for a package
type PackageType = {
  name: string;
  currentPrice: string;
  originalPrice: string;
  description: string;
  tools: string;
  materials: string;
  duration: string;
  afterSale: string;
  warranty: string;
  rating: string;
  serviceType: string;
  total: string;
  [key: string]: string; // Add index signature to allow string indexing
};

// Define the type for a row
type RowType = {
  label: string;
  key: keyof PackageType; // Use keyof to ensure key is a valid property of PackageType
  includePrice?: boolean;
  isRating?: boolean;
  isBold?: boolean;
};

const PackageComparisonTable = () => {
  // Track expanded state for each package in mobile view
  const [expandedPackages, setExpandedPackages] = useState<{
    [key: number]: boolean;
  }>({});

  const togglePackageExpand = (index: number) => {
    setExpandedPackages((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const packages: PackageType[] = [
    {
      name: "Basic",
      currentPrice: "$181.99",
      originalPrice: "$271.99",
      description:
        "Lorem ipsum dolor sit amet, consetetur pscing elitr, sed diam nonumy eirmod tempor",
      tools: "High-Powered Vacuum Cleaners",
      materials: "Non-Toxic",
      duration: "2 – 4 Hours",
      afterSale: "Chargeable",
      warranty: "7-Day Service Guarantee",
      rating: "4.5/5",
      serviceType: "Home Services, Cleaning Deep Cleaning",
      total: "$181.99",
    },
    {
      name: "Standard",
      currentPrice: "$381.99",
      originalPrice: "$371.99",
      description:
        "Lorem ipsum dolor sit amet, consetetur pscing elitr, sed diam nonumy eirmod tempor",
      tools: "High-Powered Vacuum Cleaners, eco-friendly",
      materials: "Non-Toxic, biodegradable",
      duration: "4 – 6 Hours",
      afterSale: "Chargeable",
      warranty: "7-Day Service Guarantee",
      rating: "4.5/5",
      serviceType: "Home Services, Cleaning Deep Cleaning",
      total: "$381.99",
    },
    {
      name: "Premium",
      currentPrice: "$381.99",
      originalPrice: "$371.99",
      description:
        "Lorem ipsum dolor sit amet, consetetur pscing elitr, sed diam nonumy eirmod tempor",
      tools: "High-Powered Vacuum Cleaners, eco-friendly",
      materials: "Non-Toxic, biodegradable cleaning agents",
      duration: "2 – 4 Hours",
      afterSale: "Chargeable",
      warranty: "7-Day Service Guarantee",
      rating: "4.5/5",
      serviceType: "Home Services, Cleaning Deep Cleaning",
      total: "$381.99",
    },
  ];

  const rows: RowType[] = [
    { label: "Package", key: "name", includePrice: true },
    { label: "Tools & Equipment", key: "tools" },
    { label: "Materials/Ingredients", key: "materials" },
    { label: "Service Duration", key: "duration" },
    { label: "After-Sale Service", key: "afterSale" },
    { label: "Warranty", key: "warranty" },
    { label: "Rating", key: "rating", isRating: true },
    { label: "Service Type", key: "serviceType" },
    { label: "Total", key: "total", isBold: true },
  ];

  // Desktop view
  const DesktopView = () => (
    <table className="w-full border-collapse hidden md:table">
      <tbody>
        {rows.map((row, rowIndex) => (
          <tr
            key={rowIndex}
            className={`border-b border-gray-200 ${
              rowIndex % 2 === 0 ? "bg-white" : "bg-[#FAFAFA]"
            }`}
          >
            <td className="py-4 px-4 font-medium border-r border-gray-200 align-top w-1/4">
              {row.label}
            </td>

            {packages.map((pkg, pkgIndex) => (
              <td
                key={pkgIndex}
                className="py-4 px-2 border-r border-gray-200 align-top text-sm"
              >
                {row.includePrice ? (
                  <div>
                    <div className="text-xl font-bold">{pkg.currentPrice}</div>
                    <div className="text-gray-500 line-through">
                      {pkg.originalPrice}
                    </div>
                    <div className="mt-2 text-lg ">{pkg[row.key]}</div>
                    <p className="mt-2 text-xs text-gray-600">
                      {pkg.description}
                    </p>
                  </div>
                ) : row.isRating ? (
                  <div className="flex items-center">
                    <img
                      src={"/assets/pages/rating-star.svg"}
                      alt="Rating Star"
                      className="w-5 h-5"
                    />
                    <span className="ml-1">{pkg[row.key]}</span>
                  </div>
                ) : row.isBold ? (
                  <div className="font-bold text-xl">{pkg[row.key]}</div>
                ) : (
                  <div className="text-xs">{pkg[row.key]}</div>
                )}

                {row.key === "total" && (
                  <div className="mt-4">
                    <Button
                      variant="ghost-brown"
                      className="rounded-full px-8 py-2"
                    >
                      Select
                    </Button>
                  </div>
                )}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );

  // Mobile view
  const MobileView = () => (
    <div className="md:hidden">
      {packages.map((pkg, index) => (
        <div key={index} className="bg-[#FAFAFA] mb-4 p-5 rounded-lg">
          {/* Package Name, Price, and Description section */}
          <div>
            <div className="flex justify-between items-center">
              <div className="text-lg font-medium">{pkg.name}</div>
              <div className="flex items-center">
                <span className="text-xs text-gray-500 line-through mr-2">
                  {pkg.originalPrice}
                </span>
                <span className="text-base font-bold">{pkg.currentPrice}</span>
              </div>
            </div>
            <div className="text-sm mt-2">{pkg.description}</div>
          </div>

          {/* Expanded content section */}
          {expandedPackages[index] ? (
            <>
              <div className="mt-3">
                <div className="text-sm font-medium">Tools & Equipment</div>
                <div className="text-xs">{pkg.tools}</div>
              </div>

              <div className="mt-3">
                <div className="text-sm font-medium">Materials/Ingredients</div>
                <div className="text-xs">{pkg.materials}</div>
              </div>

              <div className="mt-3">
                <div className="text-sm font-medium">Service Duration</div>
                <div className="text-xs">{pkg.duration}</div>
              </div>

              <div className="mt-3">
                <div className="text-sm font-medium">After-Sale Service</div>
                <div className="text-xs">{pkg.afterSale}</div>
              </div>

              <div className="mt-3">
                <div className="text-sm font-medium">Warranty</div>
                <div className="text-xs">{pkg.warranty}</div>
              </div>

              <div className="mt-3">
                <div className="text-sm font-medium">Rating</div>
                <div className="flex items-center">
                  <img
                    src={"/assets/pages/rating-star.svg"}
                    alt="Rating Star"
                    className="w-4 h-4"
                  />
                  <span className="ml-1 text-xs">{pkg.rating}</span>
                </div>
              </div>

              <div className="mt-3">
                <div className="text-sm font-medium">Service Type</div>
                <div className="text-xs">{pkg.serviceType}</div>
              </div>

              <div className="mt-3">
                <div className="text-sm font-medium">Total</div>
                <div className="text-base font-bold">{pkg.total}</div>
              </div>

              <div className="flex mt-4">
                <Button
                  variant="ghost-brown"
                  className="rounded-full px-6 py-1 text-sm"
                >
                  Select
                </Button>
              </div>

              <div className="flex justify-center mt-3">
                <button
                  className="flex items-center text-black font-medium text-sm"
                  onClick={() => togglePackageExpand(index)}
                >
                  Hide all
                  <ArrowUp className="w-4 h-4 ml-1" />
                </button>
              </div>
            </>
          ) : (
            /* Show only the "See all" button when collapsed */
            <div className="flex justify-center mt-3">
              <button
                className="flex items-center text-black font-medium text-sm"
                onClick={() => togglePackageExpand(index)}
              >
                See all
                <ArrowDown className="w-4 h-4 ml-1" />
              </button>
            </div>
          )}
        </div>
      ))}
    </div>
  );

  return (
    <div className="w-full border border-gray-200 rounded-lg overflow-hidden">
      <div className="p-4 font-bold text-xl border-b border-gray-200">
        Compare Package
      </div>

      <div className="w-full">
        <DesktopView />
        <MobileView />
      </div>
    </div>
  );
};

export default PackageComparisonTable;
