import StackCard from "@/components/elements/card/stack";
import classNames from "classnames";
import { Star } from "lucide-react";
import { FC } from "react";

interface OverviewType {
  className?: string;
}

const Overview: FC<OverviewType> = ({ className }) => {
  return (
    <div
      className={classNames(
        "lg:bg-white shadow-box grid grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-0",
        className
      )}
    >
      {[...Array(+4).keys()].map((_, index) => {
        return (
          <StackCard
            key={index}
            mainText="4345"
            subText="Active Products"
            icon={<Star />}
            className="!shadow-box lg:!shadow-none lg:border-r last-of-type:border-0 border-stone-200"
          >
            <div className="flex flex-col gap-1">
              <span className="text-sm text-stone-500">Profile views</span>
              <h4 className="text-base font-bold">46443</h4>
            </div>
          </StackCard>
        );
      })}
    </div>
  );
};

export default Overview;
