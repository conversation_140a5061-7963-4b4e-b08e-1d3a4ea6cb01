import compare from "@/screens/company/compare/@redux/slice";
import elements from "@/screens/elements/@redux/slice";
import user_dashboard from "@/screens/user/dashboard/@redux/slice";
import user_company from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/slice";
import user_products from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import user_services from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import user_portfolio from "@/screens/user/dashboard/pages/seller/listing/module/portfolio/@redux/slice";
import user_buyer_business from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/slice";
import user_buyer_rfqdetails from "@/screens/user/dashboard/pages/buyer/manage/@redux/slice";
import user_buyer_rfq from "@/screens/user/dashboard/pages/buyer/request/@redux/slice";
import user_buyer_favourites from "@/screens/user/dashboard/pages/buyer/favourite/@redux/slice";
import user_buyer_settings from "@/screens/user/dashboard/pages/buyer/setting/@redux/slice";

export const allReducers = {
  compare,
  elements,
  user_dashboard,
  user_company,
  user_portfolio,
  user_products,
  user_services,
  user_buyer_business,
  user_buyer_rfqdetails,
  user_buyer_rfq,
  user_buyer_favourites,
  user_buyer_settings
};
