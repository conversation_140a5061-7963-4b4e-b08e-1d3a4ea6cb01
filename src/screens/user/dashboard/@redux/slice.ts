import { APIResponseType } from "@/redux/helper/thunk";
import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";

interface APTStateType {
  deleteMedia: APIResponseType;
}

const APIState: APTStateType = {
  deleteMedia: {
    data: null,
    loading: false,
    meta: null,
  },
};

interface stateType extends APTStateType {
  showSidebar: boolean;
}

const initialState = {
  showSidebar: true,
  ...APIState,
} satisfies stateType as stateType;

export const slice = createSlice({
  name: "user_dashboard",
  initialState,
  reducers: {
    setSidebarShow: (state, action: PayloadAction<boolean>) => {
      state.showSidebar = action?.payload;
    },
  },
});

export const Actions = slice.actions;
export default slice.reducer;
