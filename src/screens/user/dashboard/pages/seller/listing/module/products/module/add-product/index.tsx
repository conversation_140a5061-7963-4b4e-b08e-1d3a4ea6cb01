"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { RootDispatch } from "@/redux/store";
import { productDetails } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import {
  getProduct,
  updateProductDetails,
} from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/thunk";
import { AlertCircle } from "lucide-react";
import { useParams, usePathname, useRouter } from "next/navigation";
import { lazy, Suspense, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const formData = [
  {
    title: "Basic Product Information",
    component: lazy(() => import("./info")),
  },
  {
    title: "Product Details",
    component: lazy(() => import("./details")),
  },
  {
    title: "Pricing Order Details",
    component: lazy(() => import("./pricing")),
  },
  {
    title: "Visual Media & additional Services",
    component: lazy(() => import("./media")),
  },
  {
    title: "Product Specifications",
    component: lazy(() => import("./specifications")),
  },
];

const ProductsAdd = () => {
  const dispatch = useDispatch<RootDispatch>();
  const router = useRouter();
  const { data: values, meta, loading } = useSelector(productDetails);
  const pathname = usePathname();
  const params = useParams();
  const product_id = params?.id;

  useEffect(() => {
    if (product_id) {
      dispatch(getProduct(product_id as string));
    } else {
      dispatch(Actions.setProductDetails(null));
    }
    dispatch(Actions.setSuggestedProduct(null));

    return () => {
      dispatch(Actions.setProductDetails(null));
      dispatch(Actions.setSuggestedProduct(null));
    };
  }, [dispatch, product_id, pathname]);

  const handleSave = (addNew?: boolean) => {
    try {
      if (values) {
        const data = new FormData();
        Object.entries(values).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (key === "metadata") {
              data.append(key, JSON.stringify(value));
            } else if (
              Array.isArray(value) &&
              value.some((fl) => fl instanceof File)
            ) {
              value.forEach((file: any) => {
                if (file instanceof File) {
                  data.append(key, file);
                }
              });
            } else if (value instanceof File || value instanceof Blob) {
              data.append(key, value);
            } else if (
              Array.isArray(value) &&
              value.some((fl) => typeof fl === "string")
            ) {
              value.forEach((str) => {
                data.append(key, String(str));
              });
            } else {
              data.append(key, String(value));
            }
          }
        });

        dispatch(
          updateProductDetails({
            product_id: product_id as string,
            data,
          })
        )
          .unwrap()
          .then((res) => {
            if (res?.type === "success") {
              if (addNew) {
                router.push("/dashboard/seller/listing/products/add");
                dispatch(Actions.setProductDetails(null));
              } else {
                router.push("/dashboard/seller/listing/products");
              }
            }
          })
          .catch((error) => {
            console.error(error);
          });
      }
    } catch (error) {
      console.error(error);
    }
  };

  if (product_id && !loading && !values && meta?.status === 404) {
    return (
      <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
        <AlertCircle /> {meta?.message}
      </div>
    );
  }

  return (
    <main className="w-full h-full">
      <h2 className="text-base font-semibold mb-6">
        {product_id ? "Update" : "Add"} Product
      </h2>
      <Accordion
        type="multiple"
        className="w-full"
        defaultValue={formData?.map((_, i) => String(i))}
      >
        {formData?.map((form, index) => {
          return (
            <AccordionItem
              key={index}
              value={String(index)}
              className="bg-white rounded-lg md:shadow-box md:p-4 [&:not(:last-child)]:mb-3"
            >
              <AccordionTrigger>{form?.title}</AccordionTrigger>
              <AccordionContent
                className="relative !py-6 px-7 flex flex-col gap-8 [&>*]:w-full min-h-20"
                disabled={loading}
                loading={loading}
              >
                <Suspense fallback={<Loader center big box />}>
                  <form.component />
                </Suspense>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>

      <div className="flex items-center justify-start md:justify-end flex-wrap gap-2 mt-7 [&>*]:rounded-md">
        <Button variant="destructive" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button
          variant="destructive"
          onClick={() => handleSave(true)}
          disabled={!values}
        >
          Save & Add New Product
        </Button>
        <Button
          variant="main-revert"
          onClick={() => handleSave()}
          disabled={!values}
        >
          Save
        </Button>
      </div>
    </main>
  );
};

export default ProductsAdd;
