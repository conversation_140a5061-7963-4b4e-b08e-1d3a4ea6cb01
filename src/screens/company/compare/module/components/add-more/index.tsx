import Dropdown, { DropdownReferenceType } from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { Input } from "@/components/ui/input/text";
import { companies } from "@/constants/companies";
import { getFilteredData } from "@/lib/utils/data";
import { comparedList } from "@/screens/company/compare/@redux/selectors";
import { Actions } from "@/screens/company/compare/@redux/slice";
import { X } from "lucide-react";
import { useRef, useState } from "react";
import { FiSearch } from "react-icons/fi";
import { GoPlusCircle } from "react-icons/go";
import { useDispatch, useSelector } from "react-redux";

const CompareAddMore = () => {
  const dropdownRef = useRef<DropdownReferenceType>(null);
  const dispatch = useDispatch();
  const [searchValue, setSearchValue] = useState<string>("");
  const [showSearch, setShowSearch] = useState<boolean>(false);
  const compared = useSelector(comparedList);

  const filteredMenu = getFilteredData({
    data: companies,
    compareValue: searchValue,
    compareParams: ["label", "location"],
  });

  const finalData = Boolean(searchValue) ? filteredMenu : companies;

  const handleSelect = (selected: any) => {
    try {
      dispatch(Actions.setComparedList(selected));
      setShowSearch(false);
    } catch (error) {
      console.error(error);
      setShowSearch(false);
    }
  };

  if (compared && compared.length > 3) {
    return null;
  }

  return (
    <div className="relative w-full flex flex-col items-center justify-center my-10">
      {showSearch ? (
        <div className="w-full flex items-center justify-between gap-4 max-w-sm lg:max-w-md">
          <Dropdown
            ref={dropdownRef}
            content={
              <DropdownList
                data={finalData}
                className="rounded-md min-w-full w-min border-0 [&_.menu-item-icon-container>*]:w-8 [&_.menu-item-icon-container>*]:h-8 [&_.menu-item-icon-container>*]:rounded-full [&_.menu-item-icon-container>*]:!object-cover"
                onSelect={handleSelect}
              />
            }
            className="flex-1"
            contentClassName="w-full flex-1 z-20 mt-0"
          >
            <Input
              type="text"
              placeholder="Search Company"
              containerClassName="flex-1"
              wrapperClassName="rounded-full bg-white"
              icon={<FiSearch size={6} />}
              value={searchValue}
              onChange={(e) => setSearchValue(e?.target?.value)}
              autoFocus
              // onFocus={() => dropdownRef?.current?.toggle(true)}
              // onBlur={() => dropdownRef?.current?.toggle(false)}
            />
          </Dropdown>

          <button
            className="flex items-center justify-center text-white"
            onClick={() => setShowSearch(false)}
          >
            <X />
          </button>
        </div>
      ) : (
        <button
          className="flex items-center gap-2 text-white"
          onClick={() => {
            setShowSearch((prev) => !prev);
            dropdownRef?.current?.toggle(true);
          }}
        >
          <GoPlusCircle />
          <span className="text-sm font-semibold">Add More</span>
        </button>
      )}
    </div>
  );
};

export default CompareAddMore;
