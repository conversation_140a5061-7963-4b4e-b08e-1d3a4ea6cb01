import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { InputLabel } from "@/components/ui/input/components/label";
import { DropdownOptions } from "@/constants/listing";
import { useState } from "react";
import { FaCaretDown } from "react-icons/fa";

const BusinessType = () => {
  const [selected, setSelected] = useState<any[]>([]);
  const displayValueCount = 2;

  const displayValue = (): string => {
    try {
      if (Array.isArray(selected) && selected?.length > 1) {
        const showValues: string = selected
          ?.map((item) => item?.title)
          ?.slice(0, displayValueCount)
          ?.join(", ");
        return showValues;
      }
      return "Business Type";
    } catch (error) {
      return "Business Type";
    }
  };

  return (
    <div className="w-full">
      <Dropdown
        content={
          <DropdownList
            data={DropdownOptions(selected, setSelected)}
            className="w-full max-w-sm sm:min-w-md md:min-w-80 border-0"
            search
            checkbox
          />
        }
        contentClassName="z-20 !w-full"
        actionClassName="flex-1 flex flex-col gap-1.5 items-center [&>*]:w-full"
      >
        <InputLabel>Business Type</InputLabel>
        <button
          title={displayValue()}
          className="text-xs py-1 px-4 hover:bg-slate-50 transition-all flex items-center justify-between gap-2 border border-light rounded-full h-input w-full"
        >
          <div className="flex items-center justify-start gap-1.5 flex-1">
            <span className="text-sm font-medium text-ellipsis overflow-hidden whitespace-nowrap">
              {displayValue()}
            </span>
            {selected?.length > displayValueCount && (
              <span className="text-sm font-medium text-stone-600">
                +{selected?.length - displayValueCount}
              </span>
            )}
          </div>

          <FaCaretDown />
        </button>
      </Dropdown>
    </div>
  );
};

export default BusinessType;
