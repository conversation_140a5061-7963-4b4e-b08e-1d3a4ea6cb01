"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  <PERSON>overContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import classNames from "classnames";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import * as React from "react";
import { type DateRange as DateRangeType } from "react-day-picker";
import { Calendar } from "../../calendar";

type DateRangeInputTargetType = {
  target: {
    name: string;
    value?: DateRangeType;
  };
};

interface DateRangeInputType {
  className?: string;
  placeholder?: string;
  name?: string;
  value?: DateRangeType;
  onChange?: (d: DateRangeInputTargetType) => void;
}

const DateRangeInput: React.FC<DateRangeInputType> = ({
  className,
  onChange,
  value,
  placeholder,
  name,
}) => {
  const handleChange = (data: DateRangeType | undefined) => {
    try {
      onChange?.({
        target: {
          name: name || "date",
          value: data,
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className={classNames("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"input"}
            className={classNames(
              "group w-full justify-start text-left font-normal",
              !value && "text-stone-400"
            )}
          >
            <CalendarIcon className="mr-1 h-4 w-4 group-hover:text-main" />
            {value?.from ? (
              value.to ? (
                <>
                  {format(value.from, "LLL dd, y")} -{" "}
                  {format(value.to, "LLL dd, y")}
                </>
              ) : (
                format(value.from, "LLL dd, y")
              )
            ) : (
              <span>{placeholder || "Pick a date"}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 bg-white" align="start">
          <Calendar
            autoFocus
            mode="range"
            defaultMonth={value?.from}
            selected={value}
            onSelect={handleChange}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export { DateRangeInput };
export type { DateRangeInputType, DateRangeType, DateRangeInputTargetType };
