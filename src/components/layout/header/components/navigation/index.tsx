"use client";

import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { navigationData } from "@/constants/header";
import useOutsideAlert from "@/lib/hooks/useOutsideAlert";
import classNames from "classnames";
import { ButtonHTMLAttributes, FC, ReactNode, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import NavigationDropdown from "./dropdown";

const Navigation = () => {
const router = useRouter();
  const wrapperRef = useRef<HTMLDivElement>(null);
  const [active, setActive] = useState<number | null>(null);

  const handleClose = () => {
    try {
      setActive(null);
    } catch (error) {
      console.error(error);
    }
  };

  useOutsideAlert(wrapperRef, handleClose);

  return (
    <div ref={wrapperRef} className="hidden sm:flex items-center gap-6 h-full">
      {navigationData?.map((menu, index) => {
        const isActive = active === index;
        return (
          <div key={index} className={classNames("group", `order-${index}`)}>
            <MenuButton active={isActive} onClick={() => setActive(index)}>
              {menu?.label}
            </MenuButton>
            <NavigationDropdown data={menu?.menus} onClose={handleClose} />
          </div>
        );
      })}

      <MenuButton onClick={() => router.push("/compare")}>Compare</MenuButton>

      <Dropdown
        inlineAlign="left"
        content={
          <DropdownList
            data={[
              {
                label: "Seller",
                path: "/seller",
              },
              {
                label: "Buyer",
                path: "/buyer",
              },
              {
                label: "All-in-one",
                path: "",
              },
              {
                label: "Advertiser",
                path: "/",
              },
            ]}
            onSelect={(option) => router.push(option?.path)}
            className="min-w-min w-full"
            closeOnSelect
          />
        }
        hover
        contentClassName="z-20 !w-full"
      >
        <MenuButton>Grow & Collaborate</MenuButton>
      </Dropdown>
    </div>
  );
};

interface MenuButtonType extends ButtonHTMLAttributes<HTMLButtonElement> {
  active?: boolean;
  children: ReactNode;
}

const MenuButton: FC<MenuButtonType> = ({ active, children, ...rest }) => (
  <button
    className={classNames(
      "relative outline-0 border-0 text-base font-bold h-full py-8 px-1 transition-all hover:text-slate-800",
      "before:transition-all hover:before:opacity-70 before:w-full before:h-0.5 before:bg-black before:absolute before:left-0 before:bottom-0",
      active ? "before:opacity-70" : "before:opacity-0"
    )}
    {...rest}
  >
    {children}
  </button>
);

export default Navigation;
