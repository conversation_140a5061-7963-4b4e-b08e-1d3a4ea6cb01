import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client"; // JSON client for normal POST

// Compare up to 4 products by IDs
export const compareProducts = async (productIds: string[]) => {
  if (productIds.length === 0 || productIds.length > 4) {
    throw new Error("Please select between 1 to 4 products to compare.");
  }

  try {
    const response = await formDataClient.post(apiRoutes().buyer.search.productCompare, {
      product_ids: productIds,
    });

    // Assuming response contains product comparison details with images
    return response.data;
  } catch (error) {
    throw error;
  }
};
