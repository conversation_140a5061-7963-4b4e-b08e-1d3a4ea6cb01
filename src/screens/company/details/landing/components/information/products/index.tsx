import ProductCard from "@/components/elements/card/product";
import ServiceCard from "@/components/elements/card/service";
import { Button } from "@/components/ui/button";
import ProductSlider from "./slider";
import ProductTabs from "./tabs";
import { ListingData } from "@/constants/listing";

const ProductsServices = () => {
  return (
    <div className="flex flex-col items-start gap-6 [&>*]:w-full">
      {/* Products */}
      <div className="relative w-full">
        <div className="flex items-center justify-between gap-4">
          <h3 className="font-bold"> Products</h3>
          <Button variant="ghost-brown">See all products</Button>
        </div>
        <div className="w-full p-4 relative">
          <ProductTabs />
          <ProductSlider>
            <ProductCard />
          </ProductSlider>
        </div>
      </div>

      {/* Services */}
      <div className="relative w-full">
        <div className="flex items-center justify-between gap-4">
          <h3 className="font-bold">Services</h3>
          <Button variant="ghost-brown">See all services</Button>
        </div>
        <div className="w-full p-4 relative">
          <ProductTabs />
          <ProductSlider>
            {/* <ServiceCard company={ListingData[0]} /> */}
            <ProductCard />
          </ProductSlider>
        </div>
      </div>
    </div>
  );
};

export default ProductsServices;
