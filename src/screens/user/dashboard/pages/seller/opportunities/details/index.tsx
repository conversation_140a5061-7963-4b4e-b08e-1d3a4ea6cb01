"use client";

import { LockedRender } from "@/components/elements/card/list-card";
import ReviewsStars from "@/components/elements/icon/reviews-stars";
import { Button } from "@/components/ui/button";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";
import { MdOutlineVerified } from "react-icons/md";
import { TbXboxXFilled } from "react-icons/tb";

const columnData = {
  proposal: [
    {
      key: "category",
      label: "Category",
    },
    {
      key: "quantity",
      label: "Quantity / Volume Needed",
    },
    {
      key: "budget",
      label: "Budget Range",
    },
    {
      key: "delivery",
      label: "Delivery Location",
    },
    {
      key: "urgency",
      label: "Requirement Urgency",
    },
    {
      key: "supplier_type",
      label: "Preferred Supplier Type",
    },
    {
      key: "payment_terms",
      label: "Preferred Payment Terms",
    },
    {
      key: "product_requirement",
      label: "Specific Product Requirement",
    },
    {
      key: "product_specification",
      label: "Product specification",
    },
  ],
  contact: [
    {
      key: "contact_name",
      label: "Contact Name",
    },
    {
      key: "email",
      label: "Email Address",
    },
    {
      key: "company_name",
      label: "Company Name",
    },
    {
      key: "contact_number",
      label: "Contact Number",
    },
    {
      key: "additional_notes",
      label: "Additional Notes",
    },
  ],
};

const columnNameData = {
  category: "Digital Marketing",
  quantity: "1-500",
  budget: "5 lakh",
  delivery: "delhi, mumbai, gujarat",
  urgency: "1 week",
  supplier_type: "Manufacturer, Wholesaler",
  payment_terms: "Net 60",
  product_requirement:
    "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
  product_specification:
    "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
};

const checkList = [
  {
    label: "Identify Verified",
    success: true,
  },
  {
    label: "Payment Verified",
    success: false,
  },
  {
    label: "Email Verified",
    success: true,
  },
  {
    label: "Profile Verified",
    success: true,
  },
  {
    label: "Phone Verified",
    success: true,
  },
];

const OpportunityDetails = () => {
  const router = useRouter();
  const { opportunity_id } = useParams();
  console.log({ opportunity_id });

  const companyDetail = ListingData[0];

  return (
    <div className="w-full h-full text-sm">
      <div className="flex flex-col md:flex-row items-start justify-between gap-4 my-6 md:my-5">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-medium">
            Website Design and Development
          </h3>
          <span className={classNames("text-green-400")}>(open)</span>
        </div>

        <div className="flex flex-col items-start gap-0.5">
          <span className="text-xs">Total Response</span>
          <span className="text-base font-bold">20</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[1fr_minmax(250px,auto)] gap-4">
        <div className="w-full order-2 md:order-1 bg-white shadow-box p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-2">Proposal Details</h3>

          <div className="w-full flex flex-col gap-5 [&>*]:w-full mt-5">
            {columnData?.proposal?.map((col, index) => {
              const current = (columnNameData as any)?.[col?.key];

              return (
                <FormTableRow
                  key={index}
                  title={<span className="block mt-1">{col?.label}</span>}
                  className="!items-start"
                >
                  <span className="block mt-1">{current}</span>
                </FormTableRow>
              );
            })}
          </div>

          <div className="w-full mt-6">
            <h3 className="text-lg font-semibold mb-2">Contact Information</h3>

            <div className="w-full flex flex-col gap-5 [&>*]:w-full mt-5">
              {columnData?.contact?.map((cont, index) => {
                return (
                  <FormTableRow key={index} title={cont?.label}>
                    <LockedRender />
                  </FormTableRow>
                );
              })}
            </div>
          </div>

          <div className="flex items-center justify-start md:justify-end flex-wrap gap-2 mt-7 [&>*]:rounded-md">
            <Button
              variant="main-revert"
              onClick={() =>
                router.push(
                  `/dashboard/seller/opportunities/${opportunity_id}/proposal`
                )
              }
            >
              Submit Proposal
            </Button>
            <Button variant="destructive" onClick={() => router.back()}>Decline</Button>
          </div>
        </div>

        <DetailsSidebar details={companyDetail} />
      </div>
    </div>
  );
};

const DetailsSidebar = ({ details }: { details: any }) => {
  return (
    <div className="order-1 md:order-2 bg-white shadow-box p-4 rounded-md">
      <h3 className="text-lg font-semibold mb-6">About Client</h3>

      <div className="flex flex-col items-start gap-3.5 [&>*]:w-full">
        <h4 className="font-semibold flex justify-start">
          <LockedRender />
        </h4>
        <div className="flex items-center gap-1.5">
          <img
            src={details?.location?.flag}
            alt="flag"
            className="flex-1 aspect-auto max-w-5 w-5 h-auto object-contain"
          />
          <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 text-wrap">
            <span>{details?.location?.address}</span>
            <span>{details?.location?.label}</span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <ReviewsStars rating={4} />
          <span className="font-light text-base text-stone-700">4/5</span>
        </div>

        {details?.verified && (
          <div className="flex items-center gap-2 mb-2">
            <div className="hidden sm:flex bg-[#9F9795] text-white px-2 py-1 items-center justify-start gap-1.5 w-max max-w-max rounded-sm">
              <img
                src={"/assets/pages/verified-white.svg"}
                alt="flag"
                className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
              />
              <span className="flex-1 text-[0.6rem] font-medium">Verified</span>
            </div>
          </div>
        )}

        <div className="flex items-start flex-col gap-3 [&>*]:w-full">
          {checkList?.map((ch, index) => (
            <div
              key={index}
              className="flex items-start justify-between gap-2.5 [&>svg]:w-5 [&>svg]:h-5"
            >
              <span className="text-sm font-light">{ch?.label}</span>
              {ch?.success ? (
                <MdOutlineVerified className="fill-green-500" />
              ) : (
                <TbXboxXFilled className="fill-red-500" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const FormTableRow = ({
  title,
  children,
  className,
}: {
  title: string | ReactNode;
  children: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={classNames(
        "grid grid-cols-1 md:grid-cols-[200px_10px_minmax(400px,_1fr)] items-center align-middle gap-4",
        className
      )}
    >
      <span className="text-sm">{title}</span>
      <span className="hidden md:flex items-start justify-center leading-3 h-full mt-2">
        :
      </span>
      <span className="w-full flex items-center justify-start [&>*]:flex-1 max-w-max">
        {children}
      </span>
    </div>
  );
};

export default OpportunityDetails;
