'use client'

import { FC, useState } from "react";
import { ArrowRightLeft } from "lucide-react";
import classNames from "classnames";

interface BuyerNameType {
  name: string;
  location: string;
}

interface PaymentType {
  method: string;
  date: string;
}

interface InvoiceListCardProps {
  invoice: string;
  buyerName: BuyerNameType;
  status: string;
  payment: PaymentType;
  totalTransaction: string;
  onViewDetails?: () => void;
  onChangeStatus?: () => void;
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
}

const InvoiceListCard: FC<InvoiceListCardProps> = ({
  invoice,
  buyerName,
  status,
  payment,
  totalTransaction,
  onViewDetails,
  onChangeStatus,
  selected,
  onSelect
}) => {
  const [isSelected, setIsSelected] = useState(selected || false);

  const handleSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsSelected(!isSelected);
    if (onSelect) {
      onSelect(!isSelected);
    }
  };

  return (
    <div className="w-full text-sm flex items-center justify-between px-4 py-3 border-none shadow-sm hover:shadow-md bg-white rounded-md">
      <div className="flex items-center justify-center w-10">
        <input
          type="checkbox"
          className="h-4 w-4"
          checked={isSelected}
          onChange={handleSelect}
          onClick={(e) => e.stopPropagation()}
        />
      </div>

      <div className="w-1/6 text-center">
        <div className="text-gray-800 text-xs font-medium">{invoice}</div>
      </div>

      <div className="w-1/6 text-center">
        <div className="text-gray-800 font-medium text-xs">{buyerName.name}</div>
        <div className="text-xs text-stone-500">{buyerName.location}</div>
      </div>

      <div className="w-1/12 flex justify-center">
        <div className={classNames(
          "px-3 py-1 rounded-full text-center text-xs",
          status === "Active" ? "text-green-500" :
            status === "Pending" ? "text-yellow-500" :
              "text-red-500"
        )}>
          {status}
        </div>
      </div>

      <div className="w-1/5 text-center">
        <div className="text-gray-800 text-xs">{payment.method}</div>
        <div className="text-stone-500 text-xs">{payment.date}</div>
      </div>

      <div className="w-1/6 text-center">
        <div className="text-gray-800 font-medium text-xs">{totalTransaction}</div>
      </div>

      <div className="w-1/4 flex items-center justify-center gap-2">
        <button
          className="flex items-center text-gray-600 hover:text-blue-600"
          onClick={(e) => {
            e.stopPropagation();
            if (onViewDetails) onViewDetails();
          }}
        >
          <span className="text-xs">View Details</span>
        </button>
        <span><ArrowRightLeft size={12} className="text-red-500" /></span>
        <button
          className="flex items-center text-gray-600 hover:text-blue-600"
          onClick={(e) => {
            e.stopPropagation();
            if (onChangeStatus) onChangeStatus();
          }}
        >
          <span className="text-xs">Change Status</span>
        </button>
      </div>
    </div>
  );
};

interface InvoiceListHeaderProps {
  className?: string;
}

export const InvoiceListHeader: FC<InvoiceListHeaderProps> = ({ className }) => {
  return (
    <div className={classNames("w-full text-sm flex items-center justify-between px-4 py-2 border-none rounded-md uppercase text-gray-600 font-medium", className)}>
      <div className="flex items-center justify-center w-10">
        <input type="checkbox" className="h-4 w-4" />
      </div>
      <div className="w-1/6 text-center">INVOICE</div>
      <div className="w-1/6 text-center">BUYER NAME</div>
      <div className="w-1/12 text-center">STATUS</div>
      <div className="w-1/5 text-center">PAYMENT</div>
      <div className="w-1/6 text-center">TOTAL TRANSACTION</div>
      <div className="w-1/4 text-center">ACTIONS</div>
    </div>
  );
};

// Sample data interface
interface InvoiceDataType {
  id: number;
  invoice: string;
  buyerName: BuyerNameType;
  status: string;
  payment: PaymentType;
  totalTransaction: string;
}

// Sample data for demonstration
const invoiceData: InvoiceDataType[] = [
  {
    id: 1,
    invoice: "#INV-2342443",
    buyerName: {
      name: "Edward Norton",
      location: "Ohio, USA"
    },
    status: "Active",
    payment: {
      method: "Direct bank transfer",
      date: "25 March, 12:55"
    },
    totalTransaction: "$43,000.00"
  },
  {
    id: 2,
    invoice: "#INV-2342444",
    buyerName: {
      name: "Brad Pitt",
      location: "California, USA"
    },
    status: "Pending",
    payment: {
      method: "Credit Card",
      date: "26 March, 10:30"
    },
    totalTransaction: "$29,500.00"
  },
  {
    id: 3,
    invoice: "#INV-2342445",
    buyerName: {
      name: "Tom Hanks",
      location: "New York, USA"
    },
    status: "Active",
    payment: {
      method: "PayPal",
      date: "27 March, 15:20"
    },
    totalTransaction: "$37,800.00"
  }
];

// Demo component that shows the invoice list
const InvoiceListDemo: FC = () => {
  const handleViewDetails = (id: number) => {
    console.log("View details for invoice ID:", id);
  };

  const handleChangeStatus = (id: number) => {
    console.log("Change status for invoice ID:", id);
  };

  return (
    <div className="flex flex-col gap-2 max-w-5xl mx-auto p-4 bg-gray-50">
      {/* Horizontal scroll wrapper */}
      <div className="w-full overflow-x-auto">
        {/* Set a min-width to trigger horizontal scroll */}
        <div className="min-w-[900px] space-y-2">
          <InvoiceListHeader />

          {invoiceData.map((invoice) => (
            <InvoiceListCard
              key={invoice.id}
              invoice={invoice.invoice}
              buyerName={invoice.buyerName}
              status={invoice.status}
              payment={invoice.payment}
              totalTransaction={invoice.totalTransaction}
              onViewDetails={() => handleViewDetails(invoice.id)}
              onChangeStatus={() => handleChangeStatus(invoice.id)}
            />
          ))}
        </div>
      </div>
    </div>

  );
};

export default InvoiceListDemo;
export { InvoiceListCard };