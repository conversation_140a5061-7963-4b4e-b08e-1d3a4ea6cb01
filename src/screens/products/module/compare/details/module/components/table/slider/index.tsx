import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import classNames from "classnames";
import { Children, cloneElement, isValidElement, ReactNode } from "react";

const CompareSlider = ({
  headerColumn,
  children,
  data = [],
  title,
  itemClassName,
}: {
  data?: any[];
  itemClassName?: string;
  title?: string;
  children: ReactNode;
  headerColumn?: ReactNode;
}) => {
  return (
    <Carousel
      opts={{
        align: "start",
      }}
      className="w-full static shadow-box rounded-lg"
    >
      {title && <div className="px-7 py-3 text-base font-bold">{title}</div>}
      <CarouselContent className="-ml-6">
        {headerColumn && (
          <CarouselItemRender className={itemClassName}>
            {headerColumn}
          </CarouselItemRender>
        )}
        {Array.from({ length: 4 }).map((_, index) => (
          <CarouselItemRender key={index} className={itemClassName}>
            {Children.map(children, (child) =>
              isValidElement<any>(child)
                ? cloneElement(child, { index } as any)
                : child
            )}
          </CarouselItemRender>
        ))}
      </CarouselContent>
    </Carousel>
  );
};

const CarouselItemRender = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => (
  <CarouselItem
    className={classNames(
      "pl-6 basis-auto",
      className
    )}
  >
    {children}
  </CarouselItem>
);

export default CompareSlider;
