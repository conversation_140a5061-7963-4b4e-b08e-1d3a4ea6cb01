"use client";

import { Badge } from "@/components/ui/badge";

const PaidServices = () => {
  return (
    <main className="bg-white shadow-box p-4">
      <h3 className="text-base font-medium mb-6"> Paid Services</h3>

      <div className="flex flex-col items-start gap-4">
        <ServiceCard
          title="Membership Plan"
          subText="Expire on 27 march 2025"
          memberType="Free Member"
          cta="Upgrade"
          onCTA={() => {}}
        />
        <div className="w-full bg-stone-200 h-[1px]" />
        <ServiceCard
          title="Membership Plan"
          subText="Expire on 27 march 2025"
          memberType="Free Member"
          cta="Upgrade"
          onCTA={() => {}}
        />
      </div>
    </main>
  );
};

const ServiceCard = ({ title, subText, memberType, cta, onCTA }: any) => {
  return (
    <div className="w-full flex items-center gap-4 justify-between shadow-box-sm p-2">
      <div className="flex flex-col items-start gap-3">
        <h5 className="text-sm font-medium">{title}</h5>
        <p className="text-[0.65rem] font-normal">{subText}</p>
      </div>
      <div className="flex flex-col items-center gap-3">
        <Badge className="!text-[0.65rem] !text-brown !bg-warning text-nowrap">
          {memberType}
        </Badge>
        <button
          onClick={() => onCTA?.()}
          className="text-sm font-medium text-main hover:drop-shadow"
        >
          {cta}
        </button>
      </div>
    </div>
  );
};

export default PaidServices;
