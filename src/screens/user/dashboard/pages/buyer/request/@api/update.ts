import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";

// Configuration for multipart/form-data requests
const formDataConfig = {
  headers: {
    "Content-Type": "multipart/form-data",
  },
};

// Sends a POST request to add a new RFQ (Request For Quote) with form data
export const addRFQ = async (formValue: FormData) => {
  try {
    return await formDataClient.post(apiRoutes().buyer.rfq.addRFQ, formValue, formDataConfig);
  } catch (error) {
    throw error; // Propagate error to caller for handling
  }
};
