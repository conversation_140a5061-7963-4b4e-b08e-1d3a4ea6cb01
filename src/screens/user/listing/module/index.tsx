"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import InputLabel from "@/components/ui/input/components/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/input/radio";
import { Input } from "@/components/ui/input/text";
import { ChangeEvent, useState } from "react";
// import { useRouter } from "next/navigation";
import AuthBox from "@/screens/user/auth/common/auth-box";
import BusinessType from "./business-type";
import BusinessLocation from "./business-location";
import { InputTargetType } from "@/components/ui/input/types";

const Listing = () => {
  // const navigate = userouter.push();
  const [values, setValues] = useState<any>({});

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | InputTargetType
  ) => {
    try {
      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <AuthBox
      title="List Your Business"
      subText="Connect with trusted buyers & suppliers, grow your business, endless opportunities."
    >
      <div className="w-full flex flex-col gap-8">
        <div className="flex flex-col gap-5">
          <InputLabel>Please select your role</InputLabel>

          <RadioGroup defaultValue="buyer" className="grid-cols-2 md:grid-cols-4 gap-6 w-max">
            <div className="flex items-center space-x-2 w-max">
              <RadioGroupItem value="seller" id="r1" />
              <InputLabel htmlFor="r1">Seller</InputLabel>
            </div>
            <div className="flex items-center space-x-2 w-max">
              <RadioGroupItem value="buyer" id="r2" />
              <InputLabel htmlFor="r2">Buyer</InputLabel>
            </div>
            <div className="flex items-center space-x-2 w-max">
              <RadioGroupItem value="advertiser" id="r3" />
              <InputLabel htmlFor="r3">Advertiser</InputLabel>
            </div>
            <div className="flex items-center space-x-2 w-max">
              <RadioGroupItem value="all_in_one" id="r3" />
              <InputLabel htmlFor="r3">All-in-One</InputLabel>
            </div>
          </RadioGroup>
        </div>

        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            type="text"
            name="name"
            label="Full Name"
            onChange={handleChange}
            value={values?.name}
            placeholder="Enter your full address"
            wrapperClassName="!rounded-full"
          />

          <Input
            type="email"
            name="email"
            label="Email Address"
            onChange={handleChange}
            value={values?.email}
            placeholder="Enter your email address"
            wrapperClassName="!rounded-full"
          />

          <Input
            type="text"
            name="phone"
            label="Phone"
            onChange={handleChange}
            value={values?.phone}
            placeholder="Enter your phone number"
            wrapperClassName="!rounded-full"
            countrySelector={{
              defaultValue: "+91",
              name: "countryCode",
              onChange: handleChange,
              value: values?.countryCode,
            }}
          />

          <Input
            type="text"
            name="businessName"
            label="Business Name"
            onChange={handleChange}
            value={values?.businessName}
            placeholder="Enter your business address"
            wrapperClassName="!rounded-full"
          />

          <BusinessType />
          <BusinessLocation />
        </div>
        <Button
          type="submit"
          variant="main-revert"
          className="w-full rounded-full"
        >
          Add Business
        </Button>
      </div>
    </AuthBox>
  );
};

export default Listing;
