"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import { Loader } from "@/components/ui/loader";
import { RootDispatch } from "@/redux/store";
import { companyDetails } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import { getCompany } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/thunk";
import { AlertCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const Companies = () => {
  const router = useRouter();
  const dispatch = useDispatch<RootDispatch>();
  const { data: companyData, loading } = useSelector(companyDetails);

  useEffect(() => {
    dispatch(getCompany());
  }, [dispatch]);

  useEffect(() => {
    if (!loading && !companyData) {
      router.push("/dashboard/seller/listing/company/update");
    }
  }, [companyData, dispatch, loading, router]);

  const handleEdit = () => {
    router.push(`/dashboard/seller/listing/company/update`);
  };

  if (loading) {
    return <Loader big center />;
  }

  return (
    <main className="w-full h-full">
      {/* <h2 className="text-base font-semibold mb-6">Company</h2> */}

      <div className="space-y-4">
        {companyData ? (
          <>
            <ListCardHeader
              data={[
                {
                  label: "Image",
                  key: "image",
                  className: "max-w-[10%]",
                },
                {
                  label: "Name",
                  key: "name",
                  className: "max-w-[70%] text-start",
                },
                {
                  label: "Actions",
                  key: "actions",
                  className: "max-w-[20%] text-center",
                },
              ]}
            />
            <ListCard
              key={companyData?._id}
              image={companyData?.business_logo}
              title={companyData?.business_name ?? " "}
              category={
                companyData?.industry_category?.[0]?.name ||
                companyData?.business_type?.[0]
              }
              onEdit={() => handleEdit()}
              classNames={{
                title: "!max-w-[70%]",
              }}
            />
          </>
        ) : (
          <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
            <AlertCircle /> No Company Available
          </div>
        )}
      </div>
    </main>
  );
};

export default Companies;
