import image1 from "/assets/pages/portfolio/p_01.png";
import image2 from "/assets/pages/portfolio/p_02.png";
import image3 from "/assets/pages/portfolio/p_03.png";
import image4 from "/assets/pages/portfolio/p_04.png";
import image5 from "/assets/pages/portfolio/p_05.png";
import image6 from "/assets/pages/portfolio/p_06.png";
import {
  Carousel,
  CarouselBullet,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import classNames from "classnames";

const ImagesData = [image1, image2, image3, image4, image5, image6];

const ProductGalleryMobile = () => {
  const images = ImagesData;

  return (
    <div className="slider-container flex flex-row gap-3.5 max-h-[460px]">
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full max-w-full"
      >
        <CarouselContent>
          {images.map((img, index) => {
            return (
              <CarouselItem key={index} className="max-h-[460px]">
                <div
                  key={index}
                  className={classNames(
                    "aspect-square w-full h-full transition-all"
                  )}
                >
                  <img
                    src={img}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselBullet
          containerClassName="mt-4"
          activeClassName="!bg-black !w-3.5"
        />
      </Carousel>
    </div>
  );
};

export default ProductGalleryMobile;
