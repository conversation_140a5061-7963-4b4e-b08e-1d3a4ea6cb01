"use client";

import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { helpCenter, resources } from "@/constants/header";
import { languages } from "@/constants/languages";
import useOpenExternalUrl from "@/lib/hooks/useOpenExternalUrl";

const TopHeader = () => {
  const openUrl = useOpenExternalUrl();

  return (
    <section className="top-header hidden sm:block bg-dark text-white">
      <div className="container flex justify-end">
        <div className="flex justify-between gap-2">
          <Dropdown
            inlineAlign="left"
            content={
              <DropdownList
                data={languages}
                className="border-t-0 min-w-40 w-min"
                closeOnSelect
              />
            }
            hover
            contentClassName="z-20"
          >
            <MenuButton title="Language" />
          </Dropdown>
          <Dropdown
            inlineAlign="left"
            content={
              <DropdownList
                data={resources}
                menuClassName="py-3 [&:not(:last-child)]:border-b last-of-type:border-b-0 border-slate-200"
                className="min-w-40 w-min"
                onSelect={(option) => openUrl(option?.path)}
                closeOnSelect
              />
            }
            hover
            contentClassName="z-20"
          >
            <MenuButton title="Resources" />
          </Dropdown>
          <Dropdown
            inlineAlign="left"
            content={
              <DropdownList
                data={helpCenter}
                menuClassName="py-3 [&:not(:last-child)]:border-b last-of-type:border-b-0 border-slate-200"
                className="min-w-40 w-min"
                onSelect={(option) => openUrl(option?.path)}
                closeOnSelect
              />
            }
            hover
            contentClassName="z-20"
          >
            <MenuButton title="Help Center" />
          </Dropdown>
        </div>
      </div>
    </section>
  );
};

const MenuButton = ({ title }: { title: string }) => (
  <button className="text-xs py-1 px-1.5 hover:bg-slate-900 transition-all">
    {title}
  </button>
);

export default TopHeader;
