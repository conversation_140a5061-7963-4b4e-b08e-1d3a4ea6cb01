'use client';

import { MultiSelect } from "@/components/ui/input/multi-select";
import React, { useState } from 'react';

interface LanguageProps {
  onLanguageChange?: (details: LanguageDetails) => void;
  initialDetails?: LanguageDetails;
}

interface LanguageDetails {
  languages: string[];
}

const Language: React.FC<LanguageProps> = ({
  onLanguageChange,
  initialDetails = {
    languages: ["english"]
  }
}) => {
  const [languageDetails, setLanguageDetails] = useState<LanguageDetails>(initialDetails);
  
  const languageOptions = [
    { label: "English", value: "english" },
    { label: "Spanish", value: "spanish" },
    { label: "French", value: "french" },
    { label: "German", value: "german" },
    { label: "Mandarin", value: "mandarin" },
    { label: "Japanese", value: "japanese" },
    { label: "Hindi", value: "hindi" },
    { label: "Portuguese", value: "portuguese" },
    { label: "Arabic", value: "arabic" },
    { label: "Russian", value: "russian" }
  ];

  const handleLanguageChange = (event: any) => {
    const selectedLanguages = event.target.value;
    const updatedDetails = {
      ...languageDetails,
      languages: selectedLanguages
    };
    setLanguageDetails(updatedDetails);
    if (onLanguageChange) {
      onLanguageChange(updatedDetails);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">Select the languages you speak</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <MultiSelect
            name="languages"
            options={languageOptions}
            placeholder="Select languages"
            onValueChange={handleLanguageChange}
            defaultValue={languageDetails.languages}
          />
        </div>
      </div>
    </div>
  );
};

export default Language;