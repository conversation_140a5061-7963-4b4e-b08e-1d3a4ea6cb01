"use client";

import AdvancedFilter from "@/components/elements/filters/listings/desktop/advanced";
import CategoryFilter from "@/components/elements/filters/listings/desktop/category";
import CompanyTypeFilter from "@/components/elements/filters/listings/desktop/company-type";
import LocationSearchFilter from "@/components/elements/filters/listings/desktop/location";
import RatingFilter from "@/components/elements/filters/listings/desktop/ratings";
import SortFilter from "@/components/elements/filters/listings/desktop/sort";
import VerifiedFilter from "@/components/elements/filters/listings/desktop/verified";
import { ListingData } from "@/constants/listing";
import useScrolled from "@/lib/hooks/useScrolled";
import classNames from "classnames";
import ComparePop from "../compare-pop";
import ListCard from "./service-card"; // Update the path to where your ListCard component is located

const ServiceListings = () => {
  const isScrolled = useScrolled();

  return (
    <div className="bg-white">
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-800">{ListingData.length} results found</h2>
        </div>
        <div
      className={classNames(
        "flex flex-col md:flex-row md:items-center md:flex-wrap gap-2 md:gap-3 bg-white py-4 md:py-6 px-4 z-30 transition-all",
        isScrolled &&
          "shadow-md sticky top-header-sm sm:top-header-md md:top-header",
        classNames
      )}
    >
      <div className="w-full md:w-auto">
        <LocationSearchFilter />
      </div>
      <div className="grid grid-cols-2 md:flex gap-2 md:gap-3">
        <CategoryFilter />
        <RatingFilter />
      </div>
      <div className="grid grid-cols-2 md:flex gap-2 md:gap-3">
        <CompanyTypeFilter />
        <VerifiedFilter />
      </div>
      <div className="flex gap-2 mt-2 md:mt-0">
        <AdvancedFilter />
        {isScrolled && <SortFilter />}
      </div>
    </div>         <div className="flex flex-col gap-4">
          {ListingData.map((company, index) => (
            <ListCard key={index} company={company} />
          ))}
        </div>
        <ComparePop/>
      </div>
    </div>
  );
};

export default ServiceListings;