import React from 'react';
import Card from './card';
import CarouselSlider from './slider';

const HomePage = () => {
  const cardData = [
    {
      image: "assets/pages/seller/Mask Group 75.png",
      title: "Free",
      subtitle: "Business Listing",
      description: "Start without any upfront cost and showcase your offerings.",
      backgroundColor: "#F9D8E0",
    },
    {
      image: "assets/pages/seller/Mask Group 75.png",
      title: "Verified",
      subtitle: "Buyer Access",
      description: "Connect with genuine buyers to avoid time-wasters.",
      backgroundColor: "#ECECEC",
    },
    {
      image: "assets/pages/seller/Mask Group 75.png",
      title: "Bulk",
      subtitle: "Proposal System",
      description: "Reach multiple buyers with customized offers in one click.",
      backgroundColor: "#F5F5DC",
    },
    {
      image: "assets/pages/seller/Mask Group 75.png",
      title: "Free",
      subtitle: "Business Listing",
      description: "Start without any upfront cost and showcase your offerings.",
      backgroundColor: "#DFEEFA",
    },
    {
      image: "assets/pages/seller/Mask Group 75.png",
      title: "Free",
      subtitle: "Business Listing",
      description: "Start without any upfront cost and showcase your offerings.",
      backgroundColor: "#FFEBEE",
    },
  ];

  return (
    <div className="px-4 flex justify-start">
      <div className="w-full">
        {/* Heading */}
        {/* <h2 className="text-3xl ml-10 font-bold text-gray-800 text-start sm:text-left">
          Key Benefits for Sellers
        </h2> */}

        {/* Carousel */}
        <CarouselSlider showBullets={true} showArrows={true}>
          {cardData.map((card, index) => (
            <Card
              key={index}
              image={card.image}
              title={card.title}
              subtitle={card.subtitle}
              description={card.description}
              backgroundColor={card.backgroundColor}
            />
          ))}
        </CarouselSlider>
      </div>
    </div>
  );
};

export default HomePage;