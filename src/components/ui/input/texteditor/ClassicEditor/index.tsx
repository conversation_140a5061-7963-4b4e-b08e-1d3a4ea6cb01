"use client";

import classNames from "classnames";
import React, { useEffect, useMemo, useRef } from "react";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";
import InputLabel from "../../components/label";
import { InputTargetType } from "../../types";

interface QuillEditorProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  containerClassName?: string;
  className?: string;
  placeholder?: string;
  value?: string;
  height?: string;
  disabled?: boolean;
  onChange?: (content: InputTargetType) => void;
  maxLength?: number;
  showCharCount?: boolean;
  error?: string;
}

const getText = (content: string) => content?.replace(/<[^>]*>/g, "");

const TextEditor: React.FC<QuillEditorProps> = ({
  value,
  onChange,
  className,
  containerClassName,
  label,
  labelClassName,
  placeholder,
  name = "textEditor",
  showCharCount,
  maxLength,
  disabled = false,
  error,
}) => {
  const quillRef = useRef<ReactQuill>(null);

  const toolbarOptions = useMemo(() => {
    return [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      ["blockquote", "code-block"],
      [{ script: "sub" }, { script: "super" }],
      [{ indent: "-1" }, { indent: "+1" }],
      [{ color: [] }, { background: [] }],
      [{ align: [] }],
      ["link", "image", "video"],
      ["clean"],
    ];
  }, []);

  useEffect(() => {
    if (!maxLength) return;

    const quill = quillRef.current?.getEditor();
    if (!quill) return;

    const handleTextChange = () => {
      const plainText = quill.getText();
      if (plainText.trim().length > maxLength) {
        quill.deleteText(maxLength, plainText.length);
      }
    };

    quill.on("text-change", handleTextChange);

    return () => {
      quill.off("text-change", handleTextChange);
    };
  }, [maxLength]);

  const modules = useMemo(() => {
    return {
      toolbar: {
        container: toolbarOptions,
        // handlers: {
        //   timestamp: () => {
        //     const editor = quillRef.current?.getEditor();
        //     const timestamp = new Date().toLocaleString();
        //     editor?.insertText(editor.getSelection()?.index || 0, timestamp);
        //   },
        // },
      },
    };
  }, [toolbarOptions]);

  const handleChange = (textValue: string) => {
    try {
      if (maxLength) {
        const text = getText(textValue);
        if (text?.length <= maxLength) {
          if (onChange) {
            onChange({
              target: {
                value: textValue,
                name,
              },
            });
          }
        }
      } else {
        if (onChange) {
          onChange({
            target: {
              value: textValue,
              name,
            },
          });
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div
      className={classNames(
        "flex flex-col gap-1.5 items-center [&>*]:w-full",
        containerClassName
      )}
    >
      {label && <InputLabel className={labelClassName}>{label}</InputLabel>}
      <ReactQuill
        ref={quillRef}
        theme="snow"
        value={value}
        onChange={handleChange}
        className={classNames(
          "[&_.ql-container]:!border-none [&_.ql-editor]:h-[200px] [&_.ql-editor]:rounded-md [&_.ql-editor]:border [&_.ql-toolbar]:!border-none [&_.ql-toolbar]:shadow [&_.ql-toolbar]:bg-light/40 [&_.ql-toolbar]:rounded-md [&_.ql-toolbar]:mb-1.5",
          error
            ? "[&_.ql-editor]:border-red-500 "
            : "[&_.ql-editor]:border-light",
          className
        )}
        placeholder={placeholder}
        modules={modules}
        readOnly={disabled}
      />
      {error && (
        <div className="flex flex-col gap-0.5 justify-start text-xs text-red-500">
          <span className="w-full">{error}</span>
        </div>
      )}
      {showCharCount && value && maxLength && (
        <div className="flex justify-end text-xs text-gray-500">
          max {getText(value)?.toString()?.length}/{maxLength} chars
        </div>
      )}
    </div>
  );
};

export { TextEditor };
