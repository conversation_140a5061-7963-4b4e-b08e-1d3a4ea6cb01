import { useState } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

const RatingGallery = () => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className="relative group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ padding: '0 50px' }} // Add padding to extend hover area
    >
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full static my-4"
      >
        <CarouselContent className="-ml-6">
          {Array.from({ length: 25 }).map((_, index) => (
            <CarouselItem
              key={index}
              className="pl-6 basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/6 my-4"
            >
              <div className="rounded-md shadow aspect-square overflow-hidden">
                <img
                  src="/assets/pages/buyer/building.jpg"
                  alt="review img"
                  className="w-full h-full object-cover"
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious 
          className={`absolute -left-10 top-1/2 -translate-y-1/2 shadow-xl bg-white border border-gray-200 hover:bg-gray-50 transition-all duration-300 ease-in-out w-10 h-10 ${
            isHovered ? 'opacity-100 visible' : 'opacity-0 invisible'
          } hidden sm:inline-flex`}
          onMouseEnter={() => setIsHovered(true)}
        />
        <CarouselNext 
          className={`absolute -right-10 top-1/2 -translate-y-1/2 shadow-xl bg-white border border-gray-200 hover:bg-gray-50 transition-all duration-300 ease-in-out w-10 h-10 ${
            isHovered ? 'opacity-100 visible' : 'opacity-0 invisible'
          } hidden sm:inline-flex`}
          onMouseEnter={() => setIsHovered(true)}
        />
      </Carousel>
    </div>
  );
};

export default RatingGallery;