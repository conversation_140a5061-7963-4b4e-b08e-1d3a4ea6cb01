'use client';

import React, { useEffect, useState, useRef } from "react";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { Button } from "@/components/ui/button";
import { Trash } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { RootDispatch } from "@/redux/store";
import {
  getProfileDetailsSelector,
} from "../../../@redux/selector";
import {
  getProfileDetails,
  deleteAccount,
  updateProfileImage,
  updateProfileDetails,
} from "../../../@redux/thunk";

export default function DisplayInformation() {
  const dispatch = useDispatch<RootDispatch>();
  const profile = useSelector(getProfileDetailsSelector);

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [formData, setFormData] = useState({
    displayName: "",
    role: "",
    country: "",
    address: "",
  });

  useEffect(() => {
    dispatch(getProfileDetails());
  }, [dispatch]);

  useEffect(() => {
    if (profile.data) {
      setFormData({
        displayName: profile.data?.basicInformation?.fullName || "",
        role: profile.data?.basicInformation?.user_role || "",
        country: profile.data?.basicInformation?.country || "",
        address: profile.data?.basicInformation?.address || "",
      });
    }
  }, [profile.data]);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDeleteAccount = () => {
    dispatch(deleteAccount());
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      dispatch(updateProfileImage(file)).then(() => {
        dispatch(getProfileDetails());
      });
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleSave = () => {
    const { displayName, role, country, address } = formData;

    dispatch(
      updateProfileDetails({
        fullName: displayName,
        user_role: role,
        country,
        address,
      } as any)
    ).then(() => {
      dispatch(getProfileDetails());
    });
  };

  const countryOptions = [
    {
      label: "India",
      value: "India",
      icon: "https://flagcdn.com/w20/in.png",
    },
    {
      label: "Singapore",
      value: "Singapore",
      icon: "https://flagcdn.com/w20/sg.png",
    },
    {
      label: "United States",
      value: "USA",
      icon: "https://flagcdn.com/w20/us.png",
    },
  ];

  const roleOptions = [
    { label: "Buyer", value: "buyer" },
    { label: "Seller", value: "seller" },
    { label: "Agent", value: "agent" },
  ];

  if (profile.loading) return <p>Loading profile...</p>;

  return (
    <div className="bg-white rounded-md shadow-sm p-6 pt-6 pb-4 max-w-7xl mx-auto">
      <h2 className="text-lg font-medium mb-6 border-b pb-4">
        Display Information
      </h2>

      <div className="flex flex-col md:flex-row gap-8">
        <div className="w-full md:w-1/4 px-4 mb-6 md:mb-0">
          <div className="flex flex-col items-center md:items-start">
            {/* Profile Image */}
            <div className="relative w-28 h-28 sm:w-32 sm:h-32">
              <img
                src={
                  profile.data?.basicInformation?.profile_picture ||
                  "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg"
                }
                alt="Profile"
                className="w-full h-full object-cover shadow-lg rounded-md"
              />
            </div>

            {/* Hidden File Input */}
            <input
              type="file"
              accept="image/*"
              ref={fileInputRef}
              onChange={handleImageChange}
              className="hidden"
            />

            {/* Button to trigger upload */}
            <Button
              variant="main-revert"
              size="default"
              type="button"
              className="mt-4 w-32 sm:w-40 rounded-md"
              onClick={triggerFileInput}
            >
              Change Photo
            </Button>
          </div>
        </div>


        <div className="w-full md:w-3/4">
          <div className="space-y-4">
            <Input
              label="Display Name"
              name="displayName"
              value={formData.displayName}
              onChange={handleChange}
              placeholder="Input text"
              wrapperClassName="border rounded-md"
              containerClassName="mb-4"
            />

            <Select
              label="Role"
              name="role"
              value={formData.role}
              onChange={handleChange}
              options={roleOptions}
              wrapperClassName="border rounded-md"
              containerClassName="mb-4"
              placeholder="Select Role"
            />

            <Select
              label="Country"
              name="country"
              value={formData.country}
              onChange={handleChange}
              options={countryOptions}
              wrapperClassName="border rounded-md"
              containerClassName="mb-4"
              placeholder="Select Country"
            />

            <Input
              label="Address"
              name="address"
              value={formData.address}
              onChange={handleChange}
              placeholder="Input text"
              wrapperClassName="border rounded-md"
              containerClassName="mb-4"
            />

            <div className="bg-white p-4 mt-2 -ml-4 mb-0 rounded-lg flex items-center gap-4">
              <Button
                variant="main-revert"
                size="default"
                type="button"
                className="w-20 rounded-md"
                onClick={handleSave} // ✅ Hook this in
              >
                Save
              </Button>
              <button
                className="flex items-center text-red-500 hover:text-red-600"
                type="button"
                onClick={handleDeleteAccount}
              >
                <Trash size={18} className="mr-0 md:mr-1" />
                Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
