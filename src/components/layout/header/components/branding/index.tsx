import classNames from "classnames";
import { FC } from "react";
import Link from "next/link";

interface BrandingType {
  className?: string;
}

const Branding: FC<BrandingType> = ({ className }) => {
  return (
    <div
      className={classNames(
        "branding flex items-center justify-center min-w-8 sm:min-w-32 max-w-32 sm:w-32 py-6 px-1 sm:mx-1",
        className
      )}
    >
      <Link href="/" className="flex items-center justify-center">
        <img
          src={"/assets/branding/logo.svg"}
          alt="Aalyana"
          className="w-full object-contain hidden sm:block"
        />
        <img
          src={"/assets/branding/icon.svg"}
          alt="Aalyana"
          className="w-full block sm:hidden object-contain"
        />
      </Link>
    </div>
  );
};

export default Branding;
