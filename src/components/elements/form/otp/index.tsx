import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input/otp";
import { InputTargetType } from "@/components/ui/input/types";
import { Loader } from "@/components/ui/loader";
import classNames from "classnames";
import { OTPInputProps } from "input-otp";
import { Check, Mail, X } from "lucide-react";
import { FC } from "react";

type OTPBoxStatusType = "initiated" | "verified" | "failed";
interface eOTPBoxType {
  className?: string;
  digits?: number;
  name?: string;
  status?: OTPBoxStatusType;
  statusLoading?: boolean;
  input?: Omit<OTPInputProps, "render" | "maxLength">;
  onChange?: (event: InputTargetType) => void;
  onResendClick?: () => void;
}

const OTPBox: FC<eOTPBoxType> = ({
  name = "otp",
  digits = 6,
  input,
  className,
  status,
  statusLoading = false,
  onChange,
  onResendClick,
}) => {
  const handleOnchange = (value: string) => {
    if (onChange) {
      onChange({ target: { value, name } });
    }
    if (input?.onChange) {
      input.onChange(value);
    }
  };

  return (
    <div
      className={classNames(
        "border-2 border-dashed border-light rounded-md w-full px-5 py-4",
        className
      )}
    >
      <span className="block text-xs text-stone-500 mb-4">
        Email verification OTP
      </span>
      <InputOTP
        maxLength={digits}
        containerClassName="!gap-5"
        autoFocus
        onChange={(value) => handleOnchange(value)}
        {...(input ?? {})}
      >
        {[...Array(+digits).keys()]?.map((_, index) => {
          return (
            <InputOTPGroup key={index} className="flex-1 justify-center">
              <InputOTPSlot
                index={index}
                className="!h-12 !min-w-14 !w-full !flex-1"
              />
            </InputOTPGroup>
          );
        })}
      </InputOTP>
      <div className="flex items-center justify-between gap-4 text-sm mt-5">
        {onResendClick ? (
          <button onClick={() => onResendClick?.()} className="hover:underline">
            Resend OTP
          </button>
        ) : (
          <span />
        )}
        <button onClick={() => onResendClick?.()} className="hover:underline">
          Resend OTP
        </button>
        <div className="">
          {statusLoading ? (
            <Loader />
          ) : (
            <>
              {status === "verified" ? (
                <span className="text-sm flex items-center justify-center gap-1 text-success">
                  <Check className="w-4 h-4" />
                  <span>Verified</span>
                </span>
              ) : status === "failed" ? (
                <span className="text-sm flex items-center justify-center gap-1 text-pending">
                  <X className="w-4 h-4" />
                  <span>Not Verified</span>
                </span>
              ) : status === "initiated" ? (
                <span className="text-sm flex items-center justify-center gap-1 text-warning">
                  <Mail className="w-4 h-4" />
                  <span>OTP Sent</span>
                </span>
              ) : null}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export { OTPBox };
export type { eOTPBoxType, OTPBoxStatusType };
