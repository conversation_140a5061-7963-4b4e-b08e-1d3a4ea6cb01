"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { Input } from "@/components/ui/input/text";
import { Loader } from "@/components/ui/loader";
import { hasRequiredKeys } from "@/lib/utils/data";
import useAuth from "@/screens/user/auth/@context/useAuth";
import { ChangeEvent, FormEvent, useState } from "react";
import { useRouter } from "next/navigation";
import AuthBox from "../../common/auth-box";
import Oauth from "../../common/oauth";

const Signin = () => {
  const router = useRouter();
  const { login, loading, response } = useAuth();
  const [values, setValues] = useState<any>({});
  const [remember, setRemember] = useState<boolean>(false);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    try {
      const name = e?.target?.name;
      const value = e?.target?.value;

      setValues((prev: any) => ({
        ...prev,
        [name]: value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    try {
      e?.preventDefault();
      await login(values);

      setValues({});
      setTimeout(() => {
        router.push("/dashboard");
      }, 1000);
    } catch (error) {
      console.error(error);
    }
  };

  const isValid = hasRequiredKeys(values, ["email", "password"]);

  return (
    <AuthBox
      title="Welcome Back! Access Your Account"
      subText="Sign in to your account to unlock the platform’s full potential."
      contentClassName="!max-w-md"
    >
      {loading && <Loader big center box />}
      {response && (
        <Alert className="mb-4" variant={response?.type}>
          {response?.type && (
            <AlertTitle className="capitalize">{response?.type}</AlertTitle>
          )}
          {response?.meta?.message && (
            <AlertDescription className="font-semibold">
              {response?.meta?.message}
            </AlertDescription>
          )}
        </Alert>
      )}
      <div className="w-full flex flex-col gap-8">
        <form
          className="w-full flex flex-col gap-8"
          onSubmit={(e) => handleSubmit(e)}
        >
          <div className="w-full grid grid-cols-1 gap-6">
            <Input
              type="email"
              name="email"
              label="Email"
              onChange={handleChange}
              value={values?.email ?? ""}
              placeholder="Enter your email address"
              wrapperClassName="!rounded-full  "
            />

            <Input
              type="password"
              name="password"
              label="Password"
              onChange={handleChange}
              value={values?.password ?? ""}
              placeholder="Enter password"
              wrapperClassName="!rounded-full"
              autoComplete="off"
            />
            <div className="flex justify-between items-center gap-4">
              <Checkbox
                label="Remember for 30 days"
                labelClassName="whitespace-nowrap"
                onChange={(e) => setRemember(e?.target?.value)}
                checked={remember}
              />
              <button
                type="button"
                className="text-sm font-semibold hover:drop-shadow hover:underline"
                onClick={() => router.push("/reset-password")}
              >
                Forget password?
              </button>
            </div>
          </div>
          <Button
            type="submit"
            variant="main-revert"
            className="w-full rounded-full"
            disabled={!isValid}
          >
            Sign in
          </Button>
        </form>

        <Oauth title="Sign in with" />

        <div className="flex items-center justify-center gap-1.5 text-sm text-center">
          <p>Don’t have an account?</p>
          <button
            className="font-semibold hover:drop-shadow text-main"
            onClick={() => router.push("/signup")}
          >
            Sign Up here.
          </button>
        </div>
      </div>
    </AuthBox>
  );
};

export default Signin;
