import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";
import privateApiClient from "@/services/axios/private-api-client";

const formDataConfig = {
  headers: {
    "Content-Type": "multipart/form-data",
    "Accept": "application/json",
  },
};

// CATEGORIES START

export const get_sub_sub_Categories = async () => {
  return await privateApiClient.get(apiRoutes().user.category.sub_sub.fetchAll);
};

// CATEGORIES END

export const addBusinessDetails = async (formValue: FormData) => {
  try {
    const serializedFormData = new FormData();
    for (const [key, value] of formValue.entries()) {
      if (Array.isArray(value)) {
        value.forEach((item) => {
          if (item instanceof File) {
            serializedFormData.append(`${key}[]`, item);
          } else if (item && typeof item === "object" && "_id" in item) {
            // Extract _id if object contains _id
            serializedFormData.append(`${key}[]`, item._id);
          } else {
            serializedFormData.append(`${key}[]`, String(item));
          }
        });
      } else if (value instanceof File) {
        serializedFormData.append(key, value);
      } else if (typeof value === 'object') {
        serializedFormData.append(key, JSON.stringify(value));
      } else {
        serializedFormData.append(key, String(value));
      }

    }

    const response = await formDataClient.post(
      apiRoutes().buyer.business.addUpdate,
      serializedFormData,
      formDataConfig
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      throw error.response.data;
    } else if (error.request) {
      throw {
        message: "No response from server",
        meta: { message: "Please check your internet connection" }
      };
    } else {
      throw {
        message: error.message,
        meta: { message: "An unexpected error occurred" }
      };
    }
  }
};

export const getBusinessDetails = async () => {
  try {
    const response = await formDataClient.get(
      apiRoutes().buyer.business.fetch,
    );

    return response.data;
  } catch (exception) {
    console.error("API getRFQDetail error:", exception);
    throw exception;
  }
}