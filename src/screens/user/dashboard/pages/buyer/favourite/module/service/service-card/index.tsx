'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useRouter } from "next/navigation";
import ContactNowModal from "@/screens/company/listing/module/components/inquiry";
import ImageGallery from "@/screens/company/listing/module/components/list/list-card/images";
import { FaRegHeart } from "react-icons/fa6";

// ✅ Update the component props interface
const ListCard = ({ 
  company, 
  onUnfavourite 
}: { 
  company: (typeof ListingData)[0];
  onUnfavourite?: () => void;
}) => {
 const router = useRouter();

  const isFavourite = true;

  return (
     <div className="w-full h-max bg-white shadow-md hover:shadow-lg rounded-md overflow-hidden">
      <div className="flex flex-col gap-0">
        <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 px-4 sm:px-6 pt-4 sm:pt-6 pb-4">
          {/* Image Slider */}
          <div className="relative w-full sm:w-max flex items-start justify-between gap-4">
            <div className="relative w-full">
              <ImageGallery images={company?.images} />

              <button
                className={`absolute right-2 top-2 z-10 transition-all h-8 w-8 rounded-full flex items-center justify-center shadow group
        ${isFavourite ? "bg-red-500" : "bg-white hover:bg-red-500"}`}
                onClick={(e) => {
                  e.stopPropagation();
                  if (isFavourite && onUnfavourite) {
                    onUnfavourite();
                  }
                }}
              >
                <FaRegHeart
                  size={16}
                  className={`mt-0.5 transition-colors
          ${isFavourite ? "text-white" : "text-gray-400 group-hover:text-white"}`}
                />
              </button>
            </div>

            <CheckboxMapping className="flex sm:hidden" />
          </div>
          {/* Title and Info */}
          <div className="flex-1 flex flex-col items-start gap-2 sm:gap-1 [&>*]:w-full">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <div className="flex items-center justify-start gap-1.5">
                <h2
                  role="button"
                  className="text-lg sm:text-xl font-bold hover:underline text-black hover:drop-shadow"
                  onClick={() => router.push("/services/details")}
                >
                  {company?.name}
                </h2>
                <div className="flex items-center gap-2 sm:hidden">
                  <img
                    src="/assets/pages/verified-gray.svg"
                    alt="flag"
                    className="w-4 h-auto"
                  />
                  <span className="text-xs text-stone-600">Sponsored</span>
                </div>
              </div>
              <CheckboxMapping className="hidden sm:flex" />
            </div>

            {/* Location */}
            <div className="flex items-center flex-wrap gap-1.5 mt-1">
              <img
                src={company?.location?.flag}
                alt="flag"
                className="w-4 h-auto"
              />
              <div className="flex flex-wrap gap-1 text-xs text-stone-700">
                <span>{company?.location?.address},</span>
                <span>{company?.location?.label}</span>
              </div>
            </div>

            {/* Verified */}
            {company?.verified && (
              <div className="flex items-center gap-2 mb-2">
                <div className="hidden sm:flex bg-[#9F9795] text-white px-2 py-1 items-center gap-1.5 rounded">
                  <img
                    src="/assets/pages/verified-white.svg"
                    alt="verified"
                    className="w-3 h-auto"
                  />
                  <span className="text-[0.6rem] font-medium">Verified</span>
                </div>
                <span className="text-xs text-stone-600">Sponsored</span>
              </div>
            )}

            {/* Price & Rating */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
              <div className="flex items-center gap-3">
                <span className="font-bold text-lg sm:text-2xl text-[#070707]">
                  ${company?.offerPrice}
                </span>
                <span className="font-bold text-base sm:text-xl text-[#A2A2A2] line-through">
                  ${company?.actualPrice}
                </span>
              </div>
              <div className="flex items-center gap-2.5">
                <img
                  src="/assets/pages/rating-star.svg"
                  alt="rating star"
                  className="w-4 sm:w-5 h-4 sm:h-5"
                />
                <div className="flex gap-2.5 text-sm font-semibold text-font">
                  <span className="border-r border-slate-300 pr-2.5">
                    {company?.rating?.stars}/5
                  </span>
                  <span>{company?.rating?.reviewsCount} Reviews</span>
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mt-2 mb-1 max-w-full overflow-hidden">
              {company?.tags?.slice(0, 10)?.map((tag, index) => (
                <button
                  key={index}
                  className="px-3 py-0.5 bg-stone-50 hover:bg-stone-100 border border-stone-300 rounded-full text-stone-800 text-xs"
                >
                  {tag?.title}
                </button>
              ))}
            </div>

            {/* Description */}
            <p
              title={company?.description}
              className="text-sm text-stone-700 line-clamp-2"
            >
              {company?.description}
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex flex-col sm:flex-row flex-wrap gap-4 sm:items-center justify-between border-t border-slate-200 px-4 sm:px-6 py-4">
          <div className="w-full sm:w-auto flex flex-col sm:flex-row items-center justify-between sm:gap-4 text-xs font-light leading-4">
            <p className="py-1 text-center sm:text-left">24/7 Availability</p>
            <p className="py-1 text-center sm:text-left sm:border-l sm:pl-4 sm:border-slate-300">On-Site Service</p>
            <p className="py-1 text-center sm:text-left sm:border-l sm:pl-4 sm:border-slate-300">
              Available: Mon–Fri, 9 AM – 6 PM
            </p>
          </div>
          <div className="w-full sm:w-auto flex flex-col sm:flex-row gap-3 mt-2 sm:mt-0">
            <Button variant="outline" size="sm" className="w-full sm:w-auto">
              Projects
            </Button>
            <ContactNowModal />
          </div>
        </div>
      </div>
    </div>
  );
};

const CheckboxMapping = ({ className }: { className?: string }) => {
  return (
    <Checkbox
      label="Add to Compare"
      labelClassName="whitespace-nowrap"
      containerClassName={classNames("order-1 sm:order-2", className)}
    />
  );
};

export default ListCard;