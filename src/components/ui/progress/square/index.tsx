import * as ProgressPrimitive from "@radix-ui/react-progress";
import classNames from "classnames";
import * as React from "react";

interface SquareProgressType
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  count?: string;
  countClassName?: string;
}

const SquareProgress = React.forwardRef<
  React.ComponentRef<typeof ProgressPrimitive.Root>, // Changed from ElementRef to ComponentRef
  SquareProgressType
>(({ className, value = 10, count, countClassName, ...props }, ref) => {
  const progressRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (progressRef?.current) {
      progressRef?.current?.style.setProperty("--progress", `${value}%`);
    }
  }, [value]);

  return (
    <div className={classNames("relative w-16 h-16", className)}>
      <div
        ref={progressRef}
        className={classNames(
          "relative w-full h-full rounded-lg transition-all will-change-auto",
          "after:absolute after:left-1/2 after:top-1/2 after:-translate-x-1/2 after:-translate-y-1/2 after:w-[85%] after:h-[85%] after:bg-white after:rounded-[0.275rem]"
        )}
        style={{
          background: `radial-gradient(closest-side, white 9%, transparent 80% 100%), conic-gradient(#347E29 0%, #347E29 var(--progress), #ECECEC 0)`
        }}
        {...props}
      />
      {count && (
        <span
          className={classNames(
            "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-sm font-semibold z-10",
            countClassName
          )}
        >
          {count}
        </span>
      )}
    </div>
  );
});

SquareProgress.displayName = "SquareProgress";

export { SquareProgress };