import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { safeCall } from "./lib/utils/api";
import { logoutAction, userAuthAction } from "./app/(user)/(auth)/actions";

// TODO
// i18n middleware pending

export async function middleware(request: NextRequest) {
  const jwt = request.cookies.get("user_access_token")?.value;
  let isAuthenticated = false;
  const url = request.nextUrl;
  const { pathname, origin, search } = url;

  if (jwt) {
    try {
      await safeCall(() => userAuthAction(jwt as string));
      isAuthenticated = true;
    } catch (err) {
      console.error("JWT verification failed:", err);
      await logoutAction();
      const res = NextResponse.redirect(new URL("/signin", origin));
      res.cookies.set("returnTo", `${pathname}${search}`, {
        path: "/",
        httpOnly: true,
        sameSite: "lax",
      });
      return res;
    }
  }

  const isProtectedRoute = request.nextUrl.pathname.startsWith("/dashboard");
  const authRoutes = ["/signin", "/signup"];

  if (isProtectedRoute && !isAuthenticated) {
    const res = NextResponse.redirect(new URL("/signin", origin));
    res.cookies.set("returnTo", `${pathname}${search}`, {
      path: "/",
      httpOnly: true,
      sameSite: "lax",
    });
    return res;
  } else if (
    isAuthenticated &&
    authRoutes?.includes(request.nextUrl.pathname)
  ) {
    const returnTo = request.cookies.get("returnTo")?.value || "/dashboard";
    const res = NextResponse.redirect(new URL(returnTo, request.url));
    res.cookies.set("returnTo", "", {
      path: "/",
      httpOnly: true,
      sameSite: "lax",
    });
    res.cookies.delete("returnTo");
    return res;
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/dashboard/:path*", "/dashboard", "/signin", "/signup"],
};
