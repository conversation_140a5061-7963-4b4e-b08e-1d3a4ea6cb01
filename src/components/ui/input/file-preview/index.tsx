"use client";

import classNames from "classnames";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import Image from "../../image";

interface FilePreviewProps {
  className?: string;
  files: File[];
  fileSrcKey?: string;
  onDelete?: (file: File) => void;
}

type PreviewType = {
  file: File;
  src: string;
};

const FilePreview: React.FC<FilePreviewProps> = ({
  className,
  files,
  fileSrcKey,
  onDelete,
}) => {
  const [previews, setPreviews] = useState<PreviewType[]>([]);

  useEffect(() => {
    if (!(files?.length > 0)) {
      setPreviews([]);
      return;
    }

    const fileReaders: FileReader[] = [];
    const urls: PreviewType[] = [];

    try {
      if (Array.isArray(files) && files.length > 0) {
        files?.forEach((file: any, index) => {
          if (!file) return;

          if (file instanceof File) {
            const reader = new FileReader();
            fileReaders.push(reader);

            reader.onloadend = () => {
              urls[index] = {
                file,
                src: reader.result as string,
              };

              setPreviews([...urls]);
            };

            reader?.readAsDataURL(file);
          } else {
            const fileSrc =
              typeof file === "string"
                ? file
                : file?.url || file?.[fileSrcKey ?? "src"];

            urls[index] = { file, src: fileSrc };
            setPreviews([...urls]);
          }
        });
      }
    } catch (error) {
      console.error(error);
    }

    return () => {
      urls.forEach((preview) => {
        URL.revokeObjectURL(preview?.src);
      });
    };
  }, [fileSrcKey, files]);

  const handleDelete = (preview: PreviewType) => {
    try {
      if (onDelete) {
        onDelete(preview?.file);
      }
    } catch (error) {
      console.error(error);
    }
  };

  if (!previews?.length) return null;

  return (
    <div
      aria-label="file preview"
      className={classNames("flex flex-wrap gap-4 mb-2", className)}
    >
      {previews.map((preview, index) => (
        <div
          role="button"
          key={index}
          className="preview-item group flex-1 max-w-40 relative cursor-auto w-full min-w-20 h-auto min-h-min"
          onClick={(e) => e?.stopPropagation()}
        >
          {onDelete && (
            <button
              onClick={() => handleDelete(preview)}
              className="absolute z-20 right-0 top-0 translate-x-1/2 -translate-y-1/2 flex items-center justify-center p-0.5 bg-white border rounded-full border-light group-hover:border-main hover:border-main hover:scale-150 transition-all"
            >
              <X className="w-4 h-4" />
            </button>
          )}
          <Image
            src={preview?.src}
            alt={`Preview ${index}`}
            className="w-full h-full object-cover border rounded-lg shadow group-hover:shadow-main transition-all"
            width={"100%"}
            height={"100%"}
          />
        </div>
      ))}
    </div>
  );
};

export { FilePreview };
