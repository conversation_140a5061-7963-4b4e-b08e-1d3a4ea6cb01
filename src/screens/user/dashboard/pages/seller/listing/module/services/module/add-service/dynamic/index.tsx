import DynamicFieldRenderer from "@/components/elements/form/dynamic/fields";
import DynamicMeasurementsRenderer from "@/components/elements/form/dynamic/measurements";
import { Loader } from "@/components/ui/loader";
import { RootDispatch } from "@/redux/store";
import {
  dynamicFields,
  serviceDetails,
} from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import { getServiceDynamicFields } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/thunk";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const ServiceDynamicFields = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values, loading } = useSelector(serviceDetails);
  const { data: dynamicFieldsData, loading: dynamicLoading } =
    useSelector(dynamicFields);

  const categoryExist =
    values?.sub_sub_category && typeof values?.sub_sub_category === "string";

  useEffect(() => {
    if (categoryExist) {
      dispatch(
        getServiceDynamicFields({ sub_sub_category: values?.sub_sub_category })
      );
    }
  }, [categoryExist, dispatch, values?.sub_sub_category]);

  if (loading || dynamicLoading) {
    return <Loader big center />;
  }

  if (
    !categoryExist ||
    !dynamicFieldsData ||
    (!(
      Array.isArray(dynamicFieldsData?.fieldsData) ||
      dynamicFieldsData?.fieldsData?.length > 0
    ) &&
      !(
        Array.isArray(dynamicFieldsData?.measurementsData) ||
        dynamicFieldsData?.measurementsData?.length > 0
      ))
  ) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {Array.isArray(dynamicFieldsData?.fieldsData) &&
        dynamicFieldsData?.fieldsData?.length > 0 &&
        dynamicFieldsData?.fieldsData?.map((field, index) => {
          return (
            <DynamicFieldRenderer
              key={index}
              field={field as any}
              values={values?.metadata?.fieldsData}
              onChange={(payload) => {
                dispatch(Actions.setDynamicValues(payload));
              }}
            />
          );
        })}
      {Array.isArray(dynamicFieldsData?.measurementsData) &&
        dynamicFieldsData?.measurementsData?.length > 0 &&
        dynamicFieldsData?.measurementsData?.map((field, index) => {
          return (
            <DynamicMeasurementsRenderer
              key={index}
              field={field as any}
              values={values?.metadata?.measurementsData}
              onChange={(payload) => {
                dispatch(Actions.setDynamicValues(payload));
              }}
            />
          );
        })}
    </div>
  );
};

export default ServiceDynamicFields;
