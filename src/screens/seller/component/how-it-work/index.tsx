"use client";

import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import img1 from "./Group 5417.png";
import img2 from "./Group 5418.png";
import img3 from "./Group 5419.png";
import img4 from "./Group 5420.png";
import img5 from "./Group 5421.png";
import img6 from "./Group 5422.png";

interface TabData {
  id: string;
  title: string;
  image: string;
  description: string;
}

const TabComponent: React.FC = () => {
const router = useRouter();

  const tabs: TabData[] = [
    {
      id: "create-profile",
      title: "Create Your Profile",
      image: "assets/pages/seller/Group 5418.png",
      description:
        "Sign up and set up your business profile in just a few steps.",
    },
    {
      id: "list-products",
      title: "List Your Products/Services",
      image: "assets/pages/seller/Group 5417.png",
      description:
        "Add your products or services with detailed descriptions and pricing.",
    },
    {
      id: "connect-buyers",
      title: "Connect with Buyers",
      image: "assets/pages/seller/Group 5419.png",
      description:
        "Interact with potential customers interested in your offerings.",
    },
    {
      id: "grow-network",
      title: "Grow Your Network",
      image: "assets/pages/seller/Group 5420.png",
      description: "Expand your business connections and reach more customers.",
    },
    {
      id: "receive-inquiries",
      title: "Receive Buyer Inquiries",
      image: "assets/pages/seller/Group 5422.png",
      description:
        "Get notified when buyers show interest in your products or services.",
    },
    {
      id: "grow-sales",
      title: "Grow Your Sales",
      image: "assets/pages/seller/Group 5421.png",
      description:
        "Convert inquiries into successful transactions and increase revenue.",
    },
  ];

  const [activeTab, setActiveTab] = useState<string>(tabs[0].id);
  const activeTabData = tabs.find((tab) => tab.id === activeTab) || tabs[0];
 const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const mobileTabContentRef = useRef<HTMLDivElement>(null);
  const desktopTabContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + window.innerHeight / 2;
      
      const mobileContainerRect = mobileTabContentRef.current?.getBoundingClientRect();
      const desktopContainerRect = desktopTabContentRef.current?.getBoundingClientRect();
      
      const isMobileView = window.innerWidth < 1024; // lg breakpoint is typically 1024px
      const activeContainer = isMobileView ? mobileContainerRect : desktopContainerRect;
      
      if (!activeContainer) return;
      
      const containerTop = activeContainer.top + window.scrollY;
      const containerBottom = containerTop + activeContainer.height;
      
      if (scrollPosition >= containerTop && scrollPosition <= containerBottom) {
        let closestTab = tabs[0].id;
        let closestDistance = Infinity;
        
        tabRefs.current.forEach((tabRef, index) => {
          if (!tabRef) return;
          
          const rect = tabRef.getBoundingClientRect();
          const tabCenter = rect.top + rect.height / 2;
          const distance = Math.abs(tabCenter - window.innerHeight / 2);
          
          if (distance < closestDistance) {
            closestDistance = distance;
            closestTab = tabs[index].id;
          }
        });
        
        setActiveTab(closestTab);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    
    // Smooth scroll to the selected tab
    const selectedTabRef = tabRefs.current[tabs.findIndex(tab => tab.id === tabId)];
    if (selectedTabRef) {
      selectedTabRef.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const renderMobileTabButton = (tab: TabData) => {
    return (
      <button
        key={tab.id}
        onClick={() => handleTabClick(tab.id)}
        className={`whitespace-nowrap 
          px-3 py-3 text-xs            // default (very small screens)
          sm:px-3 sm:py-2 sm:text-sm   // ≥640px
          md:px-4 md:py-2 md:text-base // ≥768px
          lg:px-5 lg:py-3 lg:text-base // ≥1024px
          xl:px-6 xl:py-3 xl:text-lg   // ≥1280px
          rounded-md min-w-max 
          ${
            activeTab === tab.id
              ? "bg-[#29100C] text-white font-semibold"
              : "bg-white border border-gray-300 text-black"
          }`}
      >
        {tab.title}
      </button>
    );
  };

  return (
     <div className="max-w-[85%]  2xl:mb-16  justify-center p-6">
      {/* Mobile + Tablet View */}
      <div className="block lg:hidden" ref={mobileTabContentRef}>
        <h1 className="text-[17px] text-center lg:text-3xl font-bold mb-6 ml-16">
          How It Works?
        </h1>

        <div className="flex overflow-x-auto pb-4 sm:pb-3 gap-2 no-scrollbar">
          {tabs.map(renderMobileTabButton)}
        </div>

        {/* Responsive Card Layout */}
        <div className="relative mt-1 sm:mt-1 h-[50vw] lg:h-[30vw] sm:h-[55vw] md:h-[55vw] mb-8 md:-mb-20">
          {/* Image Card */}
          <div className="absolute left-0 top-0 w-[70%] h-[100%] sm:w-[65%] md:w-[65%] md:h-[65%] z-0">
            <img
              src={activeTabData.image}
              alt={activeTabData.title}
              className="w-full h-full object-cover rounded-lg shadow-lg"
            />
          </div>

          {/* Text Card */}
          <div className="absolute top-[12%] left-[55%] w-[65%] h-[80%] sm:w-[70%] sm:h-[10%] md:h-[50%] md:w-[50%] bg-white rounded-lg p-4 sm:p-2 md:p-4 shadow-xl z-10">
            <h2 className="text-xs sm:text-sm md:text-lg font-bold mb-2">
              {activeTabData.title}
            </h2>
            <p className="text-gray-700 mb-4 md:text-lg sm:text-sm text-xs">
              {activeTabData.description}
            </p>
            <button
              onClick={() => router.push("/free-listing")}
              className="w-[100%] h-[25%] bg-[#29100C] text-white rounded-3xl flex items-center justify-center gap-1 text-xs sm:text-sm md:text-base"
            >
              Create Seller Profile
            </button>
          </div>
        </div>

        {/* Carousel Indicators - Repositioned and Improved */}
        <div className="flex mb-2 md:mt-0 justify-center items-center ">
          {tabs.map((tab, index) => (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              className={`mx-1 transition-all duration-300 ${
                activeTab === tab.id
                  ? "bg-black w-4 h-2 rounded-full"
                  : "bg-gray-300 w-2 h-2 rounded-full"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>

      {/* Desktop View */}
      <div className="hidden xl:ml-10 lg:block">
        <h1 className="text-[17px] lg:text-3xl xl:text-4xl font-bold text-start font-inter whitespace-nowrap mb-8">
          How It Works?
        </h1>
      </div>
      <div className="hidden lg:flex lg:flex-row gap-10" ref={desktopTabContentRef}>
        <div className="lg:w-[30%] lg:pl-6">
          <div className="flex flex-col gap-3">
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                ref={(el) => { tabRefs.current[index] = el }}
                onClick={() => handleTabClick(tab.id)}
                className={`p-4 text-left rounded-lg border border-gray-300 transition-all text-lg ${
                  activeTab === tab.id
                    ? "bg-[#29100C] text-white font-semibold"
                    : " hover:bg-gray-200"
                }`}
              >
                {tab.title}
              </button>
            ))}
          </div>
        </div>

        <div className="lg:w-[47%] xl:ml-8 -mt-16 w-full">
          <div className="relative ml-20">
            {/* Text Card */}
            <div
              className="xl:w-[500px]  2xl:w-[750px] xl:h-[360px] lg:w-[430px] lg:h-[300px] bg-white rounded-lg p-6 mt-16 w-full"
              style={{ boxShadow: "0px 3px 13px #00000021" }}
            >
              <div className="text-black mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="8" r="5" />
                  <path d="M20 21a8 8 0 0 0-16 0" />
                  <circle cx="12" cy="8" r="2" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold 2xl:text-5xl w-[70%] 2xl:mb-7 mb-4">
                {activeTabData.title}
              </h2>
              <p className="mb-6 text-gray-700 2xl:text-2xl 2xl:mb-7 text-lg max-w-[50%] break-words">
                {activeTabData.description}
              </p>
              <button
                onClick={() => router.push("/free-listing")}
                className="bg-[#29100C] text-white 2xl:py-4 2xl:mt-4 px-4 py-2 rounded-3xl flex items-center gap-2"
              >
                <span>+</span> Create Seller Profile
              </button>
            </div>

            {/* Image Card */}
            <div className="absolute 2xl:w-[700px] 2xl:h-[400px] xl:left-[75%] 2xl:left-[80%] lg:top-[25%] lg:left-[70%] lg:w-[430] lg:h-[300] xl:w-[480px] xl:left[100%] xl:h-[360px] w-full z-10 shadow-xl flex items-center justify-center">
              <img
                src={activeTabData.image}
                alt={activeTabData.title}
                className="w-full h-full object-cover rounded-lg shadow-2xl"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TabComponent;
