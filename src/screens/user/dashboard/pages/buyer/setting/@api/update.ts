import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";

// Fetch profile details for the logged-in user
export const getProfileDetails = async () => {
  try {
    const res = await formDataClient.get(apiRoutes().buyer.settings.fetchProfileDetails);
    return res.data;
  } catch (err) {
    console.error("API getProfileDetails error:", err);
    throw err;
  }
};

// Permanently delete the user account
export const deleteAccount = async () => {
  try {
    const res = await formDataClient.delete(apiRoutes().buyer.settings.deleteAccount);
    return res.data;
  } catch (err) {
    console.error("API deleteAccount error:", err);
    throw err;
  }
};

// Upload and update the user's profile image
export const updateProfileImage = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append("assets", file);

    const res = await formDataClient.post(
      apiRoutes().buyer.settings.updateProfileImage,
      formData
    );

    return res.data;
  } catch (err) {
    console.error("API updateProfileImage error:", err);
    throw err;
  }
};

// Update profile details such as name, role, country, and address
export const updateProfileDetails = async (data: {
  name: string;
  role: string;
  country: string;
  address: string;
}) => {
  try {
    const res = await formDataClient.patch(
      apiRoutes().buyer.settings.updateProfileDetails,
      data
    );

    return res.data;
  } catch (err) {
    console.error("API updateProfileDetails error:", err);
    throw err;
  }
};

// Change the user password using old and new credentials
export const changeUserPassword = async (data: {
  old_password: string;
  new_password: string;
  confirm_password: string;
}) => {
  try {
    const res = await formDataClient.post(
      apiRoutes().buyer.settings.changePassword,
      data
    );

    return res.data;
  } catch (err) {
    console.error("API changeUserPassword error:", err);
    throw err;
  }
};
