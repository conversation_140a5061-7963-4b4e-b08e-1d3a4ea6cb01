"use client";

import ProductCard from "@/components/elements/card/product";
import CategoryCard from "./card";
import { Button } from "@/components/ui/button";
import ProductSlider from "./slider";
import { useState } from "react";

const Products = () => {
  // Category data structure with key-value pairs
  const [categories] = useState({
    keyboards: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Keyboards"
    },
    handheldGame: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Handheld Game"
    },
    stylusPens: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Stylus Pens"
    },
    keyboardMouse: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Keyboard Mouse"
    },
    usbHubs: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "USB Hubs"
    },
    otherProducts: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Other Products"
    },
    gamingOnEar: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Gaming On-Ear"
    },
    otherGame: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Other Game"
    },
    smartRings: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Smart Rings"
    },
    powerBanks: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Power Banks"
    },
    otherProduct: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Other Products"
    },
    otherProduct1: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Other Products"
    }, otherProduct2: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Other Products"
    },
    otherProduct3: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Other Products"
    }, otherProduct4: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Other Products"
    },
    otherProduct5: {
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      label: "Other Products"
    },
  });

  return (
    <div className="flex justify-center w-full mt-7 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-screen-2xl 2xl:max-w-[90%]"> {/* Changed max-w-screen-xl to max-w-screen-2xl */}
        <div className="w-full bg-white rounded-lg shadow-lg p-4 border border-gray-100">
          <div className="mb-4 px-1">
            <h3 className="text-base font-bold text-gray-800">Source by Categories</h3>
          </div>
          <div className="w-full">
            <ProductSlider>
              {Object.entries(categories).map(([key, category]) => (
                <CategoryCard 
                  key={key} 
                  image={category.image} 
                  label={category.label} 
                />
              ))}
            </ProductSlider>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Products;
