"use client";

import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import classNames from "classnames";
import { Check } from "lucide-react";
import * as React from "react";
import InputLabel from "../components/label";

const CheckboxInput = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={classNames(
      "checkbox-input z-0 relative h-4 w-4 shrink-0 rounded-sm border shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=unchecked]:border-black data-[state=checked]:border-main data-[state=checked]:bg-main data-[state=checked]:text-white transition-all",
      className
    )}
    {...props}
  >
    <CheckboxPrimitive.Indicator
      className={classNames("flex items-center justify-center text-current")}
    >
      <Check className="check-icon h-3.5 w-3.5 z-10" />
    </CheckboxPrimitive.Indicator>
    {/* <Check className="uncheck-icon z-0 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 h-3.5 w-3.5" /> */}
  </CheckboxPrimitive.Root>
));

CheckboxInput.displayName = "CheckboxInput"

interface CheckboxChangeEvent {
  target: { value: boolean; name: string };
}

interface CheckboxType
  extends Omit<
    React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>,
    "onChange"
  > {
  containerClassName?: string;
  labelClassName?: string;
  label?: string | React.ReactNode;
  name?: string;
  onChange?: (e: CheckboxChangeEvent) => void;
}

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  CheckboxType
>(
  (
    {
      onChange,
      name = "checkbox",
      label,
      labelClassName,
      containerClassName,
      id,
      ...props
    },
    ref
  ) => {
    const uniqueId = React.useId();

    const handleChange = (value: boolean) => {
      try {
        if (onChange) {
          onChange({ target: { value, name } });
        }
      } catch (error) {
        console.error(error);
      }
    };

    return (
      <label

        htmlFor={id ?? uniqueId}
        className={classNames(
          "checkbox flex items-center",
          Boolean(label) && "gap-3",
          containerClassName
        )}
      >
        <CheckboxInput
          id={id ?? uniqueId}
          ref={ref}
          onCheckedChange={handleChange}
          {...props}
        />
        {label && (
          <InputLabel htmlFor={id ?? uniqueId} className={labelClassName}>
            {label}
          </InputLabel>
        )}
      </label>
    );
  }
);

Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
export type { CheckboxChangeEvent, CheckboxType };
