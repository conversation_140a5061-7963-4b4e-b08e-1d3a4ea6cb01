"use client";

import ListingAd from "@/components/layout/ads/listing";
import { Skeleton } from "@/components/ui/loader/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import useHashLink from "@/lib/hooks/useHashLink";
import useScrolled from "@/lib/hooks/useScrolled";
import classNames from "classnames";
import { Fragment, lazy, Suspense, useEffect, useRef } from "react";
import { ErrorBoundary } from "react-error-boundary";

const TabsData = [
  {
    title: "About Company",
    path: "company",
    component: lazy(() => import("./about")),
  },
  {
    path: "company",
    component: lazy(() => import("./photos")),
  },
  {
    title: "products & Services",
    path: "products",
    component: lazy(() => import("./products")),
  },
  {
    title: "Portfolio/Case Studies",
    path: "portfolios",
    component: lazy(() => import("./portfolios")),
  },
  {
    title: "Clientele",
    path: "clientele",
    component: lazy(() => import("./clientele")),
  },
  {
    title: "Key Features",
    path: "features",
    component: lazy(() => import("./features")),
  },
  {
    title: "Key Features",
    path: "features",
    hideTab: true,
    ad: true,
    component: lazy(() => import("@/components/layout/ads/banner")),
  },
  {
    title: "Reviews",
    path: "reviews",
    component: lazy(() => import("./reviews")),
  },
  {
    title: "Contact",
    path: "contact",
    hideTab: true,
    component: lazy(() => import("./contact")),
  },
  {
    title: "Get a Quote",
    path: "quote",
    component: lazy(() => import("./quote")),
  },
  {
    title: "Related Companies",
    path: "related",
    hideTab: true,
    component: lazy(() => import("./related")),
  },
];

const CompanyInformation = () => {
  const sectionRefs = useRef<Record<string, HTMLElement>>({});

  const isScrolled = useScrolled();
  const initialActive = TabsData?.[0]?.path;
  const [active, setActive] = useHashLink({ initialState: initialActive });

  useEffect(() => {
    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.id;
          setActive(sectionId);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, {
      root: null,
      rootMargin: "10px",
      threshold: 0.9,
    });

    Object.values(sectionRefs.current).forEach((section) =>
      observer.observe(section)
    );

    return () => observer.disconnect();
  }, [setActive]);

  return (
    <div className="mb-8">
      <div
        className={classNames(
          "bg-white border-b border-stone-300 mb-10 hidden md:block",
          isScrolled &&
            "shadow-md sticky top-header-sm z-20 sm:top-header-md md:top-header"
        )}
      >
        <div className="container pt-1.5">
          <Tabs
            defaultValue={initialActive}
            value={active ?? undefined}
            className="overflow-auto no-scrollbar py-1.5"
          >
            <TabsList className="flex-nowrap">
              {TabsData?.map((tab, index) => {
                // condition changed for tabs title view above
                if (!tab?.title || tab?.hideTab) return null;
                return (
                  <a key={index} href={`#${tab?.path}`}>
                    <TabsTrigger
                      value={tab?.path}
                      className="flex-1 capitalize before:data-[state=active]:-bottom-2.5"
                    >
                      {tab?.title}
                    </TabsTrigger>
                  </a>
                );
              })}
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="relative p-0 sm:container">
        <div className="grid grid-cols-1 lg:grid-cols-6 items-start gap-6 w-full max-w-full">
          <div className="col-span-5 flex flex-col items-start gap-6 w-full max-w-full">
            {TabsData?.map((tab, index) => {
              return (
                <Fragment key={index}>
                  <ErrorBoundary fallback={<div>Failed to load section</div>}>
                    <Suspense fallback={<Skeleton className="w-full h-44" />}>
                      <section
                        id={tab.path}
                        ref={(el) => {
                          if (el) sectionRefs.current[tab.path] = el;
                        }}
                        className={classNames(
                          "w-full sm:scroll-mt-44 sm:shadow-box rounded-md",
                          {
                            "bg-white": ["company"]?.includes(tab?.path),
                            "bg-[#FAFAFA]": [
                              "products",
                              "features",
                              "reviews",
                              "contact",
                              "related",
                              "clientele",
                              "portfolios"
                            ]?.includes(tab?.path),
                            "px-6 md:px-8 py-7": !tab?.ad,
                            "bg-gradient-to-l from-[#FFF5F6] via-[#FDFDFD]  to-[#FFFAF4]":
                              ["quote"]?.includes(tab?.path),
                          }
                        )}
                      >
                        <tab.component />
                      </section>
                    </Suspense>
                  </ErrorBoundary>
                </Fragment>
              );
            })}
          </div>
          <ListingAd className="!top-44" />
        </div>
      </div>
    </div>
  );
};

export default CompanyInformation;
