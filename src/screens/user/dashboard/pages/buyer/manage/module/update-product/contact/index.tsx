'use client';

import { Badge } from "@/components/ui/badge";
import Input<PERSON>abel from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import { RootDispatch } from "@/redux/store";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateRFQDetailsSelector } from "../../../@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/manage/@redux/slice";
import { TextEditor } from "@/components/ui/input/texteditor";

const Contact = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(updateRFQDetailsSelector);

  const CONTACT_INFO_FIELDS = ["name", "email", "phone", "companyName", "notes", "phoneCode"];

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};

      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values?.[e?.target?.name])
          ? Array.from(values?.[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        const fieldName = e?.target?.name;
        const fieldValue = e?.target?.value;

        if (CONTACT_INFO_FIELDS.includes(fieldName)) {
          // Ensure contactInfo is always an object
          payload.contactInfo = {
            ...(typeof values?.contactInfo === "object" && values?.contactInfo !== null
              ? values.contactInfo
              : {}),
            [fieldName]: fieldValue
          };
        } else {
          payload[fieldName] = fieldValue;
        }
      }

      dispatch(Actions.setRFQDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  // Fixed handlePhoneCodeChange function with type assertion
  const handlePhoneCodeChange = (e: any) => {
    try {
      const payload = {
        contactInfo: {
          ...(typeof values?.contactInfo === "object" && values?.contactInfo !== null
            ? values.contactInfo
            : {}),
          phoneCode: e.target.value
        }
      };
      // Type assertion to bypass TypeScript error
      dispatch(Actions.setRFQDetails(payload as any));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="flex items-center gap-1">
          <Input
            type="text"
            name="name"
            label="Contact Name"
            onChange={handleChange}
            value={values?.contactInfo?.name ?? ""}
            placeholder="Representative's full name"
            containerClassName="flex-1"
          />
        </div>

        <Input
          type="text"
          name="email"
          label="Email Address"
          onChange={handleChange}
          value={values?.contactInfo?.email ?? ""}
          placeholder="Business email ID"
          containerClassName="flex-1"
        />

        <div className="flex items-center gap-1">
          <div>
            <Input
              className="w-full"
              label="Phone Number"
              placeholder="Phone number"
              name="phone"
              value={values?.contactInfo?.phone ?? ""}
              onChange={handleChange}
              countrySelector={{
                value: values?.contactInfo?.phoneCode ?? "",
                onChange: handlePhoneCodeChange
              }}
            />
          </div>
        </div>

        <Input
          type="text"
          name="companyName"
          label="Company Name"
          onChange={handleChange}
          value={values?.contactInfo?.companyName ?? ""}
          placeholder="Company Name"
          containerClassName="flex-1"
        />
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6">
        <TextEditor
          name="notes"
          value={values?.contactInfo?.notes ?? ""}
          onChange={handleChange}
          containerClassName="flex-1"
          className="flex-1 rounded-md"
          key={'contact-info-notes'} // <--- Updated key
          label="Additional Notes"
        />
      </div>
    </>
  );
};

export default Contact;