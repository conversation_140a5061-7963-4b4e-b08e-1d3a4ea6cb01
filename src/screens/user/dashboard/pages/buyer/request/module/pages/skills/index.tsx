'use client';

import React, { useState } from 'react';
import { MultiSelect } from "@/components/ui/input/multi-select";

interface SkillsDetails {
  skills: string[];
}

interface SkillsProps {
  onSkillsChange?: (details: SkillsDetails) => void;
  initialDetails?: SkillsDetails;
}

const SkillsRequirementInput: React.FC<SkillsProps> = ({
  onSkillsChange,
  initialDetails = {
    skills: ["iso", "fda"]
  }
}) => {
  const [skillsDetails, setSkillsDetails] = useState<SkillsDetails>(initialDetails);
  
  const skillOptions = [
    { label: "ISO", value: "iso" },
    { label: "FDA", value: "fda" },
    { label: "GMP", value: "gmp" },
    { label: "GDP", value: "gdp" },
    { label: "CE", value: "ce" },
    { label: "HACCP", value: "haccp" },
    { label: "OSHA", value: "osha" },
    { label: "Six Sigma", value: "six_sigma" },
    { label: "Lean Manufacturing", value: "lean_manufacturing" },
    { label: "Quality Control", value: "quality_control" }
  ];

  const handleSkillsChange = (event: any) => {
    const selectedSkills = event.target.value;
    const updatedDetails = {
      ...skillsDetails,
      skills: selectedSkills
    };
    setSkillsDetails(updatedDetails);
    if (onSkillsChange) {
      onSkillsChange(updatedDetails);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col  mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">What skills are required</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <MultiSelect
            name="skills"
            options={skillOptions}
            placeholder="Select required skills"
            onValueChange={handleSkillsChange}
            defaultValue={skillsDetails.skills}
          />
        </div>
      </div>
    </div>
  );
};

export default SkillsRequirementInput;