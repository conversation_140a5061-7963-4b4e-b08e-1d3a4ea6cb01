import Branding from "@/components/layout/header/components/branding";
import React from "react";

// TODO
// Main FOOTER
const WebsiteFooter = () => {
  return (
    <footer>
      <div className="container bg-[#F4F4F4]">
        <div className="flex items-start gap-4"></div>
      </div>
      <div className="container bg-white">
        <div className="flex items-center justify-between  gap-4 w-full text-xs">
          <span>All Rights Reserved (Terms of Use)</span>
          <div className="flex items-center gap-1">
            <span>Developed and Managed by Aalayana.com</span>
            <Branding className="sm:!max-w-20 sm:!min-w-20 !w-20" />
          </div>
        </div>
      </div>
    </footer>
  );
};

export default WebsiteFooter;
