'use client';

import { Input } from "@/components/ui/input/text";
import React, { useState } from 'react';

interface ServiceDetailsProps {
  onServiceDetailsChange?: (details: ServiceDetails) => void;
  initialDetails?: ServiceDetails;
}

interface ServiceDetails {
  title: string;
}

const ServiceDetailsInput: React.FC<ServiceDetailsProps> = ({
  onServiceDetailsChange,
  initialDetails = { title: "" }
}) => {
  const [serviceDetails, setServiceDetails] = useState(initialDetails);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const updatedDetails = { ...serviceDetails, [name]: value };
    setServiceDetails(updatedDetails);
    if (onServiceDetailsChange) {
      onServiceDetailsChange(updatedDetails);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col  mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">What service do you need?</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <Input
            id="title"
            name="title"
            value={serviceDetails.title}
            onChange={handleInputChange}
            placeholder="Enter service title"
          />
        </div>
      </div>
    </div>
  );
};

export default ServiceDetailsInput;