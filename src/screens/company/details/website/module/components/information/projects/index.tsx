"use client";

import PortfolioCard from "@/components/elements/card/portfolio";
import { PortfolioDetailsModal } from "@/components/elements/card/portfolio/details";
import { portfolioCardData } from "@/screens/company/details/landing/components/information/portfolios";
import { useState } from "react";

const ProjectsCompany = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<{
    title: string;
    content: string;
    images: string[];
  } | null>(null);

  const openModal = (card: {
    title: string;
    content: string;
    images: string[];
  }) => {
    setSelectedCard(card);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedCard(null);
  };

  return (
    <div className="relative container my-12">
      <div className="grid grid-cols-1 gap-6">
        <h3 className="font-bold">Projects / Case Studies</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...portfolioCardData, ...portfolioCardData]?.map((card, index) => (
            <PortfolioCard
              key={index}
              image={card.image}
              title={card.title}
              content={card.content}
              labels={card.labels}
              onClick={() => openModal(card)}
              onReadMore={() => openModal(card)}
            />
          ))}
        </div>

        {isModalOpen && selectedCard && (
          <PortfolioDetailsModal
            title={selectedCard.title}
            content={selectedCard.content}
            images={selectedCard.images}
            onClose={closeModal}
            cardData={portfolioCardData}
          />
        )}
      </div>
    </div>
  );
};

export default ProjectsCompany;
