import {
  Carousel,
  CarouselContent,
  Carousel<PERSON>tem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ReactNode } from "react";

const ProductSlider = ({ children }: { children: ReactNode }) => {
  return (
    <div className="relative group">
      <Carousel
        opts={{
          align: "start",
          loop: false,
          slidesToScroll: 1,
        }}
        className="w-full"
      >
        <CarouselContent className="-ml-1 md:-ml-4">
          {Array.from({ length: 50 }).map((_, index) => (
            <CarouselItem
              key={index}
              className="pl-1 md:pl-4 basis-1/2 md:basis-[40%] lg:basis-[40%]"
            >
              <div className="p-1">
                {children}
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="absolute -left-12 top-1/2 -translate-y-1/2 shadow-xl bg-white border border-gray-200 !opacity-0 group-hover:!opacity-100 transition-opacity duration-200 z-10 h-12 w-12" />
        <CarouselNext className="absolute -right-12 top-1/2 -translate-y-1/2 shadow-xl bg-white border border-gray-200 !opacity-0 group-hover:!opacity-100 transition-opacity duration-200 z-10 h-12 w-12" />
      </Carousel>
    </div>
  );
};

export default ProductSlider;