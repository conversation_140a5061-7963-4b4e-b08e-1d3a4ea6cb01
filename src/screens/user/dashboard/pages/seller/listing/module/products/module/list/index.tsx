"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import { Loader } from "@/components/ui/loader";
import { RootDispatch } from "@/redux/store";
import {
  productDetails,
  productsList,
} from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import {
  deleteProduct,
  updateProductDetails,
} from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/thunk";
import { AlertCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";

interface ProductListProps {
  products: any[];
}

const ProductList: React.FC<ProductListProps> = ({ products = [] }) => {
  const router = useRouter();
  const { loading } = useSelector(productsList);
  const { data: productData, loading: productUpdateLoading } =
    useSelector(productDetails);
  const dispatch = useDispatch<RootDispatch>();

  const handleEdit = (product_id: string) => {
    router.push(`/dashboard/seller/listing/products/${product_id}`);
  };

  const handleDelete = (product_id: string) => {
    dispatch(deleteProduct(product_id));
  };

  const handleStatusUpdate = (status: string, product_id: string) => {
    const data = new FormData();
    data.append("product_status", status);

    dispatch(
      updateProductDetails({
        product_id,
        data,
      })
    );
  };

  if (loading) {
    return <Loader big center />;
  }

  return (
    <div className="space-y-4">
      {Array.isArray(products) && products?.length > 0 ? (
        <>
          <ListCardHeader />
          {products.map((product, index) => {
            if (
              productData &&
              productData?._id === product?._id &&
              productUpdateLoading
            ) {
              return <Loader key={index} big center />;
            }
            return (
              <ListCard
                key={product._id}
                image={product?.main_product_images?.[0]}
                title={product?.product_name}
                category={product?.category?.[0]?.name ?? "Category"}
                views={"345"}
                status={product?.product_status}
                onEdit={() => handleEdit(product._id)}
                onDelete={() => handleDelete(product._id)}
                onStatusChange={(status) =>
                  handleStatusUpdate(status, product._id)
                }
                statusOptions={[
                  { label: "Active", value: true },
                  { label: "Inactive", value: false },
                ]}
              />
            );
          })}
        </>
      ) : (
        <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
          <AlertCircle /> No Products Available
        </div>
      )}
    </div>
  );
};

export default ProductList;
