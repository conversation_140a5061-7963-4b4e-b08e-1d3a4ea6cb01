import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from "../@api/update";
import { Actions } from "./slice";

// Thunk to update business details - handles form data submission
export const updateBusinessDetails = createAsyncThunk<
  any,
  FormData,
  { state: RootState }
>(
  "businessDetails/updateBusinessDetails",
  async (formValue, { rejectWithValue }) => {
    try {
      const response = await API.addBusinessDetails(formValue);

      toast.success(response?.meta?.message || "Updated successfully", {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      return response;
    } catch (error: any) {
      console.error("API Error:", error);

      // Handle different types of errors
      let errorMessage = "Something went wrong";

      if (error.response) {
        errorMessage = error.response.data?.meta?.message || error.response.data?.message || errorMessage;
      } else if (error.request) {
        errorMessage = "No response from server";
      } else {
        errorMessage = error.message || errorMessage;
      }

      toast.error(errorMessage, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

// Thunk to get business details - simplified to just fetch data
export const getBusinessDetails = createAsyncThunk<any, void, { state: RootState }>(
  "businessDetails/getBusinessDetails",
  async (_, { rejectWithValue }) => {
    try {
      const response = await API.getBusinessDetails();

      if (!response) {
        return { data: {}, meta: { message: "No business data returned" } };
      }

      return {
        data: response,
        meta: { message: "Business details fetched successfully" }
      };

    } catch (error: any) {
      console.error("getBusinessDetails error:", error);

      if (error.response) {
        console.error("API error response:", error.response.status, error.response.data);
      }

      toast.error(error?.meta?.message || "Failed to load business details", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue({
        data: {},
        meta: error?.response?.data || { message: error?.message || "Failed to fetch business details" }
      });
    }
  }
);

export const getSubSubCategories = createAsyncThunk<any, void, { state: RootState }>(
  "categories/getSubSubCategories",
  async (_, { rejectWithValue }) => {
    try {
      const response = await API.get_sub_sub_Categories();
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const companyEmailVerifyInitiate = createAsyncThunk<
  any,
  { email: string; },
  { state: RootState }
>(
  "emailVerificationInitiate/companyEmailVerifyInitiate",
  async (payload, { rejectWithValue }) => {
    try {
      const response = await API.sendEmailVerification(payload);


      toast.success(response?.meta?.message || "Business verification email sent succesfully.", {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Invalid email address", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const companyEmailVerify = createAsyncThunk<
  any,
  { email_otp: string },
  { state: RootState }
>(
  "emailVerify/companyEmailVerify",
  async (payload, { rejectWithValue }) => {
    try {
      const response = await API.verifyEmailOtp(payload);

      toast.success(response?.meta?.message || "Business email verified succesfully.", {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message || "Invalid otp. Please provide valid otp.", {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);



export const thunks = {
  updateBusinessDetails,
  getBusinessDetails,
  getSubSubCategories,
  companyEmailVerifyInitiate,
  companyEmailVerify
};
