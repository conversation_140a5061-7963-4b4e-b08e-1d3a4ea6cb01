"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import CountrySelect from "@/components/ui/input/country-select";
import { InputTargetType } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { Phone } from "lucide-react";
import { ChangeEvent, useState } from "react";
import { MdOutlineMail } from "react-icons/md";
import { SlLocationPin } from "react-icons/sl";

const ContactCompany = () => {
  const [values, setValues] = useState<any>({});

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | InputTargetType
  ) => {
    try {
      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="relative container my-12">
      <div className="flex flex-wrap sm:flex-nowrap items-start gap-6 [&>*]:w-full">
        <div className="flex flex-col items-start gap-6 [&>*]:w-full">
          <h3 className="font-bold">Contact Information</h3>

          <div className="hidden md:flex flex-col items-start gap-3 [&>*]:w-full">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-white shadow flex items-center justify-center p-2">
                <Phone size={20} />
              </div>
              <div className="flex items-start gap-2.5">
                <span className="font-normal text-base">+91-1234567890</span>
                <button className="font-xs text-main">View Number</button>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="rounded-full bg-white shadow flex items-center justify-center p-2">
                <MdOutlineMail size={20} />
              </div>
              <div className="flex items-start gap-2.5">
                <span className="font-normal text-base"><EMAIL></span>
                <button className="font-xs text-main">View Email</button>
              </div>
            </div>

            <div className="sm:order-2 flex items-center gap-3">
              <div className="rounded-full bg-white shadow flex items-center justify-center p-2">
                <SlLocationPin size={20} />
              </div>
              <span className="font-normal text-sm">
                1234 Elmwood Avenue, Suite 567, Downtown Business District,
                Springfield, IL 62704, United States
              </span>
            </div>
          </div>

          <div className="flex-1 overflow-hidden mt-2">
            <iframe
              title="map"
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d6139551.528322523!2d12.712159999999999!3d41.29085!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12d4fe82448dd203%3A0xe22cf55c24635e6f!2sItaly!5e0!3m2!1sen!2sin!4v1737648310290!5m2!1sen!2sin"
              loading="lazy"
              className="border-none h-full min-h-48 w-full max-w-[80%]"
              allowFullScreen={false}
              referrerPolicy="no-referrer-when-downgrade"
            ></iframe>
          </div>
        </div>

        <div className="bg-white shadow-lg h-full p-8 rounded-md">
          <h3 className="font-bold text-2xl mb-6">Get in Touch with Us</h3>

          <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
            <Input
              type="text"
              name="name"
              label="Full Name"
              onChange={handleChange}
              value={values?.name}
              placeholder="Enter your full name"
              labelClassName="mb-1 ml-5"
            />

            <Input
              type="text"
              name="phone"
              label="Phone"
              onChange={handleChange}
              value={values?.phone}
              labelClassName="mb-1 ml-5"
              placeholder="Phone Number"
              countrySelector={{
                defaultValue: "+91",
                onValueChange: (e) => console.log({ e }),
              }}
            />

            <Input
              type="text"
              name="company_name"
              label="Company Name"
              onChange={handleChange}
              value={values?.company_name}
              labelClassName="mb-1 ml-5"
              placeholder="Enter your company name"
            />

            <CountrySelect
              name="country"
              label="Country"
              placeholder="Select Country"
              onChange={handleChange}
              value={values.country}
              labelClassName="mb-1 ml-5"
            />
          </div>
          <Textarea
            name="message"
            label="Inquiry Message"
            onChange={handleChange}
            value={values?.message}
            placeholder="Enter your phone number"
            className="min-h-20"
            labelClassName="mb-1 ml-5"
            wrapperClassName="!rounded-lg"
            containerClassName="w-full"
          />

          <Button
            type="submit"
            variant="main-revert"
            className="w-full mt-6 !rounded-md"
          >
            Send Enquiry
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ContactCompany;
