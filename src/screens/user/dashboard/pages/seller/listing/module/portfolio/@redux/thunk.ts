import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from "../@api/update";
import { Actions } from "./slice";

export const fetchPortfolioList = createAsyncThunk<
  any,
  { searchTerm?: string; page?: string; limit?: string } | undefined,
  { state: RootState }
>("portfolioList", async (pagination, { rejectWithValue, dispatch }) => {
  try {
    const response: any = await API.fetchPortfolioList({
      searchTerm: pagination?.searchTerm,
      page: pagination?.page,
      limit: pagination?.limit,
    });

    window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
    dispatch(Actions.setCurrentPage(String(response?.meta?.pagination?.page)));

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const fetchPortfolio = createAsyncThunk<
  any,
  string,
  { state: RootState }
>(
  "portfolioDetails/fetchPortfolio",
  async (portfolio_id, { rejectWithValue }) => {
    try {
      const response = await API.fetchPortfolio(portfolio_id);
      window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const addUpdatePortfolio = createAsyncThunk<
  any,
  { portfolio_id?: string; data: FormData },
  { state: RootState }
>(
  "portfolioDetails/addUpdatePortfolio",
  async ({ portfolio_id, data }, { rejectWithValue, dispatch }) => {
    try {
      const response: any = await API.addUpdatePortfolio(data, portfolio_id);
      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });
      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const deletePortfolio = createAsyncThunk<
  any,
  string,
  { state: RootState }
>("deletePortfolio", async (portfolio_id, { rejectWithValue, dispatch }) => {
  try {
    const response: any = await API.deletePortfolio(portfolio_id);
    toast.success(response?.meta?.message as string, {
      classNames: {
        toast: "!bg-green-500 !text-white",
      },
    });
    dispatch(fetchPortfolioList());
    return response;
  } catch (error: any) {
    toast.error(error?.meta?.message as string, {
      classNames: {
        toast: "!bg-red-500 !text-white",
      },
    });

    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const thunks = {
  fetchPortfolioList,
  fetchPortfolio,
  addUpdatePortfolio,
  deletePortfolio,
};
