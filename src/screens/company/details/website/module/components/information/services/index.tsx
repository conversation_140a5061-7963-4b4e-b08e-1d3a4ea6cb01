import ServiceListCard from "@/components/elements/card/service/list-card";
import { ListingData } from "@/constants/listing";
import RequestServiceModal from "./inquiry";

const ServicesCompany = ({ category_id }: { category_id?: string }) => {
  return (
    <div className="relative container my-12">
      <div className="grid grid-cols-1 gap-6">
        <h3 className="font-bold">
          {" "}
          All Services {category_id && "by" + category_id}
        </h3>

        <div className="flex flex-col items-start gap-6">
          {ListingData.map((service, i) => (
            <ServiceListCard
              key={i}
              service={service}
              inquiryModal={<RequestServiceModal />}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ServicesCompany;
