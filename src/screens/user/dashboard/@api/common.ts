import { apiRoutes } from "@/services/api/routes";
import privateApiClient from "@/services/axios/private-api-client";

import { toast } from "sonner";

export const deleteMedia = async (media_id: string) => {
  try {
    const res: any = await privateApiClient.delete(
      apiRoutes().user.seller.common.deleteMedia,
      {
        params: { mid: media_id },
      }
    );

    toast.success(res?.meta?.message as string, {
      classNames: {
        toast: "!bg-green-500 !text-white",
      },
    });

    return res;
  } catch (error: any) {
    toast.error(error?.meta?.message as string, {
      classNames: {
        toast: "!bg-red-500 !text-white",
      },
    });

    return error;
  }
};
