import { Checkbox } from "@/components/ui/input/checkbox";
import { ValueUniqueParam } from "@/types/shared";
import classNames from "classnames";
import {
  createElement,
  FC,
  isValidElement,
  MouseEvent,
  ReactNode,
  useMemo,
} from "react";
import { DropdownListDataType } from "../types";

interface ListItemType {
  className?: string;
  menu: DropdownListDataType;
  value?: string | DropdownListDataType | DropdownListDataType[];
  highlightSelected?: boolean;
  checkbox?: boolean;
  iconClassName?: string;
  onSelect?: (data: any) => void;
  optionKeys?: {
    label?: string;
    value?: string;
    icon?: string;
  };
  valueUniqueParam?: ValueUniqueParam;
  getLabel: (option: DropdownListDataType) => string;
}

const renderIcon = (icon: any) => {
  try {
    const IconComponent = icon;
    return createElement(IconComponent, null);
  } catch {
    return "";
  }
};

const ListItem: FC<ListItemType> = ({
  menu,
  className,
  checkbox,
  iconClassName,
  onSelect,
  value,
  optionKeys,
  valueUniqueParam,
  getLabel,
}) => {
  const handleEvent = (e: MouseEvent<HTMLDivElement>) => {
    try {
      e?.preventDefault();
      e?.stopPropagation();

      if (menu?.event) {
        menu?.event(menu);
      }

      if (onSelect) {
        onSelect(menu);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const isSelected = useMemo((): boolean => {
    try {
      if (Array.isArray(value)) {
        return Boolean(
          value?.find(
            (item) =>
              item?.[valueUniqueParam ?? "label"] ===
              menu?.[valueUniqueParam ?? "label"]
          )
        );
      }

      if (typeof value === "object") {
        return (
          value?.[valueUniqueParam ?? "label"] ===
          menu?.[valueUniqueParam ?? "label"]
        );
      }

      return value === menu?.[valueUniqueParam ?? "label"];
    } catch {
      return false;
    }
  }, [value, menu, valueUniqueParam]);

  const ListLabel = () => (
    <div className="flex items-center gap-2">
      {menu?.icon && (
        <div
          className={classNames(
            "menu-item-icon-container relative max-w-max [&>svg]:w-4 [&>svg]:h-4",
            iconClassName
          )}
        >
          {typeof menu?.icon === "string" ? (
            <img
              src={menu?.icon ? menu?.icon : ""}
              alt={menu?.icon ?? "menu-icon"}
              className="menu-item-icon w-4 h-4 object-contain"
            />
          ) : isValidElement(menu?.icon) ? (
            <>{menu?.icon}</>
          ) : (
            typeof menu?.icon === "object" && (
              <>{renderIcon(menu?.icon as ReactNode)}</>
            )
          )}
        </div>
      )}
      {getLabel(menu) && (
        <div className="menu-item-label flex-1 capitalize text-sm whitespace-nowrap">
          {getLabel(menu)}
        </div>
      )}
    </div>
  );

  return (
    <div
      title={getLabel(menu)}
      role="button"
      className={classNames(
        "menu-item w-full flex items-center gap-2 px-3 hover:bg-slate-100 cursor-pointer last-of-type:mb-3 py-1.5",
        isSelected && "bg-main/10 hover:!bg-main/10",
        className
      )}
      onClick={(e) => handleEvent(e)}
    >
      {checkbox ? (
        <Checkbox
          id={getLabel(menu)}
          label={<ListLabel />}
          checked={Boolean(menu?.checked) || isSelected}
        />
      ) : (
        <ListLabel />
      )}
    </div>
  );
};

export default ListItem;
