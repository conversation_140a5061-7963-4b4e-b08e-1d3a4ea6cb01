import { createSlice, PayloadAction, AsyncThunk } from "@reduxjs/toolkit";
import { APIResponseType, createThunkCase } from "@/redux/helper/thunk";
import {
  getProfileDetails,
  deleteAccount,
  updateProfileImage,
  updateProfileDetails,
  changePassword,
} from "./thunk";

// Define the shape of the profile state
interface ProfileStateType {
  getProfileDetails: APIResponseType;
  deleteAccount: APIResponseType;
  updateProfileImage: APIResponseType;
  updateProfileDetails: APIResponseType;
  changePassword: APIResponseType;
}

// Initial API state for each action
const APIState: ProfileStateType = {
 getProfileDetails: {
   data: null,
   loading: false,
   meta: null,
 },
  deleteAccount: {
    data: null,
    loading: false,
    meta: null,
  },
  updateProfileImage: {
    data: null,
    loading: false,
    meta: null,
  },
  updateProfileDetails: {
    data: null,
    loading: false,
    meta: null,
  },
   changePassword: {
     data: null,
     loading: false,
     meta: null,
   },
};

// Set the initial state of the slice
const initialState = {
  ...APIState,
} satisfies ProfileStateType as ProfileStateType;

// Create user buyer settings slice
export const slice = createSlice({
  name: "user_buyer_settings",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // Register each async thunk using helper
    const thunks = {
      getProfileDetails,
      deleteAccount,
      updateProfileImage,
      updateProfileDetails,
      changePassword,
    };

    Object.entries(thunks).forEach(([_, thunk]) => {
      createThunkCase(builder, thunk as AsyncThunk<any, any, any>, APIState);
    });
  },
});

export const Actions = slice.actions;
export default slice.reducer;
