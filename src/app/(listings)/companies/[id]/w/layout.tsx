"use client";

import BreadcrumbRender from "@/components/ui/breadcrumb/render";
import { useDOMOperator } from "@/lib/hooks/useDOMOperator";
import {
  CompanyInformationTabs,
  TabsData,
} from "@/screens/company/website/module";
import WebsiteFooter from "@/screens/company/website/module/components/footer";
import CompanyProfile from "@/screens/company/website/module/components/profile";
import { useParams, usePathname } from "next/navigation";

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const params = useParams();
  const company_id = params.id as string;

  useDOMOperator({
    selectors: ["#global-header", "#global-footer"],
    operation: "hide",
  });
  console.log({ pathname });

  return (
    <>
      <CompanyProfile />
      <CompanyInformationTabs company_id={company_id} />

      {!(
        pathname?.toLowerCase()?.endsWith("w") ||
        pathname?.toLowerCase()?.endsWith("w/")
      ) && (
        <div className="container my-6">
          <BreadcrumbRender
            className="mb-4 font-medium"
            data={[
              {
                title: "Home",
                pathname: pathname
                  .split("/")
                  .filter(Boolean)
                  .slice(0, 2)
                  .join("/"),
              },
              {
                title:
                  TabsData(company_id).find((tab) => tab.path === pathname)
                    ?.title ?? pathname.split("/")[3],
                pathname: pathname,
              },
            ]}
          />
        </div>
      )}

      {children}

      <WebsiteFooter />
    </>
  );
}
