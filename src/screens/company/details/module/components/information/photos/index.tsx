"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import classNames from "classnames";
import { ArrowDown, ArrowUp } from "lucide-react";
import { useState } from "react";

const PhotosData = {
  description: `
  <ul>
    <li>Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren </li>
    <li>Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren</li>
    <li>Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren </li>
    <li>Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren</li>
    <li>Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren</li>
    <li>Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren</li>
  </ul>
`,
  photos: [
    "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
    "https://c4.wallpaperflare.com/wallpaper/229/242/345/moscow-city-2017-art-irbis-production-wallpaper-thumb.jpg",
    "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
    "https://c4.wallpaperflare.com/wallpaper/229/242/345/moscow-city-2017-art-irbis-production-wallpaper-thumb.jpg",
    "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
    "https://c4.wallpaperflare.com/wallpaper/229/242/345/moscow-city-2017-art-irbis-production-wallpaper-thumb.jpg",
    "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
    "https://c4.wallpaperflare.com/wallpaper/229/242/345/moscow-city-2017-art-irbis-production-wallpaper-thumb.jpg",
    "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
    "https://c4.wallpaperflare.com/wallpaper/229/242/345/moscow-city-2017-art-irbis-production-wallpaper-thumb.jpg",
  ],
};

const CompanyPhotos = () => {
  const [readFull, setReadFull] = useState<boolean>(false);

  return (
    <div className="flex flex-col items-start gap-5 [&>*]:w-full">
      <h3 className="font-bold">About Company</h3>

      {Array?.isArray(PhotosData?.photos) && (
        <Carousel
          opts={{
            align: "start",
          }}
          className="w-full static"
        >
          <CarouselContent className="-ml-6">
            {PhotosData?.photos?.map((photo, index) => (
              <CarouselItem
                key={index}
                className="pl-6 basis-auto sm:basis-1/2 hover:shadow-inner"
              >
                <div className="w-full h-full rounded-md overflow-hidden">
                  <img
                    src={photo}
                    alt="company-photo"
                    className="w-full h-full object-cover"
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="-left-6 shadow-xl bg-white" />
          <CarouselNext className="-right-6 shadow-xl bg-white" />
        </Carousel>
      )}

      {PhotosData?.description && (
        <div className="relative text-xs font-normal">
          <div
            title={PhotosData?.description}
            className={classNames("transition-all", {
              "max-h-[150px] overflow-hidden": !readFull,
            })}
            dangerouslySetInnerHTML={{
              __html: PhotosData?.description
                .replace("<ul>", '<ul class="list-disc font-normal pl-5 mt-3">')
                .replace(/<li>/g, '<li class="mt-4 font-normal text-sm">'),
            }}
          />
          <div className="flex items-center justify-center mt-4">
            <button
              className="text-font hover:text-main text-xs hover:underline hover:drop-shadow flex items-center gap-1 capitalize [&>svg]:w-4 [&>svg]:h-4 w-max"
              onClick={() => setReadFull((prev) => !prev)}
            >
              {readFull ? "read less" : "read more"}
              {readFull ? <ArrowUp /> : <ArrowDown />}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyPhotos;
