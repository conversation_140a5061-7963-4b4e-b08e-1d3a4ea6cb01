import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { languages } from "@/constants/languages";
import { FaChevronUp } from "react-icons/fa6";

const FooterLanguage = () => {
  return (
    <div className="flex items-center flex-wrap gap-2">
      <Dropdown
        verticalAlign="top"
        content={<DropdownList data={languages} className="min-w-40 w-min" />}
        contentClassName="z-20"
      >
        <button className="text-xs py-1 px-1.5 hover:bg-slate-800 transition-all flex items-center justify-center gap-2">
          <img src={"/assets/footer/globe.svg"} alt="globe" className="w-6 h-6 object-contain" />
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">English</span>
            <FaChevronUp />
          </div>
        </button>
      </Dropdown>
    </div>
  );
};

export default FooterLanguage;
