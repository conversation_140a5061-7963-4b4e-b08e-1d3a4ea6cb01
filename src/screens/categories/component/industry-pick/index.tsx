"use client";

import React, { useState } from 'react';
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from '@/components/ui/carousel';

interface Category {
  id: number;
  title: string;
  image: string;
  alt: string;
}

const CrossIndustryPicks: React.FC = () => {
  const [categories] = useState<Category[]>([
    {
      id: 1,
      title: "DIY Computer",
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      alt: "Computer workstation with monitor and keyboard"
    },
    {
      id: 2,
      title: "Computer Input",
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      alt: "Close-up of hands typing on keyboard"
    },
    {
      id: 3,
      title: "Audio & Video",
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      alt: "Portable camera equipment"
    },
    {
      id: 4,
      title: "Cyber Cafe",
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      alt: "Person working in a gaming cafe environment"
    },
    {
      id: 5,
      title: "Gaming Accessories",
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      alt: "Gaming controller and accessories"
    },
    {
      id: 6,
      title: "Photo Studio",
      image: "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      alt: "Photography studio setup with lights and props"
    }
  ]);

  // Category item component to maintain consistent styling
  const CategoryItem = ({ category }: { category: Category }) => (
    <div className="flex flex-col items-center text-center">
      <div className="rounded-lg overflow-hidden mb-2 w-4/5 aspect-[3/3.5]">
        <img
          src={category.image}
          alt={category.alt}
          className="w-full h-full object-cover hover:opacity-90 transition-opacity"
        />
      </div>
      <div className="font-medium text-sm sm:text-base">{category.title}</div>
    </div>
  );

  return (
    <div className="flex justify-center mb-6 w-full mt-4 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-screen-2xl 2xl:max-w-[90%]">
        <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6 border border-gray-100">
          <div className="mb-4">
            <h3 className="text-base sm:text-lg font-bold text-gray-800">Cross-Industry Picks</h3>
          </div>

          {/* Desktop Grid - Hidden on small screens */}
          <div className="hidden md:grid grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category) => (
              <CategoryItem key={category.id} category={category} />
            ))}
          </div>

          {/* Mobile Carousel - Visible only on small screens */}
          <div className="block md:hidden relative">
            <Carousel
              opts={{
                align: "start",
                dragFree: true
              }}
              className="w-full"
            >
              <CarouselContent className="">
                {categories.map((category) => (
                  <CarouselItem 
                    key={category.id} 
                    className=" basis-[57%] sm:basis-1/3"
                  >
                    <CategoryItem category={category} />
                  </CarouselItem>
                ))}
              </CarouselContent>
              {/* <CarouselPrevious className="bg-white shadow-lg border border-gray-100 rounded-full h-10 w-10 left-0 focus:ring-1 focus:ring-gray-200 focus:outline-none sm:flex absolute" />
              <CarouselNext className="bg-white shadow-lg border border-gray-100 rounded-full h-10 w-10 right-0 focus:ring-1 focus:ring-gray-200 focus:outline-none sm:flex absolute" /> */}
            </Carousel>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CrossIndustryPicks;