import StackCard from "@/components/elements/card/stack";
import { <PERSON><PERSON> } from "@/components/ui/button";
import classNames from "classnames";
import { ReactNode } from "react";
import { IoReload } from "react-icons/io5";
import Listing from "./components/listing";
import Overview from "./components/overview";
import PaidServices from "./components/paid-services";
import PieReport from "./components/pie-report";
import ResponseTime from "./components/response-time";
import RfqReceived from "./components/rfq";
import WebsitePerformance from "./components/website";

const RfqData = [
  {
    icon: "/assets/pages/user/dashboard/insights/active_products.svg",
    title: "Active Products",
    value: "46443",
  },
  {
    icon: "/assets/pages/user/dashboard/insights/active_services.svg",
    title: "Active Services",
    value: "46464",
  },
  {
    icon: "/assets/pages/user/dashboard/insights/pending_rfq.svg",
    title: "Pending RFQ",
    value: "46464",
  },
  {
    icon: "/assets/pages/user/dashboard/insights/conversion_rate.svg",
    title: "Conversion Rate",
    value: "46464",
  },
];

const Dashboard = () => {
  return (
    <main className="my-6 grid grid-cols-5 gap-6">
      <div className="col-span-5">
        <div className="flex items-center gap-4 justify-between mb-6">
          <Title
            subText="Account Age (3 Years, 1 Month) | Last Login 21 March 2025"
            className="flex-1"
          >
            Overview
          </Title>
          <RefreshButton>Reload Data</RefreshButton>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-4 md:grid-cols-5 gap-6">
          <Overview className="col-span-2 md:col-span-3" />
          <PieReport className="col-span-2" />
        </div>
      </div>

      <div className="col-span-5">
        <Title className="mb-6">RFQs Received</Title>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <RfqReceived className="col-span-2" />
          <div className="col-span-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-6">
            {RfqData?.map((rfq, index) => {
              return (
                <StackCard
                  key={index}
                  mainText={rfq?.value}
                  subText={rfq?.title}
                  icon={rfq?.icon}
                  iconClassName="[&>svg]:!w-6 [&>svg]:!h-6"
                />
              );
            })}
          </div>
        </div>
      </div>
      <div className="col-span-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <PaidServices />
        <ResponseTime />
        <WebsitePerformance />
      </div>
      <div className="col-span-5">
        <div className="w-full flex items-center justify-between gap-4 mb-6">
          <Title>Listing Performance / Most Viewed Products/ Services</Title>
          <div className="flex items-center gap-1">
            <Button className="!rounded-md" variant="main-revert">
              Products
            </Button>
            <Button className="!rounded-md" variant="main">
              Services
            </Button>
          </div>
        </div>
        <Listing className="col-span-5" />
      </div>
    </main>
  );
};

const Title = ({
  className,
  children,
  subText,
}: {
  className?: string;
  children: ReactNode;
  subText?: string;
}) => (
  <div
    className={classNames(
      "w-full flex items-center flex-wrap gap-1.5",
      className
    )}
  >
    <h5 className="text-base font-normal">{children}</h5>
    <span className="text-xs text-stone-500">{subText}</span>
  </div>
);

const RefreshButton = ({ children }: { children: ReactNode }) => (
  <button
    className={classNames(
      "text-nowrap flex items-center flex-wrap gap-1.5 hover:underline hover:drop-shadow-sm"
    )}
  >
    <IoReload className="w-5 h-5" />
    {children}
  </button>
);

export default Dashboard;
