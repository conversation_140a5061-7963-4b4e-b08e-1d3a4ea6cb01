import React from 'react';

const BuyerFeatures = () => {
  return (
    <div className="max-w-full mx-auto px-4 lg:mt-8 py-8">
      <h1 className="text-xl md:text-3xl lg:text-[46px] font-bold text-center text-[#321310] mb-12">Key Features For Buyers</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        {/* Large card on the left spanning 1 column but full height */}
        <div className="md:col-span-1 md:row-span-2 relative rounded-lg overflow-hidden shadow-lg">
          <img 
            src="assets/pages/buyer/Group 5426.png" 
            alt="Free Requirement Posting" 
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
          
          <div className="absolute bottom-0 left-0 p-6 text-white">
            <div className="flex items-center mb-2">
              <img src="assets/pages/buyer/b795230c46b5961c188d569f2d892d8d.svg" alt="icon" className="w-6 h-6 mr-2" />
              <h3 className="text-xl font-semibold">Free Requirement Posting</h3>
            </div>
            <p className="text-sm opacity-90">Quickly submit your buying needs for free.</p>
          </div>
        </div>
        
        {/* Top right cards (2 cards) */}
        <div className="relative rounded-lg overflow-hidden shadow-lg">
          <img 
            src="assets/pages/buyer/Group 5427.png" 
            alt="Verified Sellers Only"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
          
          <div className="absolute bottom-0 left-0 p-6 text-white">
            <div className="flex items-center mb-2">
              <img src="assets/pages/buyer/21. Verified.svg" alt="icon" className="w-6 h-6 mr-2" />
              <h3 className="text-xl font-semibold">Verified Sellers Only</h3>
            </div>
            <p className="text-sm opacity-90">Work with suppliers you can trust.</p>
          </div>
        </div>
        
        <div className="relative rounded-lg overflow-hidden shadow-lg">
          <img 
            src="assets/pages/buyer/Group 5428.png" 
            alt="Bulk Proposals"
            className="w-full h-full object-cover" 
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
          
          <div className="absolute bottom-0 left-0 p-6 text-white">
            <div className="flex items-center mb-2">
              <img src="assets/pages/buyer/Layer_2.svg" alt="icon" className="w-6 h-6 mr-2" />
              <h3 className="text-xl font-semibold">Bulk Proposals</h3>
            </div>
            <p className="text-sm opacity-90">Access multiple quotes to find the best deal.</p>
          </div>
        </div>
        
        {/* Bottom right cards (2 cards) */}
        <div className="relative rounded-lg overflow-hidden shadow-lg bg-[#161c2e]">
          <img 
            src="assets/pages/buyer/Group 5429.png" 
            alt="Smart Search"
            className="w-full h-full object-cover opacity-30" 
          />
          
        
          
          <div className="absolute bottom-0 left-0 p-6 text-white">
            <div className="flex items-center mb-2">
              <img src="assets/pages/buyer/Global_Search.svg" alt="icon" className="w-6 h-6 mr-2" />
              <h3 className="text-xl font-semibold">Smart Search</h3>
            </div>
            <p className="text-sm opacity-90">Use filters to find the right supplier by product, location, or certifications.</p>
          </div>
        </div>
        
        <div className="relative rounded-lg overflow-hidden shadow-lg">
          <img 
            src="assets/pages/buyer/Group 5430.png" 
            alt="Global Sourcing"
            className="w-full h-full object-cover" 
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
          
          <div className="absolute bottom-0 left-0 p-6 text-white">
            <div className="flex items-center mb-2">
              <img src="assets/pages/buyer/boxes.svg" alt="icon" className="w-6 h-6 mr-2" />
              <h3 className="text-xl font-semibold">Global Sourcing</h3>
            </div>
            <p className="text-sm opacity-90">Expand your options with suppliers from around the world.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyerFeatures;
