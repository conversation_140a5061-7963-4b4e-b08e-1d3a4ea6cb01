import { createSlice, PayloadAction, AsyncThunk } from "@reduxjs/toolkit";
import { createThunkCase, APIResponseType } from "@/redux/helper/thunk";
import { compareProductsThunk } from "./thunk";

interface CompareStateType {
  compareProducts: APIResponseType; // State for comparing products
}

const APIState: CompareStateType = {
  compareProducts: {
    data: null,
    loading: false,
    meta: null,
  },
};

const initialState = {
  ...APIState,
} satisfies CompareStateType as CompareStateType;

export const slice = createSlice({
  name: "user_buyer_search_compare",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    createThunkCase(builder, compareProductsThunk as AsyncThunk<any, any, any>, APIState);
  },
});

export default slice.reducer;
