"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Actions } from "@/screens/user/dashboard/pages/buyer/request/@redux/slice";
import { productDetails, serviceDetails } from "@/screens/user/dashboard/pages/buyer/request/@redux/selector";
import { thunks } from "@/screens/user/dashboard/pages/buyer/request/@redux/thunk";
import Product from "./product";
import Service from "./service";
import InquiryCategory from "./card";
// import ThankYou from "@/pages/user/auth/thank-you/module";
import QuoteThanks from "@/components/elements/form/inquiry/thanks";

type StepType = 'selection' | 'category' | 'quantity' | 'requirement' | 'location' | 'urgency' | 'provider' | 'budget' | 'additional' | 'otp' | 'skills' | 'language' | 'team' | 'duration' | 'thankYou';

const ProductServiceUI = () => {
  const dispatch = useDispatch();
  const productDetailsState = useSelector(productDetails);
  const serviceDetailsState = useSelector(serviceDetails);

  const [selection, setSelection] = useState("");
  const [currentStep, setCurrentStep] = useState<StepType>("selection");
  const [categorySelected, setCategorySelected] = useState("");
  const [isCurrentStepValid, setIsCurrentStepValid] = useState(false);

  const formData = useMemo(() => {
    return selection === "product"
      ? productDetailsState?.formData || {}
      : serviceDetailsState?.formData || {};
  }, [selection, productDetailsState, serviceDetailsState]);


  const handleSelection = (option: string) => {
    const normalizedOption = option.toLowerCase();
    setSelection(normalizedOption);
    setCurrentStep("category");

    if (normalizedOption === "product") {
      dispatch(Actions.updateFormData({
        field: 'type',
        data: {
          name: "product",
          timestamp: new Date().toISOString(),
          isProduct: true
        }
      }));

      dispatch(Actions.updateFormData({
        field: 'productInfo',
        data: {
          isInitialized: true
        }
      }));
    } else if (normalizedOption === "service") {
      dispatch(Actions.updateServiceFormData({
        field: 'type',
        data: {
          name: "service",
          timestamp: new Date().toISOString(),
          isProduct: false
        }
      }));

      dispatch(Actions.updateServiceFormData({
        field: 'serviceInfo',
        data: {
          isInitialized: true
        }
      }));
    }

  };

  const handleNext = () => {

    if (currentStep === 'otp') {
      submitForm();
      setCurrentStep('thankYou');
      return;
    }

    if (selection === "product") {
      switch (currentStep) {
        case 'selection':
          setCurrentStep('category');
          break;
        case 'category':
          setCurrentStep('quantity');
          break;
        case 'quantity':
          setCurrentStep('location');
          break;
        case 'location':
          setCurrentStep('urgency');
          break;
        case 'urgency':
          setCurrentStep('provider');
          break;
        case 'provider':
          setCurrentStep('budget');
          break;
        case 'budget':
          setCurrentStep('requirement');
          break;
        case 'requirement':
          setCurrentStep('additional');
          break;
        case 'additional':
          setCurrentStep('otp');
          break;
        default:
          break;
      }
    } else if (selection === "service") {
      switch (currentStep) {
        case 'selection':
          setCurrentStep('category');
          break;
        case 'category':
          setCurrentStep('location');
          break;
        case 'location':
          setCurrentStep('language');
          break;
        case 'language':
          setCurrentStep('team');
          break;
        case 'team':
          setCurrentStep('skills');
          break;
        case 'skills':
          setCurrentStep('duration');
          break;
        case 'duration':
          setCurrentStep('budget');
          break;
        case 'budget':
          setCurrentStep('requirement');
          break;
        case 'requirement':
          setCurrentStep('additional');
          break;
        case 'additional':
          setCurrentStep('otp');
          break;
        default:
          break;
      }
    }
  };

  const submitForm = () => {
    const submitFormData = new FormData();

    const formDataToUse = selection === "product"
      ? productDetailsState?.formData || {}
      : serviceDetailsState?.formData || {};

    const displayType = selection === "product" ? "Product" : "Service";

    if (selection === "product") {
      const productData = {
        type: "product",
        title: formDataToUse.title || "",
        subSubCategory: "67f3a34510f2c55697597bbd",
        productDetails: {
          quantity: Number(formDataToUse.quantity?.amount) || 0,
          volume: formDataToUse.quantity?.unit || "piece",
          detailedDescription: formDataToUse.product?.description || "dfdffd",
          targetCountries: formDataToUse.location?.countries || [],
          requirementUrgency: formDataToUse.urgency?.urgency || "",
          preferredProviderType: formDataToUse.provider?.providerTypes?.[0] || "",
          budgetRange: {
            min: formDataToUse.budget?.budgetRange?.min || 0,
            max: formDataToUse.budget?.budgetRange?.max || 0,
            currency: formDataToUse.budget?.budgetRange?.currency || "USD"
          }
        },
        contactInfo: {
          name: formDataToUse.additional?.contactName || "",
          email: formDataToUse.additional?.email || "",
          phone: `${formDataToUse.additional?.phoneCode || ""}${formDataToUse.additional?.phoneNumber || ""}`,
          companyName: formDataToUse.additional?.companyName || "",
          notes: formDataToUse.additional?.notes || ""
        }
      };

      Object.entries(productData).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          submitFormData.append(key, JSON.stringify(value));
        } else {
          submitFormData.append(key, String(value));
        }
      });

    } else if (selection === "service") {
      const serviceData = {
        type: "service",
        title: formDataToUse.title || "",
        subSubCategory: "67f3a34510f2c55697597bbd",
        serviceDetails: {
          serviceDescription: formDataToUse.serviceRequirement?.scope || "",
          preferredCountries: formDataToUse.serviceLocation?.countries || [],
          languagePreferences: formDataToUse.language?.languages || [],
          teamSize: formDataToUse.team?.teamSize || "",
          requiredSkills: formDataToUse.skills?.skills || [],
          timeDuration: formDataToUse.duration?.duration || "",
          budgetRange: {
            min: formDataToUse.serviceBudget?.budgetRange?.min || 0,
            max: formDataToUse.serviceBudget?.budgetRange?.max || 0,
            currency: formDataToUse.serviceBudget?.budgetRange?.currency || "USD"
          },
        },
        contactInfo: {
          name: formDataToUse.serviceAdditional?.contactName || "",
          email: formDataToUse.serviceAdditional?.email || "",
          phone: `${formDataToUse.serviceAdditional?.phoneCode || ""}${formDataToUse.serviceAdditional?.phoneNumber || ""}`,
          companyName: formDataToUse.serviceAdditional?.companyName || "",
          notes: formDataToUse.serviceAdditional?.notes || ""
        }
      };

      // Add structured service data to FormData
      Object.entries(serviceData).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          submitFormData.append(key, JSON.stringify(value));
        } else {
          submitFormData.append(key, String(value));
        }
      });

    }
    try {
      // @ts-expect-error - We need to bypass the type checking here
      dispatch(thunks.postRFQ(submitFormData));
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const handlePrevious = () => {

    if (selection === "product") {
      switch (currentStep) {
        case 'category':
          setCurrentStep('selection');
          break;
        case 'quantity':
          setCurrentStep('category');
          break;
        case 'location':
          setCurrentStep('quantity');
          break;
        case 'urgency':
          setCurrentStep('location');
          break;
        case 'provider':
          setCurrentStep('urgency');
          break;
        case 'budget':
          setCurrentStep('provider');
          break;
        case 'requirement':
          setCurrentStep('budget');
          break;
        case 'additional':
          setCurrentStep('requirement');
          break;
        case 'otp':
          setCurrentStep('additional');
          break;
        default:
          break;
      }
    } else if (selection === "service") {
      switch (currentStep) {
        case 'category':
          setCurrentStep('selection');
          break;
        case 'location':
          setCurrentStep('category');
          break;
        case 'language':
          setCurrentStep('location');
          break;
        case 'team':
          setCurrentStep('language');
          break;
        case 'skills':
          setCurrentStep('team');
          break;
        case 'duration':
          setCurrentStep('skills');
          break;
        case 'budget':
          setCurrentStep('duration');
          break;
        case 'requirement':
          setCurrentStep('budget');
          break;
        case 'additional':
          setCurrentStep('requirement');
          break;
        case 'otp':
          setCurrentStep('additional');
          break;
        default:
          break;
      }
    }
  };

  const handleCategoryChange = (category: string) => {
    setCategorySelected(category);

    dispatch(Actions.updateFormData({
      field: 'category',
      data: { name: category }
    }));
  };

  const handleValidationChange = (isValid: boolean) => {
    setIsCurrentStepValid(isValid);
  };

  const isNextDisabled = () => {
    if (currentStep === 'selection' && !selection) {
      return true;
    }

    // Check for 'budget' step validation
    if (currentStep === 'budget') {
      const budgetRange = selection === 'product'
        ? productDetailsState?.formData?.budget?.budgetRange
        : serviceDetailsState?.formData?.serviceBudget?.budgetRange;

      const min = Number(budgetRange?.min || 0);
      const max = Number(budgetRange?.max || 0);

      // Disable if min > max
      if (min > max) return true;
    }

    if (currentStep !== 'selection' && currentStep !== 'thankYou') {
      return !isCurrentStepValid;
    }

    return false;
  };


  const isLastStep = currentStep === 'otp';

  const handleStartOver = () => {
    setSelection("");
    setCategorySelected("");
    setCurrentStep("selection");
    setIsCurrentStepValid(false);

    dispatch(Actions.resetProductDetails());
  };

  return (
    <div className="w-full max-w-4xl mx-auto py-8 px-4">
      {currentStep === 'selection' && (
        <div className="mb-8 h-[60vh]">
          <InquiryCategory
            onProductClick={() => handleSelection("product")}
            onServiceClick={() => handleSelection("service")}
            className="bg-white rounded-lg shadow-sm h-[60vh] flex items-center justify-center"
          />
        </div>
      )}

      {currentStep === 'thankYou' && (
        <div className="relative flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6 rounded-lg shadow-sm">
          {/* Back arrow */}
          <button
            onClick={handleStartOver}
            className="absolute top-16 left-4 p-2 text-gray-600 hover:text-[#ff4d4d] transition-colors"
            aria-label="Back to home"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </button>

          <div className="flex flex-col mb-16 items-center max-w-1">
            <QuoteThanks className="!min-w-max"
            // message={`Your ${selection.toLowerCase()} inquiry has been submitted successfully.`} 
            // onStartOver={handleStartOver}
            />
          </div>
        </div>
      )}

      {currentStep !== 'selection' && currentStep !== 'thankYou' && selection === "product" && (
        <Product
          currentStep={currentStep}
          onCategoryChange={handleCategoryChange}
          categorySelected={categorySelected}
          onValidationChange={handleValidationChange}
        />
      )}

      {currentStep !== 'selection' && currentStep !== 'thankYou' && selection === "service" && (
        <Service
          currentStep={currentStep}
          onValidationChange={handleValidationChange}
        />
      )}

      <div className="flex justify-between mt-8">
        {currentStep !== 'selection' && currentStep !== 'thankYou' && (
          <>
            <button
              onClick={handlePrevious}
              className="px-6 py-2 border-2 border-[#ff4d4d] text-[#ff4d4d] rounded-md hover:bg-[#ff4d4d] hover:text-white transition-all"
            >
              Previous
            </button>
            <button
              onClick={handleNext}
              disabled={isNextDisabled()}
              className="px-6 py-2 bg-[#ff4d4d] text-white rounded-md hover:bg-[#ff3333] transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLastStep ? 'Submit' : 'Save & Next'}
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default ProductServiceUI;