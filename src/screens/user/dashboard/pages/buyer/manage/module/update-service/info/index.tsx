'use client';

import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { TextEditor } from "@/components/ui/input/texteditor";
import { InputTargetType } from "@/components/ui/input/types";
import { RootDispatch } from "@/redux/store";
import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateRFQDetailsSelector } from "../../../@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/manage/@redux/slice";

const SERVICE_DETAILS_FIELDS = [
  "budgetRange",
  "serviceDescription",
  "preferredCountries",
  "languagePreferences",
  "teamSize",
  "requiredSkills",
  "timeDuration",
];

const ServiceInformation = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(updateRFQDetailsSelector);
  const [ready, setReady] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setReady(true), 100); // Delay of 50ms

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    try {
      const { name, value } = e.target;
      if (name === "budgetMin" || name === "budgetMax" || name === "budgetCurrency") {
        dispatch(
          Actions.setRFQDetails({
            serviceDetails: {
              ...values?.serviceDetails,
              budgetRange: {
                ...values?.serviceDetails?.budgetRange,
                [name === "budgetMin"
                  ? "min"
                  : name === "budgetMax"
                    ? "max"
                    : "currency"]: value,
              },
            },
          })
        );
      } else if (SERVICE_DETAILS_FIELDS.includes(name)) {
        dispatch(
          Actions.setRFQDetails({
            serviceDetails: {
              ...values?.serviceDetails,
              [name]: value,
            },
          })
        );
      } else {
        dispatch(
          Actions.setRFQDetails({
            [name]: value,
          })
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleMultiSelect = (name: string, e: MultiSelectTarget) => {
    dispatch(
      Actions.setRFQDetails({
        serviceDetails: {
          ...values?.serviceDetails,
          [name]: e.target.value as string[],
        },
      })
    );
  };

 const handleInputTargetChange = (e: InputTargetType) => {
  const { name, value } = e.target;
  dispatch(
    Actions.setRFQDetails({
      serviceDetails: {
        ...values?.serviceDetails,
        [name]: value,
      },
    })
  );
};

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6 [&>*]:w-full md:[&>*]:w-auto">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[25%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Service Name</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Required, Text (Max 100 chars)
          </span>
        </div>
        <Input
          type="text"
          name="title"
          label="Service Name"
          onChange={handleChange}
          value={values?.title ?? ""}
          placeholder="Service Name"
          containerClassName="flex-1"
          labelClassName="hidden md:block"
          maxLength={100}
          showCharCount
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <MultiSelect
          onValueChange={(e) => handleMultiSelect("preferredCountries", e)}
          value={values?.serviceDetails?.preferredCountries ?? []}
          name="preferredCountries"
          label="Preferred Countries"
          placeholder="Select countries"
          variant="inverted"
          options={[
            { label: "India", value: "india" },
            { label: "New Zealand", value: "new_zealand" },
            { label: "America", value: "america" },
            { label: "Austria", value: "austria" },
            { label: "Canada", value: "canada" },
            { label: "Australia", value: "australia" }
          ]}
        />

        <MultiSelect
          onValueChange={(e) => handleMultiSelect("languagePreferences", e)}
          value={values?.serviceDetails?.languagePreferences ?? []}
          name="languagePreferences"
          label="Language Preferences"
          placeholder="Select languages"
          variant="inverted"
          options={[
            { label: "English", value: "english" },
            { label: "Spanish", value: "spanish" },
            { label: "French", value: "french" },
            { label: "German", value: "german" },
            { label: "Mandarin", value: "mandarin" },
            { label: "Japanese", value: "japanese" },
            { label: "Hindi", value: "hindi" },
            { label: "Portuguese", value: "portuguese" },
            { label: "Arabic", value: "arabic" },
            { label: "Russian", value: "russian" }
          ]}
        />

        <Select
          name="teamSize"
          label="Team Size"
          onChange={handleInputTargetChange}
          value={values?.serviceDetails?.teamSize ?? ""}
          placeholder="Select team size"
          options={[
            { label: "1-2 people", value: "1-2" },
            { label: "3-5 people", value: "3-5" },
            { label: "6+ people", value: "6+" }
          ]}
        />

        <MultiSelect
          onValueChange={(e) => handleMultiSelect("requiredSkills", e)}
          value={values?.serviceDetails?.requiredSkills ?? []}
          name="requiredSkills"
          label="Required Skills"
          placeholder="Select required skills"
          variant="inverted"
          options={[
            { label: "ISO", value: "iso" },
            { label: "FDA", value: "fda" },
            { label: "GMP", value: "gmp" },
            { label: "GDP", value: "gdp" },
            { label: "CE", value: "ce" },
            { label: "HACCP", value: "haccp" },
            { label: "OSHA", value: "osha" },
            { label: "Six Sigma", value: "six_sigma" },
            { label: "Lean Manufacturing", value: "lean_manufacturing" },
            { label: "Quality Control", value: "quality_control" }
          ]}
        />

        <Input
          type="text"
          name="timeDuration"
          label="Time Duration"
          onChange={handleChange}
          value={values?.serviceDetails?.timeDuration ?? ""}
          placeholder="e.g., 2 hours, 3 days"
          containerClassName="flex-1"
          labelClassName="hidden md:block"
          maxLength={70}
        />
      </div>
      <div className="flex flex-col">
        <label htmlFor="minBudget" className="mb-2 text-sm font-medium text-gray-700">
          Minimum Budget (USD)
        </label>
        <Input
          id="minBudget"
          name="budgetMin"
          type="number"
          placeholder="Enter minimum budget"
          value={values?.serviceDetails?.budgetRange?.min ?? ""}
          onChange={handleChange}
          min="0"
        />
      </div>
      <div className="flex flex-col">
        <label htmlFor="maxBudget" className="mb-2 text-sm font-medium text-gray-700">
          Maximum Budget (USD)
        </label>
        <Input
          id="maxBudget"
          name="budgetMax"
          type="number"
          placeholder="Enter maximum budget"
          value={values?.serviceDetails?.budgetRange?.max ?? ""}
          onChange={handleChange}
          min="0"
        />
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6 mt-4">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Scope of work?</InputLabel>
          </div>
        </div>
        {ready && (

          <TextEditor
            name="detailedDescription"
            value={values?.serviceDetails?.detailedDescription ?? ""}
            onChange={handleInputTargetChange}
            containerClassName="flex-1"
            className="flex-1 rounded-md"
            key={'detailedDescription'}
            label="Brief product warranty"
          />
        )}
      </div>
    </>
  );
};

export default ServiceInformation;
