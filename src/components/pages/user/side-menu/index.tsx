"use client";

import { Switch } from "@/components/ui/input/switch";
import { showSidebar } from "@/screens/user/dashboard/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/@redux/slice";
import classNames from "classnames";
import { X } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useRef } from "react";
import { FiMenu } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { BuyerMenuData, sellerMenuData } from "./data";
import SideMenuList from "./list";
import SideMenuProfile from "./profile";

const SideMenu = () => {
  const router = useRouter();
  const pathname = usePathname();
  const containerRef = useRef<HTMLDivElement>(null);
  const show = useSelector(showSidebar);
  const isBuyer = pathname?.includes("/dashboard/buyer");
  const menuData = isBuyer ? BuyerMenuData : sellerMenuData;

  const handleProfileSwitch = (toBuyer: boolean) => {
    if (toBuyer) {
      router.push("/dashboard/buyer");
    } else {
      router.push("/dashboard/seller");
    }
  };

  return (
    <>
      {!show && <ToggleButton show={show} classANmes="hidden md:block" />}

      <div
        ref={containerRef}
        className={classNames(
          "fixed left-0 top-0 bottom-0 transition-all z-50 max-w-screen min-w-screen overflow-auto",
          {
            "-translate-x-full md:hidden": !show,
            "translate-x-0": show,
          },
          "side-menu md:z-20 md:overflow-hidden md:translate-x-0 md:sticky md:top-header bg-[#E3E3E3] rounded-lg shadow-box px-4 pt-2 pb-4 w-full md:max-w-[256px] md:min-w-[256px] block"
        )}
      >
        <div className="w-full flex items-center justify-between mb-2 z-50">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-sm">Seller</span>
            <Switch checked={isBuyer} onCheckedChange={handleProfileSwitch} />
            <span className="font-semibold text-sm">Buyer</span>
          </div>
          <ToggleButton show={show} />
        </div>
        <SideMenuProfile />
        <SideMenuList data={menuData} />
      </div>
    </>
  );
};

const ToggleButton = ({
  show,
  classANmes,
}: {
  show: boolean;
  classANmes?: string;
}) => {
  const dispatch = useDispatch();

  return (
    <button
      className={classNames(
        "flex items-center justify-center p-2 rounded-full [&>svg]:w-6 [&>svg]:h-6 bg-stone-100",
        classANmes
      )}
      onClick={() => dispatch(Actions.setSidebarShow(!show))}
    >
      {show ? <X /> : <FiMenu />}
    </button>
  );
};

export default SideMenu;
