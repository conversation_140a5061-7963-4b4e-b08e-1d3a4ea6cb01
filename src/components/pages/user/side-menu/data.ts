import DASHBOARD from "/assets/pages/user/dashboard/sidebar/seller/dashboard.svg";
import LISTING from "/assets/pages/user/dashboard/sidebar/seller/listing.svg";
import MEDIA from "/assets/pages/user/dashboard/sidebar/seller/media.svg";
import SUPPORT from "/assets/pages/user/dashboard/sidebar/seller/support.svg";
import SETTING from "/assets/pages/user/dashboard/sidebar/seller/setting.svg";
import OPPORTUNITIES from "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg";
import PAID from "/assets/pages/user/dashboard/sidebar/seller/paid.svg";
import WEBSITE from "/assets/pages/user/dashboard/sidebar/seller/website.svg";
import INBOX from "/assets/pages/user/dashboard/sidebar/seller/inbox.svg";
import REVIEWS from "/assets/pages/user/dashboard/sidebar/seller/reviews.svg";

import { MenuData } from "./types";

export const sellerMenuData: MenuData = [
  {
    icon: DASHBOARD,
    title: "Insights",
    activePathname: ["/dashboard/seller"],
    pathname: "/dashboard/seller",
  },
  {
    icon: LISTING,
    title: "Listing",
    pathname: "/dashboard/seller/listing",
    activePathname: ["/dashboard/seller/listing/*"],
  },
  {
    icon: WEBSITE,
    title: "Website",
  },
  {
    icon: INBOX,
    title: "Inbox",
  },
  {
    icon: OPPORTUNITIES,
    title: "Opportunities",
    pathname: "/dashboard/seller/opportunities",
    activePathname: ["/dashboard/seller/opportunities/*"],
  },
  {
    icon: PAID,
    title: "Paid Services",
    pathname: "/dashboard/seller/paid-services",
    activePathname: ["/dashboard/seller/paid-services/*"],
  },
  {
    icon: REVIEWS,
    title: "Reviews",
    pathname: "/dashboard/seller/reviews",
    activePathname: ["/dashboard/seller/reviews"],
  },
  {
    icon: MEDIA,
    title: "Photos & Media",
  },
  {
    icon: SETTING,
    title: "Setting",
  },
  {
    icon: SUPPORT,
    title: "Support",
  },
];
