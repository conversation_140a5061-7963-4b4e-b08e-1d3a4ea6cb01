import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from "../@api/update";

export const fetchWebsiteDetails = createAsyncThunk<
  any,
  void,
  { state: RootState }
>("websiteDetails/fetchPortfolio", async (_, { rejectWithValue }) => {
  try {
    const response = await API.fetchWebsite();
    window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const updateWebsite = createAsyncThunk<
  any,
  FormData,
  { state: RootState }
>(
  "updateWebsite",
  async (formValue, { rejectWithValue, getState, dispatch }) => {
    const currentWebsiteDetails = (getState() as RootState)?.user_seller_website
      ?.websiteDetails;
    try {
      const response: any = await API.addUpdateWebsite(formValue);
      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      dispatch(fetchWebsiteDetails());
      
      const successPayload = {
        ...response,
        data: currentWebsiteDetails?.data,
      };
      return successPayload;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      const rejectPayload = {
        data: currentWebsiteDetails?.data,
        meta: error?.response?.data || {
          message: error?.message || "Failed to fetch business details",
        },
      };

      const rejected = currentWebsiteDetails?.data
        ? rejectPayload
        : rejectWithValue(error?.response?.data || error?.message);
      return rejected;
    }
  }
);

export const thunks = {
  fetchWebsiteDetails,
  updateWebsite,
};
