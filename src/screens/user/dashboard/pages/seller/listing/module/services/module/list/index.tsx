"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import { Loader } from "@/components/ui/loader";
import { servicesList } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/selectors";
import { deleteService } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/thunk";
import { RootDispatch } from "@/redux/store";
import { AlertCircle } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";

interface ServiceListProps {
  services: any[];
}

const ServiceList: React.FC<ServiceListProps> = ({ services = [] }) => {
const router = useRouter();
  const { loading } = useSelector(servicesList);
  const dispatch = useDispatch<RootDispatch>();

  const handleEdit = (service_id: string) => {
    router.push(`update-service/${service_id}`);
  };

  const handleDelete = (service_id: string) => {
    dispatch(deleteService(service_id));
  };

  if (loading) {
    return <Loader big center />;
  }

  return (
    <div className="space-y-4">
      {Array.isArray(services) && services?.length > 0 ? (
        <>
          <ListCardHeader />
          {services.map((service) => (
            <ListCard
              key={service._id}
              image={service?.main_service_images?.[0]}
              title={service?.service_name}
              category={service?.category?.[0]?.name ?? "Category"}
              views={"345"}
              status={service?.service_status}
              onEdit={() => handleEdit(service._id)}
              onDelete={() => handleDelete(service._id)}
            />
          ))}
        </>
      ) : (
        <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
          <AlertCircle /> No Services Available
        </div>
      )}
    </div>
  );
};

export default ServiceList;
