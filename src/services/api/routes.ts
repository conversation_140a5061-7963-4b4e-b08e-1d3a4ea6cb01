const prodRoutes = {
  auth: {
    userAuth: "/auth/user-details",
    register: "/auth/signup",
    login: "/auth/signin",
    googleAuth: "/auth/google",
    facebookAuth: "/auth/facebook",
    linkedinAuth: "/auth/linkedin",
    resetPassword: "/auth/reset-password",
    createPassword: "/auth/create-password",
  },
  user: {
    category: {
      getAll: "/category/fetch",
      sub: {
        fetchAll: "/category/sub-category/fetch",
      },
      sub_sub: {
        fetchAll: "/category/sub-sub-category/fetch",
      },
      fetchDynamicField: "/dynamic/product/schema/fetch",
    },
    seller: {
      company: {
        fetchAll: "/seller/business/fetch/all",
        fetch: "/seller/business/fetch",
        update: "/seller/business/add-update",
        verification: {
          email: {
            initiate: "/seller/business/verification/email/sent",
            verify: "/seller/business/verification/email/verify",
          },
        },
        files: {
          businessPhotos: "/seller/business/upload/business-photos",
          govRegistration: "/seller/business/upload/government-registration",
          companyPhotos: "/seller/business/upload/company-photos",
        },
        portfolio: {
          addUpdate: "/seller/portfolio/add",
          fetchAll: "/seller/portfolio/all",
          fetch: "/seller/portfolio/fetch",
          delete: "/seller/portfolio/delete",
        },
      },
      website: {
        fetch: "/seller/business/website/fetch",
        update: "/seller/business/website/addupdate",
      },
      products: {
        suggestions: "/seller/meta-template/product/suggestions",
        preValues: "/seller/meta-template/product/fetch",
        addUpdate: "/seller/product/add",
        fetch: "/seller/product/fetch",
        fetchAll: "/seller/product/all",
        delete: "/seller/product/delete",
      },
      service: {
        suggestions: "/seller/meta-template/service/suggestions",
        preValues: "/seller/meta-template/service/fetch",
        addUpdate: "/seller/service/add",
        fetch: "/seller/service/fetch",
        fetchAll: "/seller/service/all",
        delete: "/seller/service/delete",
      },
      common: {
        getDynamicFields: "/seller/product/getmetadata",
        deleteMedia: "/media/delete",
      },
    },
  },
  buyer: {
    business: {
      addUpdate: "/buyer/business/add-update",
      fetch: "/buyer/business/fetch",
      verification: {
        email: {
          initiate: "/seller/business/verification/email/sent",
          verify: "/seller/business/verification/email/verify",
        },
      },
    },
    manage: {
      fetchRFQ: "/buyer/rfq/fetch/all",
      fetchRFQDetails: "/buyer/rfq/fetch",
      updateRFQ: "/buyer/rfq/update",
      proposalAction: "/buyer/rfq/proposal/action",
    },
    rfq: {
      addRFQ: "/buyer/rfq/post",
      deleteRFQ: "/buyer/rfq/delete",
    },
    favourite: {
      addFavourite: "/buyer/favorite/add",
      deleteFavourite: "/buyer/favorite/remove",
      fetchFavourite: "/buyer/favorite/fetch",
    },
    settings: {
      fetchProfileDetails: "/profile/getDetails",
      deleteAccount: "/profile/delete-account",
      updateProfileImage: "/profile/profile/update",
      updateProfileDetails: "/profile/update-details",
      changePassword: "/auth/change-password",
    },
    search: {
      productCompare: "/seller/product/compare",
    },
  },
};

export const apiRoutes = () => prodRoutes;
