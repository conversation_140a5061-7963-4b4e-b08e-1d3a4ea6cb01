"use client";

import React from "react";
import ListCard from "@/screens/company/listing/module/components/list/list-card"; // Update the path to where your ListCard component is located
import { ListingData } from "@/constants/listing";
import ListingFilter from "@/components/elements/filters/listings/desktop"; // Import your constant data
import ComparePop from "@/screens/company/listing/module/components/compare-pop";
const CompanyListings = () => {
  return (
    <div className="flex flex-col bg-white p-6 gap-4">
       <ListingFilter/>
      {ListingData.map((company, index) => (
        <ListCard key={index} company={company} />
      ))}
      <ComparePop/>
    </div>
  );
};

export default CompanyListings;