import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  Dropzone,
  DropzoneInputTargetType,
  InlinePlaceholder,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import { deleteMediaHandler } from "@/lib/utils/helper";
import { productDetails } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/products/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";

const Media = () => {
  const dispatch = useDispatch();
  const { data: values } = useSelector(productDetails);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values?.[e?.target?.name])
          ? Array.from(values?.[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setProductDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const handleDeleteMedia = async (file: any, name: string) => {
    const updatedFiles = await deleteMediaHandler(values, file, name);
    dispatch(Actions.setProductDetails({ [name]: updatedFiles }));
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Main Product Images</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload the primary product image (1000x1000px)
          </span>
        </div>
        <Dropzone
          containerClassName="flex-1"
          onChange={(e) => handleChange(e)}
          onFileDelete={(file, name) => handleDeleteMedia(file, name)}
          name="main_product_images"
          value={values?.main_product_images}
          preview
          inputProps={{ multiple: true }}
        >
          {Array.isArray(values?.main_product_images) &&
          values?.main_product_images?.length > 0 ? (
            <InlinePlaceholder />
          ) : null}
        </Dropzone>
      </div>

      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Catalogs Photos</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload the catalog photos (1000x1000px)
          </span>
        </div>
        <Dropzone
          containerClassName="flex-1"
          onChange={(e) => handleChange(e)}
          onFileDelete={(file, name) => handleDeleteMedia(file, name)}
          name="catalogs_images"
          value={values?.catalogs_images}
          preview
          filePreviewClassName="grid grid-cols-2 [&>*]:min-w-[100%] [&>*]:min-h-[200px] px-3"
          inputProps={{ multiple: true }}
        >
          {Array.isArray(values?.catalogs_images) &&
          values?.catalogs_images?.length > 0 ? (
            <InlinePlaceholder className="!h-32 bg-stone-100 rounded-lg shadow-md w-[96%] mx-auto" />
          ) : null}
        </Dropzone>
      </div>

      <div className="w-full flex flex-col md:flex-row items-center gap-4">
        <div className="w-full flex items-center justify-start gap-1.5 md:w-[30%]">
          <InputLabel>Product Video URL</InputLabel>
        </div>
        <Input
          type="text"
          placeholder="Product Video Url"
          name="product_video_url"
          value={values?.product_video_url ?? ""}
          onChange={handleChange}
          containerClassName="flex-1 w-full "
        />
      </div>

      <div className="w-full flex flex-col md:flex-row items-center gap-4">
        <div className="w-full flex items-center justify-start gap-1.5 md:w-[30%]">
          <InputLabel>Warranty</InputLabel>
        </div>
        <Input
          type="text"
          placeholder="Product Warranty Period"
          name="warranty"
          value={values?.warranty ?? ""}
          onChange={handleChange}
          containerClassName="flex-1 w-full "
        />
      </div>

      <div className="w-full flex flex-col md:flex-row items-center gap-4">
        <div className="w-full flex items-center justify-start gap-1.5 md:w-[30%]">
          <InputLabel>Service Offered</InputLabel>
        </div>
        <Input
          type="text"
          placeholder="Installation, Maintenance, etc."
          name="service_offered"
          value={values?.service_offered ?? ""}
          onChange={handleChange}
          containerClassName="flex-1 w-full"
        />
      </div>

      <div className="w-full flex flex-col md:flex-row items-center gap-4">
        <div className="w-full flex items-center justify-start gap-1.5 md:w-[30%]">
          <InputLabel>After-Sale Service</InputLabel>
        </div>
        <Input
          type="text"
          placeholder="Support and after-sale services"
          name="after_sale_services"
          value={values?.after_sale_services ?? ""}
          onChange={handleChange}
          containerClassName="flex-1 w-full"
        />
      </div>
    </>
  );
};

export default Media;
