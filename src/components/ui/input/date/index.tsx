"use client";

import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import classNames from "classnames";
import { InputLabel } from "../components/label";

type DateInputTargetType = {
  target: {
    name: string;
    value?: Date;
  };
};

interface DateInputType {
  containerClassName?: string;
  className?: string;
  placeholder?: string;
  label?: string;
  labelClassName?: string;
  name?: string;
  value?: Date;
  onChange?: (d: DateInputTargetType) => void;
  required?: boolean;
}

const DateInput: React.FC<DateInputType> = ({
  containerClassName,
  className,
  onChange,
  value,
  label,
  labelClassName,
  placeholder,
  name,
  required,
}) => {
  const handleChange = (data: Date | undefined) => {
    try {
      onChange?.({
        target: {
          name: name || "date",
          value: data,
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Popover>
      <div
        className={classNames(
          "flex flex-col gap-1.5 items-center",
          containerClassName
        )}
      >
        {label && (
          <InputLabel
            className={classNames("w-full", labelClassName)}
            required={required}
          >
            {label}
          </InputLabel>
        )}
        <PopoverTrigger asChild>
          <Button
            variant={"input"}
            className={classNames(
              "w-full justify-start text-left font-normal",
              !value && "text-stone-400",
              className
            )}
          >
            <CalendarIcon className="mr-1 h-4 w-4 group-hover:text-main" />
            {value ? (
              format(value, "PPP")
            ) : (
              <span>{placeholder || "Pick a date"}</span>
            )}
          </Button>
        </PopoverTrigger>
      </div>
      <PopoverContent className="w-auto p-0 bg-white" align="start">
        <Calendar
          mode="single"
          selected={value}
          onSelect={handleChange}
          initialFocus
          required={required}
        />
      </PopoverContent>
    </Popover>
  );
};

export { DateInput };
export type { DateInputTargetType, DateInputType };
