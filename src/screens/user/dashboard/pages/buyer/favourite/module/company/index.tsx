'use client';

import React, { useEffect } from "react";
import ListC<PERSON> from "./company-card";
import { ListingData } from "@/constants/listing";
import { getFavourites, deleteFavourite } from "../../@redux/thunk"; // ✅ Import deleteFavourite
import { getFavouritesSelector } from "../../@redux/selector";
import ListingFilter from "@/components/elements/filters/listings/desktop";
import ComparePop from "@/screens/company/listing/module/components/compare-pop";
import { useDispatch, useSelector } from "react-redux";
import { RootDispatch } from "@/redux/store";

const CompanyListings = () => {
  const dispatch = useDispatch<RootDispatch>();
  
  // Get favourites from Redux store
  const { data: favouriteCompanies, loading } = useSelector(getFavouritesSelector);
  
  // Dispatch API call when component mounts
  useEffect(() => {
    dispatch(
      getFavourites({
        favoriteType: "business",
        priority: 1,
      })
    );
  }, [dispatch]);

  // Transform API data to match ListCard expected format with static fallbacks
 const transformCompanyData = (apiData: any) => {
  const staticFallback = {
    name: "Premium Business Solutions",
    images: [
      "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      "https://c4.wallpaperflare.com/wallpaper/229/242/345/moscow-city-2017-art-irbis-production-wallpaper-thumb.jpg"
    ],
    location: {
      flag: "https://flagcdn.com/w40/in.png",
      address: "Business District, Downtown",
      label: "Commercial Area"
    },
    verified: true,
    offerPrice: 999,
    actualPrice: 1299,
    rating: {
      stars: 4.2,
      reviewsCount: 156
    },
    tags: [
      { title: "Mobile Phones" },
      { title: "Electronics" },
      { title: "Second Hand" },
      { title: "Refurbished" },
      { title: "Warranty Available" }
    ],
    strengthRange: "10-50",
    businessAge: "5 years",
    minOrderQuantity: 100,
    shortIntro: "Your trusted partner for quality products and exceptional service."
  };

  return {
    id: apiData._id || Math.random().toString(),
    name: apiData.business_name || apiData.name || staticFallback.name,
    shortIntro: apiData.shortIntro || staticFallback.shortIntro,
    images: apiData.business_photos?.length > 0 
      ? apiData.business_photos.map((photo: any) => photo.url)
      : staticFallback.images,
    location: {
      flag: staticFallback.location.flag,
      address: apiData.business_address || `${apiData.city || "Unknown City"}`,
      label: `${apiData.city || "Unknown City"}, ${apiData.state || "Unknown State"}, ${apiData.country || "Unknown Country"}`
    },
    verified: staticFallback.verified,
    offerPrice: staticFallback.offerPrice,
    actualPrice: staticFallback.actualPrice,
    rating: {
      stars: apiData.average_rating || staticFallback.rating.stars,
      reviewsCount: apiData.reviews_count || staticFallback.rating.reviewsCount
    },
    tags: apiData.tags?.length > 0 ? apiData.tags : staticFallback.tags,
    description: apiData.description || "Quality products and services for your business needs.",
    strengthRange: staticFallback.strengthRange,
    businessAge: apiData.businessAge?.toString() || staticFallback.businessAge, // ✅ Ensure string
    minOrderQuantity: apiData.minOrderQuantity || staticFallback.minOrderQuantity
  };
};


  // ✅ Handle unfavorite function
  const handleUnfavourite = (companyId: string) => {
    dispatch(
      deleteFavourite({
        favouriteId: companyId,
        filters: {
          favoriteType: "business",
          priority: 1,
        },
      })
    );
  };

  return (
   <div className="flex flex-col bg-white p-3 sm:p-6 gap-4">
      <div className="flex justify-between items-center mb-2 sm:mb-4">
        <h2 className="text-base sm:text-lg font-medium text-gray-800">
          {favouriteCompanies?.length || 0} results found
        </h2>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <p className="text-gray-500">Loading companies...</p>
        </div>
      ) : favouriteCompanies?.length > 0 ? (
        favouriteCompanies.map((item: any, index: number) => (
          <ListCard 
            key={item.id || index} 
            company={transformCompanyData(item.favoriteDetails || item)}
            onUnfavourite={() => handleUnfavourite(item._id)} 
          />
        ))
      ) : (
        <div className="flex items-center justify-center py-8">
          <p className="text-gray-500">No favorite companies found</p>
        </div>
      )}
    </div>
  );
};

export default CompanyListings;