'use client';

import { Select } from "@/components/ui/input/select";
import React, { useState } from 'react';
import { InputTargetType } from "@/components/ui/input/select";

interface CategoryProps {
  onCategoryChange?: (payment: string) => void;
  selectedCategory?: string;
}

const CategoryDropdown: React.FC<CategoryProps> = ({ onCategoryChange, selectedCategory = "" }) => {
  const [payment, setCategory] = useState(selectedCategory);

  const handleCategoryChange = (e: InputTargetType) => {
    setCategory(e.target.value);
    if (onCategoryChange) {
      onCategoryChange(e.target.value);
    }
  };

  const PaymentOptions = [
    { label: "Credit Card", value: "Credit Card" },
    { label: "Debit Card", value: "Debit Card" },
    { label: "Bank Transfer", value: "Bank Transfer" },
    { label: "PayPal", value: "PayPal" },
    { label: "Apple Pay", value: "Apple Pay" },
    { label: "Google Pay", value: "Google Pay" },
    { label: "Cryptocurrency", value: "Cryptocurrency" },
    { label: "Cash on Delivery", value: "Cash on Delivery" },
  ];

  return (
    <div className="flex flex-col items-center bg-white min-h-[50vh] w-full mx-auto p-4 sm:p-6">
      <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 sm:mb-4 text-center">Preferred Payment Terms</h1>
      <p className="text-sm sm:text-base text-center mb-4 sm:mb-6 px-2 sm:px-4">Specify your preferred payment terms</p>
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md">
        <Select
          placeholder="Payment"
          options={PaymentOptions}
          value={payment}
          onChange={handleCategoryChange}
        />
      </div>
    </div>
  );
};

export default CategoryDropdown;