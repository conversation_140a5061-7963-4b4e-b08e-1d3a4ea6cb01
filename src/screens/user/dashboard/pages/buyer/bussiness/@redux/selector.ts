import { RootState } from "@/redux/root-reducer";
import { createSelector } from "reselect";

export const reducer = (state: RootState) => state.user_buyer_business;

export const currentFormStep = createSelector(
  [reducer],
  (reducer) => reducer.currentFormStep
);

export const businessDetails = createSelector(
  [reducer],
  (reducer) => reducer.businessDetails
);

export const getBusinessDetailsSelector = createSelector(
  [reducer],
  (reducer) => reducer.businessDetails
);


export const sub_sub_categories = createSelector(
  [reducer],
  (reducer) => reducer.sub_sub_categories
);

export const emailVerificationInitiate = createSelector(
  [reducer],
  (reducer) => reducer.emailVerificationInitiate
);

export const emailVerify = createSelector(
  [reducer],
  (reducer) => reducer.emailVerify
);
