'use client';

import React, { useState, useEffect, useMemo } from "react";
import { usePathname, useRouter } from "next/navigation";
import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import { Select, InputTargetType } from "@/components/ui/input/select";
import { useDispatch, useSelector } from "react-redux";
import { getRFQList, getRFQDetail, updateRFQStatus, deleteRFQ } from "../../@redux/thunk";
import { getRFQListSelector, getRFQDetailSelector, updateRFQStatusSelector, reducer as buyerRFQReducer } from "../../@redux/selector";
import { RootDispatch } from "@/redux/store";
import ListPagination from "@/components/elements/list/pagination";

const buildHeaderData = (type: string) => [
  { label: "RFQ ID", key: "rfqId", className: "!max-w-[10%] !text-center" },
  { label: type === "product" ? "PRODUCT NAME" : "SERVICE NAME", key: "title", className: "!max-w-[30%] !text-center" },
  { label: "DATE CREATED", key: "postedAt", className: "!max-w-[18%] !text-center" },
  { label: "STATUS", key: "status", className: "!max-w-[12%] !text-center" },
  { label: "RESPONSE", key: "proposalCount", className: "!max-w-[10%] !text-center" },
  { label: "ACTIONS", key: "actions", className: "!max-w-[30%] !text-center" },
];

const typeOptions = [
  { label: "Product", value: "product" },
  { label: "Service", value: "service" }
];

const statusOptions = [
  { label: "Pending", value: "pending" },
  { label: "Open", value: "active" },
  { label: "Closed", value: "closed" },
];

interface TypeSelection {
  title: string;
}
interface RFQItem {
  _id: string;
  rfqId?: string;
  title: string;
  postedAt: string;
  status: string;
  proposalCount: number;
  type?: string;
}

const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getLastFourDigits = (id: string): string => {
  if (!id) return '';
  return id.slice(-4);
};

// Helper function to get status label from value
const getStatusLabel = (statusValue: string): string => {
  const statusOption = statusOptions.find(option => option.value.toLowerCase() === statusValue.toLowerCase());
  return statusOption ? statusOption.label : statusValue.charAt(0).toUpperCase() + statusValue.slice(1);
};

const RFQListPage = () => {
  // Initialize selectedType from sessionStorage immediately
  const getInitialType = (): TypeSelection => {
    try {
      const savedState = sessionStorage.getItem('rfq_filter_state');
      if (savedState) {
        const { selectedType } = JSON.parse(savedState);
        return selectedType || { title: "product" };
      }
    } catch (error) {
      console.error('Error parsing saved state:', error);
    }
    return { title: "product" };
  };

  const [pagination, setPagination] = useState({ page: 1, limit: 10 });
  const [filteredData, setFilteredData] = useState<RFQItem[]>([]);
  const [selectedType, setSelectedType] = useState<TypeSelection>(getInitialType);
  const [totalPages, setTotalPages] = useState(1);

  const dispatch = useDispatch<RootDispatch>();
  const rfqReducerState = useSelector(buyerRFQReducer);
  const rfqListState = useSelector(getRFQListSelector);
  const rfqStatusState = useSelector(updateRFQStatusSelector);

  const pathname = usePathname();
  const router = useRouter();

  const headerData = useMemo(() => buildHeaderData(selectedType.title), [selectedType.title]);

  // Save state to sessionStorage whenever selectedType or pagination changes
  useEffect(() => {
    const stateToSave = {
      pagination,
      selectedType,
      path: pathname
    };
    sessionStorage.setItem('rfq_filter_state', JSON.stringify(stateToSave));
  }, [selectedType, pagination, pathname]);

  // Restore pagination from sessionStorage when component mounts
  useEffect(() => {
    try {
      const savedState = sessionStorage.getItem('rfq_filter_state');
      if (savedState) {
        const { pagination: savedPagination } = JSON.parse(savedState);
        if (savedPagination) {
          setPagination(savedPagination);
        }
      }
    } catch (error) {
      console.error('Error restoring pagination:', error);
    }
  }, []);

  // Main data fetching effect
  useEffect(() => {
    const fetchParams = {
      ...pagination,
      type: selectedType.title
    };

    dispatch(getRFQList(fetchParams))
      .unwrap()
      .then((result) => {
        if (result && result.total) {
          setTotalPages(Math.ceil(result.total / pagination.limit));
        } else if (result && result.count) {
          setTotalPages(Math.ceil(result.count / pagination.limit));
        } else if (result && Array.isArray(result) && result.length > 0) {
          setTotalPages(Math.max(2, pagination.page + 1)); // Assume there's at least one more page
        } else {
          setTotalPages(Math.max(pagination.page, Math.ceil(filteredData.length / pagination.limit)));
        }

        if (filteredData.length > 0) {
          setTotalPages(prev => Math.max(prev, 1));
        }
      })
      .catch((error) => {
        setTotalPages(Math.max(pagination.page, 1));
      });
  }, [dispatch, pagination.page, pagination.limit, selectedType.title, pathname]);

  const handleTypeChange = (e: InputTargetType) => {
    const value = e.target.value;
    const newSelectedType = { title: value };
    setSelectedType(newSelectedType);
    setPagination({ page: 1, limit: 10 });
    
    // Immediately save to sessionStorage
    const stateToSave = {
      pagination: { page: 1, limit: 10 },
      selectedType: newSelectedType,
      path: pathname
    };
    sessionStorage.setItem('rfq_filter_state', JSON.stringify(stateToSave));
  };

  const getRFQData = () => {
    if (!rfqListState || !rfqListState.data) {
      return [];
    }

    const data = rfqListState.data;
    if (Array.isArray(data)) {
      return data;
    }

    if (data && typeof data === 'object') {
      if ('data' in data && Array.isArray(data.data)) {
        return data.data;
      }

      if ('result' in data && Array.isArray(data.result)) {
        return data.result;
      }

      if ('items' in data && Array.isArray(data.items)) {
        return data.items;
      }
    }
    return [];
  };

  const transformRFQData = (data: any[]): RFQItem[] => {
    return data.map(item => ({
      _id: item._id,
      rfqId: getLastFourDigits(item._id),
      title: item.title || 'No Title',
      postedAt: formatDate(item.postedAt),
      status: item.status ? item.status.toLowerCase() : 'pending',
      proposalCount: item.proposalCount || 0,
      type: item.type
    }));
  };

  useEffect(() => {
    const rfqData = getRFQData();
    if (rfqData.length === 0) {
      setFilteredData([]);
      return;
    }

    const transformedData = transformRFQData(rfqData);

    applyStatusFiltering(transformedData);

    if (transformedData.length > 0) {
      const responseData = rfqListState.data;
      if (responseData && typeof responseData === 'object' && !Array.isArray(responseData)) {
        const totalCount = responseData.total || responseData.count || responseData.totalCount;
        if (totalCount) {
          setTotalPages(Math.ceil(totalCount / pagination.limit));
        } else {
          setTotalPages(Math.max(pagination.page, Math.ceil(transformedData.length / pagination.limit)));
        }
      }
    }
  }, [pathname, rfqListState.data, pagination.limit]);

  const applyStatusFiltering = (data: RFQItem[]) => {
    const currentPath = pathname.toLowerCase();
    let statusFilter = '';
    if (currentPath.includes("/open") || currentPath.includes("/active")) {
      statusFilter = "active";
    } else if (currentPath.includes("/close") || currentPath.includes("/closed")) {
      statusFilter = "closed";
    } else if (currentPath.includes("/pending")) {
      statusFilter = "pending";
    } else if (currentPath.includes("/expired")) {
      statusFilter = "expired";
    }

    const filtered = statusFilter
      ? data.filter(item => item.status.toLowerCase() === statusFilter)
      : data;

    setFilteredData(filtered);
  };

  const handleView = (id: string) => {
    dispatch(getRFQDetail(id));
    router.push(`/dashboard/buyer/manage/detail/${id}`);
  };

  const handleEdit = (rfq_id: string) => {
    const basePath = selectedType.title === "product" ? "update-product" : "update-service";
    router.push(`/dashboard/buyer/manage/${basePath}/${rfq_id}`);
  };

  const handleDelete = (rfq_id: string) => {
    const currentPath = pathname.toLowerCase();
    let statusFilter = '';

    if (currentPath.includes("/open") || currentPath.includes("/active")) {
      statusFilter = "active";
    } else if (currentPath.includes("/close") || currentPath.includes("/closed")) {
      statusFilter = "closed";
    } else if (currentPath.includes("/pending")) {
      statusFilter = "pending";
    } else if (currentPath.includes("/expired")) {
      statusFilter = "expired";
    }

    dispatch(deleteRFQ({
      rfqId: rfq_id,
      filters: {
        page: pagination.page,
        limit: pagination.limit,
        type: selectedType.title,
        status: statusFilter
      }
    }));
  };

  const handleStatusChange = (id: string, newStatus: string) => {
    const apiStatus = newStatus.toLowerCase();

    // Find the RFQ item in filteredData by id
    const rfqItem = filteredData.find(item => item._id === id);
    if (!rfqItem) return; // just in case

    // If current status is "closed", do not update or call API
    if (rfqItem.status === "closed") {
      console.log("Cannot update status because RFQ is closed.");
      return;
    }

    console.log(`Updating status of RFQ ${id} to ${newStatus}`);

    // Optimistically update the UI
    setFilteredData(prevData =>
      prevData.map(item =>
        item._id === id ? { ...item, status: apiStatus } : item
      )
    );

    // Dispatch API call to update status
    dispatch(updateRFQStatus({ id, status: apiStatus }))
      .unwrap()
      .then(() => {
        // Refresh the list after successful update
        dispatch(getRFQList({
          ...pagination,
          type: selectedType.title
        }));
      })
      .catch((error) => {
        console.error("Failed to update status:", error);
        // Revert the optimistic update on error
        setFilteredData(prevData =>
          prevData.map(item =>
            item._id === id ? { ...item, status: rfqItem.status } : item
          )
        );
        // Still refresh the list to get the latest data
        dispatch(getRFQList({
          ...pagination,
          type: selectedType.title
        }));
      });
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="py-6 px-4 overflow-x-hidden">
      <div className="flex justify-between items-center mb-6">
        <div className="w-64">
          <Select
            placeholder="Select Type"
            options={typeOptions}
            value={selectedType.title}
            onChange={handleTypeChange}
            label="Request Type"
            className="bg-white"
          />
        </div>
      </div>

      <div className="rounded-lg overflow-hidden">
        {/* Horizontal Scroll Wrapper */}
        <div className="w-full overflow-x-auto">
          <div className="min-w-[800px] flex flex-col gap-2 p-2">
            <ListCardHeader
              data={headerData}
              className="py-3 font-medium text-gray-700"
            />

            {(rfqListState.loading || rfqStatusState.loading) && (
              <div className="py-8 text-center text-gray-500">
                Loading RFQ data...
              </div>
            )}
            {!rfqListState.loading &&
              !rfqStatusState.loading &&
              (filteredData.length > 0 ? (
                filteredData.map((rfq, index) => (
                  <ListCard
                    key={rfq._id || index}
                    rfqId={rfq.rfqId || ""}
                    title={
                      <span
                        className="block truncate hover:whitespace-normal hover:overflow-visible hover:relative"
                        title={rfq.title}
                      >
                        {rfq.title}
                      </span>
                    }
                    dateCreated={rfq.postedAt}
                    status={rfq.status}
                    statusOptions={statusOptions}
                    responses={String(rfq.proposalCount)}
                    hideImage={true}
                    onView={() => handleView(rfq._id)}
                    onEdit={() => handleEdit(rfq._id)}
                    onDelete={() => handleDelete(rfq._id)}
                    onStatusChange={(newStatus) =>
                      handleStatusChange(rfq._id, newStatus)
                    }
                    className="px-2"
                    classNames={{
                      rfqId: "!text-center !max-w-[10%]",
                      title:
                        "!text-center !max-w-[30%] truncate hover:whitespace-normal hover:overflow-visible relative",
                      dateCreated: "!text-center !max-w-[18%]",
                      status: "!text-center !max-w-[12%]",
                      responses: "!text-center !max-w-[10%]",
                    }}
                    actionClasses={{
                      container:
                        "flex flex-row items-center gap-2 justify-center",
                      view: "!max-w-[30%]",
                      edit: "!max-w-[30%]",
                      delete: "!max-w-[30%]",
                    }}
                  />
                ))
              ) : (
                <div className="py-8 text-center text-gray-500">
                  No RFQ items found for this status.
                </div>
              ))}
          </div>
        </div>

        {!rfqListState.loading &&
          !rfqStatusState.loading &&
          filteredData.length > 0 && (
            <div className="flex justify-center py-4">
              <ListPagination
                totalPages={totalPages}
                currentPage={pagination.page}
                onPageChange={handlePageChange}
              />
            </div>
          )}
      </div>
    </div>
  );
};

export default RFQListPage;