"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useRouter } from "next/navigation";
import ContactNowModal from "../../inquiry";
import ImageGallery from "./images";

const ListCard = ({ company }: { company: (typeof ListingData)[0] }) => {
  const router = useRouter();

  return (
    <div className="flex-1 w-full h-max bg-white border border-gray-200 shadow-sm hover:shadow-md rounded-lg">
      <div className="flex flex-col gap-0">
        <div className="flex flex-col sm:flex-row gap-6 px-6 pt-6 pb-4">
          {/* Image Slider */}
          <div className="w-full sm:w-max flex items-start justify-between gap-4">
            <ImageGallery images={company?.images} />
            <CheckboxMapping className="flex sm:hidden" />
          </div>

          {/* title */}
          <div className="flex-1 flex flex-col items-start gap-1 [&>*]:w-full">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center justify-start gap-1.5">
                <h2
                  role="button"
                  className="text-xl font-bold hover:underline text-black hover:drop-shadow"
                  onClick={() => router.push("/companies/details")}
                >
                  {company?.name}
                </h2>
                <div className="flex items-center gap-2 sm:hidden ">
                  <img
                    src={"/assets/pages/verified-gray.svg"}
                    alt="flag"
                    className="aspect-auto w-5 h-auto object-contain"
                  />
                  <span className="flex-1 text-xs text-stone-600">
                    Sponsored
                  </span>
                </div>
              </div>
              <CheckboxMapping className="hidden sm:flex" />
            </div>

            {/* Location */}
            <div className="flex items-center gap-1.5 mt-1 mb-2">
              <img
                src={company?.location?.flag}
                alt="flag"
                className="flex-1 aspect-auto max-w-5 w-5 h-auto object-contain"
              />
              <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 ">
                <span>
                  {company?.location?.address}
                  {", "}
                </span>
                <span>{company?.location?.label}</span>
              </div>
            </div>

            {/* Verified */}
            {company?.verified && (
              <div className="flex items-center gap-2 mb-2">
                <div className="hidden sm:flex bg-[#9F9795] text-white px-2 py-1 items-center justify-start gap-1.5 w-max max-w-max rounded-tl-sm rounded-bl-sm rounded-tr-md rounded-br-md">
                  <img
                    src={"/assets/pages/verified-white.svg"}
                    alt="flag"
                    className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
                  />
                  <span className="flex-1 text-[0.6rem] font-medium">
                    Verified
                  </span>
                </div>
                <span className="text-xs text-stone-600">Sponsored</span>
              </div>
            )}
            {/* Reviews */}
            <div className="flex items-center gap-4 mb-2 flex-wrap">
              {/* Prices */}
              <div className="flex items-center gap-3 w-full sm:w-auto">
                <span className="font-bold text-[#070707] text-2xl">
                  ${company?.offerPrice}
                </span>
                <span className="font-bold text-[#A2A2A2] text-xl line-through">
                  ${company?.actualPrice}
                </span>
              </div>

              {/* Rating */}
              <div className="flex items-center gap-2.5 w-full sm:w-auto flex-wrap">
                <img
                  src={"/assets/pages/rating-star.svg"}
                  alt="rating star"
                  className="w-5 h-5 object-contain"
                />
                <span className="text-base font-semibold text-font border-r border-slate-300 pr-2.5">
                  {company?.rating?.stars}/5
                </span>
                <span className="text-base font-semibold text-font">
                  {company?.rating?.reviewsCount} Reviews
                </span>
              </div>
            </div>



            {/* Tags */}
            <div
              className="hidden md:flex items-center flex-wrap gap-2 max-w-full md:max-w-[75%] mt-3 mb-2 overflow-hidden"
              style={{
                WebkitBoxOrient: "vertical",
                WebkitLineClamp: 2,
                maxHeight: "calc(2 * (1.3rem + 0.5rem))", // 2 lines with gap
              }}
            >
              {Array.isArray(company?.tags) &&
                company?.tags?.length > 0 &&
                company?.tags?.slice(0, 10)?.map((tag, index) => {
                  return (
                    <button
                      key={index}
                      className="flex items-center justify-center px-3 py-0.5 bg-stone-50 hover:bg-stone-100 border border-stone-300 rounded-full text-stone-800 text-xs"
                    >
                      {tag?.title}
                    </button>
                  );
                })}
            </div>

            {/* Description */}
            <div className="mt-1">
              <p
                title={company?.description}
                className="text-[0.92rem] text-stone-700 line-clamp-2"
              >
                {company?.description}
              </p>
            </div>
          </div>
        </div>

        {/* Card Footer */}
        <div className="flex flex-col sm:flex-row flex-wrap gap-4 sm:items-center justify-between border-t border-slate-200 px-4 sm:px-6 py-4">
          {/* Mobile View - 3 columns in a grid with separators */}
          <div className="w-full sm:hidden">
            <div className="grid grid-cols-3 gap-2 text-center mb-4">
              <div className="flex flex-col py-2">
                <span className="text-sm font-semibold text-black">{company?.strengthRange}</span>
                <span className="text-xs text-gray-600">Employees</span>
              </div>
              <div className="flex flex-col py-2 border-l border-r border-gray-200">
                <span className="text-sm font-semibold text-black">{company?.businessAge} Years</span>
                <span className="text-xs text-gray-600">in business</span>
              </div>
              <div className="flex flex-col py-2">
                <span className="text-sm font-semibold text-black">{company?.minOrderQuantity} Pieces</span>
                <span className="text-xs text-gray-600">Min. order</span>
              </div>
            </div>

            {/* Mobile Buttons - Same row */}
            <div className="flex flex-row gap-3">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
                onClick={() => router.push("/companies/projects#portfolios")}
              >
                Projects
              </Button>
              <div className="flex-1">
                <ContactNowModal className="w-full" />
              </div>
            </div>
          </div>

          {/* Desktop View - Original horizontal layout */}
          <div className="hidden sm:flex w-auto flex-row items-center gap-4 text-xs font-light leading-4">
            <p className="py-1">{company?.strengthRange} <span>Employees</span></p>
            <p className="py-1 border-l pl-4 border-slate-300">{company?.businessAge} <span>Years in business</span></p>
            <p className="py-1 border-l pl-4 border-slate-300">
              Min Order Value: {company?.minOrderQuantity} <span>USD</span>
            </p>
          </div>

          {/* Desktop Buttons */}
          <div className="hidden sm:flex w-auto flex-row gap-3">
            <Button
              variant="outline"
              size="sm"
              className="px-8 py-2 border-gray-300 text-gray-700 hover:bg-gray-50 min-w-[120px]"
              onClick={() => router.push("/companies/details#portfolios")}
            >
              Projects
            </Button>
            <ContactNowModal className="min-w-[120px]" />
          </div>
        </div>

      </div>
    </div>
  );
};

const CheckboxMapping = ({ className }: { className?: string }) => {
  return (
    <Checkbox
      label="Add to Compare"
      labelClassName="whitespace-nowrap"
      containerClassName={classNames("order-1 sm:order-2", className)}
    />
  );
};

export default ListCard;