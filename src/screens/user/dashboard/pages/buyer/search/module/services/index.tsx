"use client";

import ServiceCard from "@/components/elements/card/service";
import ListingFilter from "@/components/elements/filters/listings/desktop";
import { ListingData } from "@/constants/listing"; // replace with your actual path
import ComparePop from "@/screens/products/module/compare-pop";

const Companies = () => {
  // Example: Slice the first 3 companies for the demo
  const companies = ListingData.slice(0, 3); // Make sure ListingData exists and is valid

  return (
    <div className="bg-white">
      <div className="container mx-auto p-6">
        <ListingFilter />
        <div className="grid grid-cols-1  gap-4 mt-6">
          {companies.map((company, index) => (
            <ServiceCard key={index} company={company} />
          ))}
        </div>
        <div className="flex items-center justify-center mt-8 mb-4">
          <span className="flex items-center justify-center gap-2 bg-brown text-white rounded-full px-4 py-2.5 text-sm font-medium whitespace-nowrap max-w-max">
            Loading more...
          </span>
        </div>
      </div>
      <ComparePop/>
    </div>
  );
};

export default Companies;
