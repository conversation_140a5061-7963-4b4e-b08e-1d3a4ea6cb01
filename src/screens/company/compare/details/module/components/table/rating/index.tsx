"use client";

import { selectedColumn } from "@/screens/company/compare/@redux/selectors";
import classNames from "classnames";
import { Star } from "lucide-react";
import { useSelector } from "react-redux";

const CompareRating = () => {
  const current = useSelector(selectedColumn);

  return (
    <section id="rating" className="md:shadow-box rounded-lg">
      <div className="px-7 py-3 text-lg font-bold md:text-base border-b-none md:border-b border-stone-200">
        Ratings
      </div>

      <div className="px-4 md:px-0 flex flex-col md:flex-row items-start gap-4 md:gap-0 rounded-lg overflow-auto">
        <div className="flex-1 hidden lg:block w-full" />
        {Array.from({ length: 4 }).map((_, index) => {
          if (window?.innerWidth < 768 && current !== index) return null;
          return <RatingCard key={index} />;
        })}
      </div>
    </section>
  );
};

const RatingCard = () => {
  const ratingCategories = [
    { label: "Visual Appeal", rating: 4.5, color: "green" },
    { label: "Reliability", rating: 3.4, color: "orange" },
    { label: "Performance", rating: 4.4, color: "green" },
    { label: "Comfort", rating: 3.4, color: "orange" },
    { label: "Service Experience", rating: 4.4, color: "green" },
    { label: "Value for Money", rating: 3.4, color: "orange" }
  ];

  const renderStars = (rating, isMainRating = false) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const totalStars = 5;

    return (
      <div className="flex items-center gap-0.5">
        {[...Array(totalStars)].map((_, index) => {
          let starClass = "text-gray-300";
          
          if (index < fullStars) {
            starClass = isMainRating ? "text-[#347E29] fill-[#347E29]" : "text-[#347E29] fill-[#347E29]";
          } else if (index === fullStars && hasHalfStar) {
            starClass = isMainRating ? "text-[#347E29] fill-[#347E29]" : "text-[#347E29] fill-[#347E29]";
          }

          return (
            <Star
              key={index}
              size={isMainRating ? 16 : 14}
              className={starClass}
            />
          );
        })}
      </div>
    );
  };

  const getCircleColor = (color) => {
    return color === "green" ? "bg-[#347E29]" : "bg-[#F59E0B]";
  };

  return (
    <div className="relative flex flex-col gap-5 items-start [&>*]:w-full w-full flex-1 md:border-l border-stone-200 p-6 bg-white shadow-box md:shadow-none rounded-lg">
      <div className="bg-[#E6E6E6] px-4 py-1 -translate-x-6 md:hidden block">
        <span className="text-sm font-bold">
          Accenture Solutions Private Limited.
        </span>
      </div>
      
      {/* Main Rating Display */}
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <span className="font-bold text-2xl">4.2/5</span>
          {renderStars(4.2, true)}
        </div>
        <span className="font-normal text-sm text-gray-600">All Ratings</span>
      </div>

      {/* Individual Rating Categories */}
      <div className="flex flex-col gap-3 w-full">
        {ratingCategories.map((category, index) => (
          <div key={index} className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium min-w-[1.5rem]">
                {category.rating}
              </span>
              <div className={classNames(
                "rounded-full p-1.5 flex items-center justify-center",
                getCircleColor(category.color)
              )}>
                <Star
                  size={10}
                  className="text-white fill-white"
                />
              </div>
              <span className="text-sm text-gray-700">{category.label}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CompareRating;