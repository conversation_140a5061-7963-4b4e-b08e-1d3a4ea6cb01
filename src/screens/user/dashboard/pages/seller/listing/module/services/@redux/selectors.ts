import { RootState } from "@/redux/root-reducer";
import { createSelector } from "reselect";

export const reducer = (state: RootState) => state.user_services;

export const searchValue = createSelector(
  [reducer],
  (reducer) => reducer.searchValue
);

export const currentPage = createSelector(
  [reducer],
  (reducer) => reducer.currentPage
);

export const servicesList = createSelector(
  [reducer],
  (reducer) => reducer.servicesList
);

export const serviceDetails = createSelector(
  [reducer],
  (reducer) => reducer.serviceDetails
);

export const dynamicFields = createSelector(
  [reducer],
  (reducer) => reducer.dynamicFields
);

export const serviceSuggestions = createSelector(
  [reducer],
  (reducer) => reducer.serviceSuggestions
);

export const suggestedService = createSelector(
  [reducer],
  (reducer) => reducer.suggestedService
);
