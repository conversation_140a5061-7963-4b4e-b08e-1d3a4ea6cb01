"use client";

import classNames from "classnames";
import React, { FormEvent } from "react";
import { CiCirclePlus } from "react-icons/ci";
import { Input, InputType } from "../input/text";
import { X } from "lucide-react";

interface TagsInputType extends InputType {
  onActionClick?: (value?: string | number | readonly string[]) => void;
}

interface TagsDataType {
  title?: string;
  [key: string]: any;
}

interface TagsType {
  data?: TagsDataType[];
  className?: string;
  inputClassName?: string;
  input?: TagsInputType;
  onSelect?: (tag: TagsDataType) => void;
  onDelete?: (tag: TagsDataType) => void;
}

const Tags = React.forwardRef<HTMLDivElement, TagsType>(
  ({ className, input, data, onSelect, onDelete }, ref) => {
    const handleActionCLick = (e: FormEvent<HTMLFormElement>) => {
      try {
        e?.preventDefault();
        if (input?.onActionClick) {
          input?.onActionClick(input?.value);
        }
      } catch (error) {
        console.error(error);
      }
    };

    const handleSelect = (tag: TagsDataType) => {
      try {
        if (onSelect) {
          onSelect(tag);
        }
      } catch (error) {
        console.error(error);
      }
    };

    const handleDelete = (tag: TagsDataType) => {
      try {
        if (onDelete) {
          onDelete(tag);
        }
      } catch (error) {
        console.error(error);
      }
    };

    return (
      <div
        ref={ref}
        className={classNames(
          "relative w-full flex flex-col items-start gap-3.5 [&>*]:w-full",
          className
        )}
      >
        {input && (
          <form onSubmit={handleActionCLick}>
            <Input
              type="text"
              placeholder={input?.placeholder ?? "Enter Tag"}
              action={
                <button
                  type="submit"
                  className="flex items-center justify-center drop-shadow hover:text-main"
                >
                  <CiCirclePlus />
                </button>
              }
              {...input}
            />
          </form>
        )}
        {Array.isArray(data) && data?.length > 0 && (
          <div className="flex items-center flex-wrap gap-1.5">
            {data?.map((tag, index) => (
              <button
                key={index}
                className="group relative flex items-center justify-center px-3.5 py-0.5 bg-stone-50 hover:bg-stone-100 border border-stone-300 hover:border-main rounded-full text-stone-800 text-xs transition-all"
                onClick={() => handleSelect(tag)}
              >
                {tag?.title}

                <span
                  role="button"
                  className="absolute top-0 right-0.5 translate-x-1/2 -translate-y-1/2 flex items-center justify-center border border-stone-200 group-hover:border-main rounded-full p-0.5 bg-white hover:bg-main hover:text-white transition-all hover:scale-150"
                  onClick={() => handleDelete(tag)}
                >
                  <X className="w-2 h-2" />
                </span>
              </button>
            ))}
          </div>
        )}
      </div>
    );
  }
);

Tags.displayName = "Tags";

export { Tags };
export type { TagsInputType, TagsType };
