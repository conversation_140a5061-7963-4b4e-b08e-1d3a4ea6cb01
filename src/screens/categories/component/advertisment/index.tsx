"use client";

import {
  Carousel,
  CarouselBullet,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import React, { useEffect, useState } from "react";

interface ImageItem {
  id: number;
  src: string;
  alt: string;
}

interface SliderImageProps {
  image: ImageItem;
}

const ImageSlider: React.FC = () => {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);

  const images: ImageItem[] = [
    {
      id: 1,
      src: "./freepik__upload__40729.png",
      alt: "Uploaded Visual",
    },
    {
      id: 2,
      src: "./freepik__upload__40729.png",
      alt: "Uploaded Visual",
    },
    {
      id: 3,
      src: "./freepik__upload__40729.png",
      alt: "Uploaded Visual",
    },
    {
      id: 4,
      src: "./freepik__upload__40729.png",
      alt: "Uploaded Visual",
    },
  ];

  const SliderImage: React.FC<SliderImageProps> = ({ image }) => (
    <div className="w-full h-full">
      <img
        src={image.src}
        alt={image.alt}
        className="w-full h-auto object-cover hover:opacity-90 transition-opacity"
      />
    </div>
  );

  return (
    <div className="flex justify-center w-full mt-7 px-0">
      <div className="w-full max-w-screen-2xl 2xl:max-w-[90%]">
        <div className="p-0 bg-transparent shadow-none border-none">
          {/* Desktop Carousel - Hidden on mobile */}
          <div className="hidden md:block relative">
            <Carousel
              opts={{
                align: "center",
                loop: true,
                dragFree: false,
              }}
              className="w-full"
            >
              <CarouselContent>
                {images.map((image) => (
                  <CarouselItem key={image.id} className="lg:basis-full">
                    <SliderImage image={image} />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="bg-white shadow-lg border border-gray-100 rounded-full h-12 w-12 left-4 focus:ring-1 focus:ring-gray-200 focus:outline-none" />
              <CarouselNext className="bg-white shadow-lg border border-gray-100 rounded-full h-12 w-12 right-4 focus:ring-1 focus:ring-gray-200 focus:outline-none" />
              <CarouselBullet
                containerClassName="absolute bottom-4 left-0 right-0"
                className="w-2 h-2 bg-white hover:bg-gray-300"
              />
            </Carousel>
          </div>

          {/* Mobile Slider - Visible only on mobile */}
          <div className="block md:hidden relative">
            <Carousel
              opts={{
                align: "start",
                loop: true,
                dragFree: false,
                // draggable: true
              }}
              className="w-full"
            >
              <CarouselContent>
                {images.map((image) => (
                  <CarouselItem key={image.id} className="basis-full">
                    <SliderImage image={image} />
                  </CarouselItem>
                ))}
              </CarouselContent>
              {/* Using only CarouselBullet for mobile navigation (no next/prev buttons) */}
              <CarouselBullet containerClassName="mt-4" className="w-3 h-3" />
            </Carousel>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageSlider;
