export interface ProductUpdateStep1Type {
  business_name: string;
  role: string;
  ownership_type: string;
  business_type: string;
  industry_category: string;
  sub_category: string;
  sub_sub_category: string;
  business_registration_number: string;
  tax_number: string;
  meta_title: string;
  meta_description: string;
  meta_keywords: string;
  business_photos: File[];
  deletedFiles: File[];
   productDetails?: {
    targetCountries?: string[];
    quantity?: number;
    volume?: string;
    preferredProviderType?: string;
    requirementUrgency?: string;
    budgetRange?: {
      min?: number;
      max?: number;
      currency?: string;
    };
  }
}

export type ProductUpdateValuesType = ProductUpdateStep1Type;
