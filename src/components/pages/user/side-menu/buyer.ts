import { MenuData } from "./types";

export const BuyererMenuData: MenuData = [
    {
        icon: "/assets/pages/user/dashboard/sidebar/seller/dashboard.svg",
        title: "Insights",
        activePathname: ["/dashboard/buyer"],
        pathname: "/dashboard/buyer",
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/listing.svg",
        title: "Profile",
        pathname: "/dashboard/buyer/Bussiness",
        activePathname: ["/dashboard/buyer/bussiness/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
        title: "Search & Compare",
        pathname: "/dashboard/buyer/search",
        activePathname: ["/dashboard/buyer/search/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
        title: "Inbox"
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
        title: "Request Quotations",
        pathname: "/dashboard/buyer/request",
        activePathname: ["/dashboard/buyer/request/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
        title: "Manage Quotations",
        pathname: "/dashboard/buyer/manage",
        activePathname: ["/dashboard/buyer/manage/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
        title: "Reviews",
        pathname: "/dashboard/buyer/reviews",
        activePathname: ["/dashboard/buyer/reviews/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
        title: "Favourite",
        pathname: "/dashboard/buyer/favourite",
        activePathname: ["/dashboard/buyer/favourite/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
        title: "Photos & Media",
        pathname: "/dashboard/buyer/media",
        activePathname: ["/dashboard/buyer/media/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/paid.svg",
        title: "Paid Services",
        pathname: "/dashboard/buyer/service",
        activePathname: ["/dashboard/buyer/service/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/reviews.svg",
        title: "Settings",
        pathname: "/dashboard/buyer/settings",
        activePathname: ["/dashboard/buyer/settings/*"],
      },
      {
        icon: "/assets/pages/user/dashboard/sidebar/seller/media.svg",
        title: "Support",
        pathname: "/dashboard/buyer/support",
        activePathname: ["/dashboard/buyer/support/*"],
      }
];
