"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import { Button } from "@/components/ui/button";
import { RootDispatch } from "@/redux/store";
import classNames from "classnames";
import { debounce } from "lodash";
import { AlertCircle } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";

const data = Array.from({ length: 4 })?.map((_, index) => {
  return {
    _id: String(index + 1),
    title: "Basic Plan",
    banner: "In-Listing Ad Banner",
    placement: "Between every 5 listings on category pages",
    banner_size: "468 x 60 px",
    price: "€250",
    duration: "1 Month",
    features: "Targeted visibility within listings.",
  };
});

const MembershipTable = () => {
  const dispatch = useDispatch<RootDispatch>();
const router = useRouter();

  const pageNow = "2";

  const pagination = {
    totalPages: 3,
    page: 2,
  };

  const finalData = data;

  useEffect(() => {
    // dispatch(getProducts());
  }, [dispatch]);

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (searchTerm?: string, page?: string) =>
          //   dispatch(getProducts({ searchTerm, page })),
          500
      ),
    [dispatch]
  );

  const handlePagination = (page: string) => {
    // dispatch(Actions.setCurrentPage(page));
    // debouncedSearch(search, page);
  };

  //   const isDataThere = !loading && productData && pagination?.totalPages > 1;

  return (
    <div className="space-y-4 max-w-full overflow-hidden">
      <div className="whitespace-nowrap w-full overflow-x-auto">
        <div className="flex flex-col items-start gap-2.5 [&>*]:w-full">
          {Array.isArray(finalData) && finalData?.length > 0 ? (
            <>
              <ListCardHeader
                className="whitespace-nowrap"
                data={[
                  {
                    label: "Plan Name",
                    key: "title",
                    className: "max-w-[10%] text-start",
                  },
                  {
                    label: "banner Type",
                    key: "banner",
                    className: "max-w-[12%] text-start",
                  },
                  {
                    label: "Placement",
                    key: "placement",
                    className: "max-w-[32%] text-start",
                  },
                  {
                    label: "Banner Size",
                    key: "banner_size",
                    className: "max-w-[8%] text-center",
                  },
                  {
                    label: "price",
                    key: "price",
                    className: "max-w-[8%] text-center",
                  },
                  {
                    label: "Duration",
                    key: "duration",
                    className: "max-w-[8%] text-center",
                  },
                  {
                    label: "Features",
                    key: "features",
                    className: "max-w-[11%] text-start",
                  },
                  {
                    label: "Select Plan",
                    key: "action",
                    className: "max-w-[11%] text-center",
                  },
                ]}
              />
              {finalData?.map((prop, index: number) => {
                return (
                  <ListCard
                    key={prop?._id + String(index)}
                    title={prop?.title}
                    additionalStringFields={[
                      {
                        label: prop?.banner,
                        className: "max-w-[12%] text-wrap text-start text-xs",
                      },
                      {
                        label: prop?.placement,
                        className: "max-w-[32%] text-start text-wrap text-xs",
                      },
                      {
                        label: prop?.banner_size,
                        className: "max-w-[8%] text-center text-xs",
                      },
                      {
                        label: prop?.price,
                        className: "max-w-[8%] text-center text-xs",
                      },
                      {
                        label: prop?.duration,
                        className: "max-w-[8%] text-center text-xs",
                      },
                      {
                        label: prop?.features,
                        className: "max-w-[11%] text-start text-wrap text-xs",
                      },
                    ]}
                    classNames={{
                      title: "!max-w-[10%] text-start font-semibold",
                    }}
                    actionClasses={{
                      container: "!max-w-[11%] text-center",
                    }}
                    hideImage
                    hideAction
                    // onSelect={() => router.push(prop?._id)}
                    additionalActions={
                      <div className="w-full max-w-[11%] my-2">
                        <Button
                          variant="main-revert"
                          className={classNames("flex-1 w-full")}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          Choose
                        </Button>
                      </div>
                    }
                  />
                );
              })}
            </>
          ) : (
            <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
              <AlertCircle /> No Plans Available
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MembershipTable;
