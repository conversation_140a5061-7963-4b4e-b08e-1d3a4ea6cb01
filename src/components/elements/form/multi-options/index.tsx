import { Input<PERSON>abel } from "@/components/ui/input/components/label";
import { Input } from "@/components/ui/input/text";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import classNames from "classnames";
import { Plus, X } from "lucide-react";
import { ChangeEvent, FC, useState } from "react";
import SortableOptions from "./options";

type MultiOptionInputTargetType = {
  target: {
    name: string;
    value: string;
    option: string[];
  };
};

type ValuesType = {
  name?: string;
  option?: string[];
};

interface MultiOptionInputType {
  className?: string;
  name?: string;
  value?: ValuesType;
  maxLength?: number;
  onChange?: (e: MultiOptionInputTargetType) => void;
}

const MultiOptionInput: FC<MultiOptionInputType> = ({
  className,
  value,
  name,
  maxLength = 20,
  onChange,
}) => {
  const [values, setValues] = useState<ValuesType | null>(value ?? null);
  const [options, setOptions] = useState<string[]>(value?.option ?? []);

  const handleAddOption = () => {
    try {
      setOptions((prevOptions) => [...prevOptions, ""]);
    } catch (error) {
      console.error(error);
    }
  };

  const handleOptionRemove = (index: number) => {
    setOptions((prevOptions) => prevOptions.filter((_, i) => i !== index));
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>, index?: number) => {
    try {
      const { name: localName, value } = e.target;

      const newOptions = [...options];

      if (localName === "option" && index !== undefined) {
        newOptions[index] = value;
        setOptions(newOptions);
      }

      setValues((prev) => {
        const updatedValues = {
          ...prev,
          [localName]: localName === "option" ? newOptions : value,
        };

        if (onChange) {
          onChange({
            target: {
              name: name ?? "MultiOptionInput",
              value: updatedValues?.name ?? "",
              option: updatedValues?.option ?? [],
            },
          });
        }

        return updatedValues;
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = options.findIndex((_, index) => index === active.id);
      const newIndex = options.findIndex((_, index) => index === over.id);

      setOptions((prevOptions) => arrayMove(prevOptions, oldIndex, newIndex));
    }
  };

  return (
    <div
      className={classNames(
        "flex-1 relative bg-light/20 border border-slate-200 rounded-lg px-12 py-12",
        className
      )}
    >
      <button className="absolute right-2 top-2 z-10 text-stone-400 hover:text-stone-600 transition-all">
        <X className="w-5 h-5" />
      </button>
      <div className="flex flex-col items-start gap-5 [&>*]:w-full">
        <div className="flex items-center gap-5">
          <InputLabel className="min-w-[14%] text-base font-normal text-stone-500">
            Name
          </InputLabel>
          <div className="flex-1 flex items-center gap-2">
            <Input
              type="text"
              name="name"
              onChange={handleChange}
              value={value?.name}
              placeholder="Name"
              maxLength={maxLength}
              action={
                <span className="px-3 py-2 bg-light rounded-md">
                  {value?.name?.toString()?.length}/{maxLength}
                </span>
              }
              wrapperClassName="!pe-0"
              containerClassName="flex-1"
            />
            <div className="w-16 ms-auto" />
          </div>
        </div>
        <div className="flex items-start gap-5">
          <InputLabel className="mt-2 min-w-[14%] text-base font-normal text-stone-500">
            Options
          </InputLabel>
          <div className="flex-1 flex flex-col items-start gap-5 [&>*]:w-full">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext items={options.map((_, index) => index)}>
                {Array.isArray(options) &&
                  options?.length > 0 &&
                  options?.map((option, index) => {
                    return (
                      <SortableOptions
                        key={index}
                        id={index}
                        index={index}
                        onChange={(e: any) => handleChange(e, index)}
                        value={option}
                        maxLength={maxLength}
                        onDelete={() => handleOptionRemove(index)}
                      />
                    );
                  })}
              </SortableContext>
            </DndContext>

            <div className="flex items-center gap-2">
              <button
                className="flex-1 flex items-center justify-center gap-2 p-1.5 rounded-md border border-dashed border-stone-500 hover:bg-slate-100 transition-all"
                onClick={() => handleAddOption()}
              >
                <Plus /> <span>Add New Option</span>
              </button>
              <div className="w-16 ms-auto" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export { MultiOptionInput };
export type { MultiOptionInputTargetType };
