import FrequentlyAskedQuestions from "@/components/elements/list/faq";
import MembershipTable from "./table";

const faqData = Array.from({ length: 4 })?.map((_) => ({
  title: "Is listing my business free of charge?",
  description:
    "Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and.",
}));

const Membership = () => {
  return (
    <div className="w-full">
      <MembershipTable />

      <div className="w-full bg-white  shadow-md rounded-md px-7 py-10 mt-8">
        <h3 className="font-bold mb-4">Frequently Asked Questions</h3>
        <FrequentlyAskedQuestions type="single" data={faqData} />
      </div>
    </div>
  );
};

export default Membership;
