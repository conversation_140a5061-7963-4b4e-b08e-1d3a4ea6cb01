export const ownershipTypes = [
  { label: "Sole Proprietorship", value: "Sole Proprietorship" },
  { label: "General Partnership", value: "General Partnership" },
  { label: "Limited Partnership (LP)", value: "Limited Partnership (LP)" },
  {
    label: "Limited Liability Partnership (LLP)",
    value: "Limited Liability Partnership (LLP)",
  },
  { label: "Private Limited Company", value: "Private Limited Company" },
  { label: "Public Limited Company", value: "Public Limited Company" },
  {
    label: "Limited Liability Company (LLC)",
    value: "Limited Liability Company (LLC)",
  },
  { label: "Cooperative (Co-op)", value: "Cooperative (Co-op)" },
  {
    label: "State-Owned Enterprise (SOE)",
    value: "State-Owned Enterprise (SOE)",
  },
  {
    label: "Nonprofit Organization / NGO",
    value: "Nonprofit Organization / NGO",
  },
  { label: "Franchise", value: "Franchise" },
  { label: "Joint Venture (JV)", value: "Joint Venture (JV)" },
  { label: "Holding Company", value: "Holding Company" },
  { label: "Subsidiary Company", value: "Subsidiary Company" },
  { label: "Trust", value: "Trust" },
  {
    label: "International Business Company",
    value: "International Business Company",
  },
];

export const businessTypes = [
  { label: "Manufacturer", value: "Manufacturer" },
  { label: "Exporter", value: "Exporter" },
  { label: "Importer", value: "Importer" },
  { label: "Wholesaler", value: "Wholesaler" },
  { label: "Distributor", value: "Distributor" },
  { label: "Trader / Trading Company", value: "Trader / Trading Company" },
  { label: "Retailer", value: "Retailer" },
  { label: "Service Provider", value: "Service Provider" },
  { label: "Franchisee", value: "Franchisee" },
  { label: "Agent / Representative", value: "Agent / Representative" },
  {
    label: "OEM (Original Equipment Manufacturer)",
    value: "OEM (Original Equipment Manufacturer)",
  },
  {
    label: "ODM (Original Design Manufacturer)",
    value: "ODM (Original Design Manufacturer)",
  },
  { label: "Contractor / Subcontractor", value: "Contractor / Subcontractor" },
  { label: "System Integrator", value: "System Integrator" },
  { label: "Solution Provider", value: "Solution Provider" },
  { label: "E-commerce Seller", value: "E-commerce Seller" },
  { label: "Dropshipper", value: "Dropshipper" },
  { label: "Consultant", value: "Consultant" },
  { label: "Sourcing Agent", value: "Sourcing Agent" },
];

export const keyBusinessDesignations = [
  { label: "Founder", value: "Founder" },
  { label: "Co-Founder", value: "Co-Founder" },
  { label: "Owner", value: "Owner" },
  { label: "Director", value: "Director" },
  { label: "Managing Director (MD)", value: "Managing Director (MD)" },
  {
    label: "Chief Executive Officer (CEO)",
    value: "Chief Executive Officer (CEO)",
  },
  {
    label: "Chief Operating Officer (COO)",
    value: "Chief Operating Officer (COO)",
  },
  {
    label: "Chief Financial Officer (CFO)",
    value: "Chief Financial Officer (CFO)",
  },
  {
    label: "Chief Marketing Officer (CMO)",
    value: "Chief Marketing Officer (CMO)",
  },
  {
    label: "Chief Technology Officer (CTO)",
    value: "Chief Technology Officer (CTO)",
  },
  { label: "General Manager (GM)", value: "General Manager (GM)" },
  { label: "Partner", value: "Partner" },
  {
    label: "Business Development Manager",
    value: "Business Development Manager",
  },
  { label: "Sales Manager", value: "Sales Manager" },
  { label: "Marketing Manager", value: "Marketing Manager" },
  { label: "Operations Manager", value: "Operations Manager" },
  { label: "Product Manager", value: "Product Manager" },
  {
    label: "Purchase Manager / Procurement Head",
    value: "Purchase Manager / Procurement Head",
  },
  { label: "Export Manager", value: "Export Manager" },
  { label: "Import Manager", value: "Import Manager" },
  { label: "Logistics Manager", value: "Logistics Manager" },
  { label: "HR Manager", value: "HR Manager" },
  { label: "IT Manager", value: "IT Manager" },
  { label: "Finance Manager", value: "Finance Manager" },
  { label: "Consultant", value: "Consultant" },
  { label: "Advisor", value: "Advisor" },
  { label: "Executive / Officer", value: "Executive / Officer" },
  { label: "Assistant Manager", value: "Assistant Manager" },
  { label: "Coordinator", value: "Coordinator" },
  { label: "Administrator", value: "Administrator" },
];

export const regions = [
  { label: "North America", value: "North America" },
  { label: "South America", value: "South America" },
  { label: "Europe", value: "Europe" },
  { label: "Asia", value: "Asia" },
  { label: "Africa", value: "Africa" },
  { label: "Middle East", value: "Middle East" },
  { label: "Oceania", value: "Oceania" },
  { label: "Caribbean", value: "Caribbean" },
  { label: "Southeast Asia", value: "Southeast Asia" },
  { label: "Central Asia", value: "Central Asia" },
  { label: "Nordic Countries", value: "Nordic Countries" },
  { label: "CIS Countries", value: "CIS Countries" },
  { label: "Baltic States", value: "Baltic States" },
  { label: "Gulf Region", value: "Gulf Region" },
  { label: "Sub-Saharan Africa", value: "Sub-Saharan Africa" },
];

export const languages = [
  { label: "English", value: "English" },
  { label: "Albanian", value: "Albanian" },
  { label: "Mandarin Chinese", value: "Mandarin Chinese" },
  { label: "Spanish", value: "Spanish" },
  { label: "Hindi", value: "Hindi" },
  { label: "Arabic", value: "Arabic" },
  { label: "French", value: "French" },
  { label: "Portuguese", value: "Portuguese" },
  { label: "Russian", value: "Russian" },
  { label: "Japanese", value: "Japanese" },
  { label: "Korean", value: "Korean" },
  { label: "Bengali", value: "Bengali" },
  { label: "Punjabi", value: "Punjabi" },
  { label: "Urdu", value: "Urdu" },
  { label: "Vietnamese", value: "Vietnamese" },
  { label: "Italian", value: "Italian" },
  { label: "Turkish", value: "Turkish" },
  { label: "Tamil", value: "Tamil" },
  { label: "Telugu", value: "Telugu" },
  { label: "Polish", value: "Polish" },
  { label: "Dutch", value: "Dutch" },
  { label: "Thai", value: "Thai" },
  { label: "Swahili", value: "Swahili" },
  { label: "Farsi (Persian)", value: "Farsi (Persian)" },
  { label: "Greek", value: "Greek" },
  { label: "Swedish", value: "Swedish" },
  { label: "Czech", value: "Czech" },
  { label: "Hungarian", value: "Hungarian" },
  { label: "Romanian", value: "Romanian" },
  { label: "Finnish", value: "Finnish" },
  { label: "Danish", value: "Danish" },
];

export const paymentMethods = [
  { label: "Credit Cards", value: "Credit Cards" },
  { label: "Debit Cards", value: "Debit Cards" },
  { label: "Bank Transfers", value: "Bank Transfers" },
  { label: "Digital Wallets", value: "Digital Wallets" },
  { label: "Cryptocurrency", value: "Cryptocurrency" },
  { label: "Buy Now, Pay Later", value: "Buy Now, Pay Later" },
  { label: "Cash on Delivery (COD)", value: "Cash on Delivery (COD)" },
  { label: "Mobile Payments", value: "Mobile Payments" },
  { label: "E-checks", value: "E-checks" },
  { label: "Prepaid Cards", value: "Prepaid Cards" },
  { label: "Payment Gateways", value: "Payment Gateways" },
  { label: "Bank Drafts / Checks", value: "Bank Drafts / Checks" },
  { label: "Invoice Payments", value: "Invoice Payments" },
];

export const workShifts = [
  { label: "9:00 AM – 5:00 PM", value: "9:00 AM – 5:00 PM" },
  { label: "8:00 AM – 4:00 PM", value: "8:00 AM – 4:00 PM" },
  { label: "10:00 AM – 6:00 PM", value: "10:00 AM – 6:00 PM" },
  { label: "7:00 AM – 3:00 PM", value: "7:00 AM – 3:00 PM" },
  { label: "8:30 AM – 5:30 PM", value: "8:30 AM – 5:30 PM" },
  { label: "12:00 PM – 8:00 PM", value: "12:00 PM – 8:00 PM" },
  { label: "6:00 AM – 2:00 PM", value: "6:00 AM – 2:00 PM" },
  { label: "2:00 PM – 10:00 PM", value: "2:00 PM – 10:00 PM" },
  { label: "10:00 PM – 6:00 AM", value: "10:00 PM – 6:00 AM" },
  { label: "9:00 AM – 1:00 PM", value: "9:00 AM – 1:00 PM" },
  { label: "1:00 PM – 5:00 PM", value: "1:00 PM – 5:00 PM" },
  { label: "Flexible Hours", value: "Flexible Hours" },
  { label: "Weekend Shifts", value: "Weekend Shifts" },
  { label: "Part-Time", value: "Part-Time" },
];

export const industries = [
  { label: "Accounting & Auditing", value: "Accounting & Auditing" },
  { label: "Advertising & Marketing", value: "Advertising & Marketing" },
  { label: "Agriculture & Farming", value: "Agriculture & Farming" },
  { label: "AI & Machine Learning", value: "AI & Machine Learning" },
  { label: "Automotive", value: "Automotive" },
  { label: "Beauty & Personal Care", value: "Beauty & Personal Care" },
  { label: "Biotechnology", value: "Biotechnology" },
  { label: "Chemicals", value: "Chemicals" },
  { label: "Construction & Engineering", value: "Construction & Engineering" },
  { label: "Consumer Electronics", value: "Consumer Electronics" },
  { label: "Consulting", value: "Consulting" },
  { label: "Cybersecurity", value: "Cybersecurity" },
  { label: "Digital Marketing", value: "Digital Marketing" },
  { label: "E-commerce", value: "E-commerce" },
  { label: "EdTech", value: "EdTech" },
  { label: "Electricity, Water & Gas (Utilities)", value: "Electricity, Water & Gas (Utilities)" },
  { label: "Environmental Services", value: "Environmental Services" },
  { label: "Fashion & Apparel", value: "Fashion & Apparel" },
  { label: "Film & Television", value: "Film & Television" },
  { label: "Fintech", value: "Fintech" },
  { label: "Food & Beverage (Manufacturing)", value: "Food & Beverage (Manufacturing)" },
  { label: "Food & Beverage (Retail)", value: "Food & Beverage (Retail)" },
  { label: "Forestry & Timber", value: "Forestry & Timber" },
  { label: "Gaming & Esports", value: "Gaming & Esports" },
  { label: "Hardware & Semiconductors", value: "Hardware & Semiconductors" },
  { label: "Health & Wellness", value: "Health & Wellness" },
  { label: "Higher Education", value: "Higher Education" },
  { label: "Hospitals & Healthcare Services", value: "Hospitals & Healthcare Services" },
  { label: "Industrial Goods & Services", value: "Industrial Goods & Services" },
  { label: "Information Technology & Services", value: "Information Technology & Services" },
  { label: "Insurance", value: "Insurance" },
  { label: "K-12 Education", value: "K-12 Education" },
  { label: "Legal Services", value: "Legal Services" },
  { label: "Luxury Goods", value: "Luxury Goods" },
  { label: "Machinery & Equipment", value: "Machinery & Equipment" },
  { label: "Management Consulting", value: "Management Consulting" },
  { label: "Marketing & Advertising", value: "Marketing & Advertising" },
  { label: "Medical Devices", value: "Medical Devices" },
  { label: "Mining & Metals", value: "Mining & Metals" },
  { label: "Music & Audio Production", value: "Music & Audio Production" },
  { label: "Oil & Gas", value: "Oil & Gas" },
  { label: "Others", value: "Others" },
  { label: "Packaging & Containers", value: "Packaging & Containers" },
  { label: "Pharmaceuticals", value: "Pharmaceuticals" },
  { label: "Publishing & Print Media", value: "Publishing & Print Media" },
  { label: "Real Estate & Property Management", value: "Real Estate & Property Management" },
  { label: "Research & Development", value: "Research & Development" },
  { label: "Software & SaaS", value: "Software & SaaS" },
  { label: "Telecommunications", value: "Telecommunications" },
  { label: "Textiles & Apparel", value: "Textiles & Apparel" },
  { label: "Travel, Leisure & Hospitality", value: "Travel, Leisure & Hospitality" },
  { label: "Venture Capital & Private Equity", value: "Venture Capital & Private Equity" },
  { label: "Waste Management", value: "Waste Management" }
];

export const businessModels = [
  { label: "B2B – Business to Business", value: "B2B – Business to Business" },
  { label: "B2C – Business to Consumer", value: "B2C – Business to Consumer" },
  { label: "B2B2C – Business to Business to Consumer", value: "B2B2C – Business to Business to Consumer" },
  { label: "B2G – Business to Government", value: "B2G – Business to Government" },
  { label: "B2B2G – Business to Business to Government", value: "B2B2G – Business to Business to Government" },
  { label: "B2E – Business to Employee", value: "B2E – Business to Employee" },
  { label: "C2C – Consumer to Consumer", value: "C2C – Consumer to Consumer" },
  { label: "C2B – Consumer to Business", value: "C2B – Consumer to Business" },
  { label: "D2C – Direct to Consumer", value: "D2C – Direct to Consumer" },
  { label: "G2B – Government to Business", value: "G2B – Government to Business" },
  { label: "G2C – Government to Consumer", value: "G2C – Government to Consumer" },
  { label: "P2P – Peer to Peer", value: "P2P – Peer to Peer" }
];

export const unitsOfMeasurement = [
  { label: "Piece (pc)", value: "Piece (pc)" },
  { label: "Kilogram (kg)", value: "Kilogram (kg)" },
  { label: "Gram (g)", value: "Gram (g)" },
  { label: "Metric Ton (MT or tonne)", value: "Metric Ton (MT or tonne)" },
  { label: "Liter (L)", value: "Liter (L)" },
  { label: "Milliliter (ml)", value: "Milliliter (ml)" },
  { label: "Meter (m)", value: "Meter (m)" },
  { label: "Centimeter (cm)", value: "Centimeter (cm)" },
  { label: "Millimeter (mm)", value: "Millimeter (mm)" },
  { label: "Square Meter (m²)", value: "Square Meter (m²)" },
  { label: "Cubic Meter (m³)", value: "Cubic Meter (m³)" },
  { label: "Inch (in)", value: "Inch (in)" },
  { label: "Foot (ft)", value: "Foot (ft)" },
  { label: "Yard (yd)", value: "Yard (yd)" },
  { label: "Gallon (gal)", value: "Gallon (gal)" },
  { label: "Pound (lb)", value: "Pound (lb)" },
  { label: "Ounce (oz)", value: "Ounce (oz)" },
  { label: "Dozen (dz)", value: "Dozen (dz)" },
  { label: "Pack (pk)", value: "Pack (pk)" },
  { label: "Box (bx)", value: "Box (bx)" },
  { label: "Carton (ctn)", value: "Carton (ctn)" },
  { label: "Roll", value: "Roll" },
  { label: "Sheet", value: "Sheet" },
  { label: "Bag", value: "Bag" },
  { label: "Bundle", value: "Bundle" },
  { label: "Can", value: "Can" },
  { label: "Bottle", value: "Bottle" },
  { label: "Barrel (bbl)", value: "Barrel (bbl)" },
  { label: "Hour (hr)", value: "Hour (hr)" },
  { label: "Minute (min)", value: "Minute (min)" },
  { label: "Second (sec)", value: "Second (sec)" },
  { label: "Day", value: "Day" },
  { label: "Week", value: "Week" },
  { label: "Month", value: "Month" },
  { label: "Year", value: "Year" },
  { label: "Unit", value: "Unit" },
  { label: "Set", value: "Set" },
  { label: "Tray", value: "Tray" },
  { label: "Pair", value: "Pair" },
  { label: "Tube", value: "Tube" }
];

