"use client";

import Dropdown from "@/components/ui/dropdown";
import DropdownList from "@/components/ui/dropdown/list";
import { Skeleton } from "@/components/ui/loader/skeleton";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import useScrolled from "@/lib/hooks/useScrolled";
import classNames from "classnames";
import { Fragment, lazy, Suspense, useRef } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import Link from "next/link";

export const TabsData = [
  {
    title: "Home",
    path: "/company/website",
  },
  {
    title: "About Us",
    path: "/company/website/about",
  },
  {
    title: "Products",
    path: "/company/website/products",
    menu: [
      {
        title: "Product 1",
        path: "/company/website/products/1",
      },
      {
        title: "Product 2",
        path: "/company/website/products/2",
      },
      {
        title: "Product 3",
        path: "/company/website/products/3",
      },
      {
        title: "Product 4",
        path: "/company/website/products/4",
      },
    ],
  },
  {
    title: "Services",
    path: "/company/website/services",
    menu: [
      {
        title: "Services 1",
        path: "/company/website/services/1",
      },
      {
        title: "Services 2",
        path: "/company/website/services/2",
      },
      {
        title: "Services 3",
        path: "/company/website/services/3",
      },
      {
        title: "Services 4",
        path: "/company/website/services/4",
      },
    ],
  },
  {
    title: "Projects",
    path: "/company/website/projects",
  },
  {
    title: "Reviews",
    path: "/company/website/reviews",
  },
  {
    title: "Faq",
    path: "/company/website/faq",
  },
  {
    title: "Contact Us",
    path: "/company/website/contact",
    hideTab: false,
  },
];

export const CompanyInformationTabs = () => {
  const router = useRouter();
  const pathname = usePathname();
  const initialActive = TabsData?.[0]?.path;
  const isScrolled = useScrolled();

  return (
    <div
      className={classNames(
        "bg-white border-b border-stone-300 hidden md:block",
        isScrolled && "shadow-md sticky top-0 z-20"
      )}
    >
      <div className="container">
        <Tabs
          defaultValue={initialActive}
          value={pathname ?? undefined}
          className="no-scrollbar"
        >
          <TabsList className="flex-nowrap !py-0">
            {TabsData?.map((tab: any, index) => {
              if (!tab?.title || tab?.hideTab) return null;

              if (tab?.menu) {
                return (
                  <Dropdown
                    key={index}
                    inlineAlign="left"
                    content={
                      <DropdownList
                        data={tab?.menu}
                        onSelect={(option) => router.push(option?.path)}
                        className="min-w-min w-full"
                        closeOnSelect
                      />
                    }
                    hover
                    contentClassName="z-20 !w-full"
                  >
                    <TabsTrigger
                      key={index}
                      value={tab?.path}
                      className="flex-1 py-2 !text-sm !font-bold capitalize before:data-[state=active]:bottom-0 hover:bg-main/10"
                    >
                      <Link href={tab?.path}>{tab?.title}</Link>
                    </TabsTrigger>
                  </Dropdown>
                );
              }

              return (
                <TabsTrigger
                  key={index}
                  value={tab?.path}
                  className={classNames(
                    "flex-1 py-2 !text-sm !font-bold capitalize before:data-[state=active]:bottom-0 hover:bg-main/10"
                  )}
                >
                  <Link href={tab?.path}>{tab?.title}</Link>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>
      </div>
    </div>
  );
};

const homeRenderData = [
  {
    title: "Company Banner",
    path: "banner",
    component: lazy(() => import("./components/information/banner")),
    screenWidth: true,
  },
  {
    title: "About Company",
    path: "company",
    component: lazy(() =>
      import("../../details/module/components/information/about")
    ),
  },
  {
    path: "company",
    component: lazy(() =>
      import("../../details/module/components/information/photos")
    ),
  },
  {
    title: "products & Services",
    path: "products",
    component: lazy(() =>
      import("../../details/module/components/information/products")
    ),
  },
  {
    title: "Portfolio/Case Studies",
    path: "portfolios",
    component: lazy(() =>
      import("../../details/module/components/information/portfolios")
    ),
  },
  {
    title: "Clientele",
    path: "clientele",
    component: lazy(() =>
      import("../../details/module/components/information/clientele")
    ),
  },
  {
    title: "Key Features",
    path: "features",
    component: lazy(() =>
      import("../../details/module/components/information/features")
    ),
  },
  {
    title: "Key Features",
    path: "features",
    hideTab: true,
    ad: true,
    component: lazy(() => import("@/components/layout/ads/banner")),
  },
  {
    title: "Reviews",
    path: "reviews",
    component: lazy(() =>
      import("../../details/module/components/information/reviews")
    ),
  },
  {
    title: "Contact",
    path: "contact",
    hideTab: true,
    component: lazy(() =>
      import("../../details/module/components/information/contact")
    ),
  },
  {
    title: "Get a Quote",
    path: "quote",
    component: lazy(() =>
      import("../../details/module/components/information/quote")
    ),
  },
];

const CompanyInformation = () => {
  const sectionRefs = useRef<Record<string, HTMLElement>>({});

  return (
    <div className="mb-8">
      <div className="relative p-0">
        <div className="grid grid-cols-1 items-start gap-6 w-full max-w-full">
          <div className="flex flex-col items-start gap-6 w-full max-w-full">
            {homeRenderData?.map((tab, index) => {
              if (!tab.component) return null;

              return (
                <Fragment key={index}>
                  <ErrorBoundary fallback={<div>Failed to load section</div>}>
                    <div
                      className={classNames(
                        "w-full",
                        !tab?.screenWidth && "sm:container"
                      )}
                    >
                      <Suspense fallback={<Skeleton className="w-full h-44" />}>
                        <section
                          id={tab.path}
                          ref={(el) => {
                            if (el) sectionRefs.current[tab.path] = el;
                          }}
                          className={classNames(
                            "w-full sm:scroll-mt-44 sm:shadow-md rounded-md",
                            {
                              "bg-white": ["company"]?.includes(tab?.path),
                              "bg-[#FAFAFA]": [
                                "products",
                                "features",
                                "reviews",
                                "contact",
                                "related",
                                "clientele",
                                "portfolios",
                              ]?.includes(tab?.path),
                              "bg-gradient-to-l from-[#FFF5F6] via-[#FDFDFD]  to-[#FFFAF4]": [
                                "quote",
                              ]?.includes(tab?.path),
                              "px-6 md:px-8 py-7 ":
                                !tab?.ad && !tab?.screenWidth,
                            }
                          )}
                        >
                          <tab.component />
                        </section>
                      </Suspense>
                    </div>
                  </ErrorBoundary>
                </Fragment>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyInformation;
