"use client";

import { RefObject, useEffect } from "react";

type RefType = RefObject<HTMLElement | null>;
type RefsInput = RefType | RefType[] | undefined;

const useOutsideAlert = (
  refs?: RefsInput,
  callback?: (event: Event, isOutside?: boolean) => void,
  deactivate?: boolean
) => {
  useEffect(() => {
    if (deactivate) return;

    const refList: RefType[] = (
      Array.isArray(refs) ? refs : refs ? [refs] : []
    ).filter((ref): ref is RefType => !!ref?.current);

    const handleClickOutside = (event: MouseEvent) => {
      const isOutside = refList.every((ref) => {
        const el = ref.current;
        return el && !el.contains(event.target as Node);
      });

      if (isOutside && callback) {
        callback(event, true);
      }
    };

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (callback) callback(event, true);
    };

    document.addEventListener("mousedown", handleClickOutside);
    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [refs, callback, deactivate]);
};

export default useOutsideAlert;
