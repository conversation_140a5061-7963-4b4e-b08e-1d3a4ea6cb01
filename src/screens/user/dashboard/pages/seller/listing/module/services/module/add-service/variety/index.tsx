import {
  MultiOptionInput,
  MultiOptionInputTargetType,
} from "@/components/elements/form/multi-options";
import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import { InputTargetType } from "@/components/ui/input/types";
import { ChangeEvent, useState } from "react";

const Variety = () => {
  const [values, setValues] = useState<any>({});

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
      | MultiOptionInputTargetType
  ) => {
    try {
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        setValues((prev: any) => {
          let fileList = Array.isArray(prev[e?.target?.name])
            ? Array.from(prev[e?.target?.name])
            : [];
          fileList = fileList.concat(fileArray);
          return { ...prev, [e?.target?.name]: fileList };
        });
      } else if ("option" in e?.target) {
        console.log({ e });
      } else {
        setValues((prev: any) => ({
          ...prev,
          [e?.target?.name]: e?.target?.value,
        }));
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Material</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your service
          </span>
        </div>

        <MultiOptionInput onChange={handleChange} name="material" />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Size</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your service
          </span>
        </div>

        <MultiOptionInput onChange={handleChange} name="size" />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Color Variants</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your service
          </span>
        </div>

        <MultiOptionInput onChange={handleChange} name="color_variants" />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Shape & Design</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your service
          </span>
        </div>

        <MultiOptionInput onChange={handleChange} name="shape_design" />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Packaging Details</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your service
          </span>
        </div>

        <MultiOptionInput onChange={handleChange} name="packaging_details" />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Shelf Life / Expiry date</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your service
          </span>
        </div>

        <MultiOptionInput onChange={handleChange} name="shelf_Life" />
      </div>

      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Service Functionality</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Specify Different variations of your service
          </span>
        </div>

        <MultiOptionInput
          onChange={handleChange}
          name="product_functionality"
        />
      </div>
    </>
  );
};

export default Variety;
