'use client';

import * as React from "react";
import { Input } from "@/components/ui/input/text";
import { ChevronDown } from "lucide-react";
import { Dropzone, DropzoneInputTargetType } from "@/components/ui/input/dropzone";
import { Select } from "@/components/ui/input/select";
import { Button } from "@/components/ui/button";
import CountrySelect from "@/components/ui/input/country-select";
import { Textarea } from "@/components/ui/input/textarea"; // Import your custom Textarea component

const contactReasonOptions = [
  { label: "General Inquiry", value: "General Inquiry" },
  { label: "Product Support", value: "Product Support" },
  { label: "Billing Question", value: "Billing Question" },
  { label: "Partnership Opportunity", value: "Partnership Opportunity" },
  { label: "Other", value: "Other" }
];

const ContactForm = () => {
  const [formData, setFormData] = React.useState({
    fullName: "",
    email: "",
    countryCode: "+91",
    phone: "",
    contactReason: "General Inquiry",
    message: ""
  });
  
  const [files, setFiles] = React.useState<File[]>([]);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  const handleCountryChange = (e: { target: { value: string, name: string } }) => {
    setFormData({
      ...formData,
      countryCode: e.target.value
    });
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement> | DropzoneInputTargetType) => {
    if (e.target.files && e.target.files.length > 0) {
      setFiles(Array.from(e.target.files));
    }
  };
  
  const handleFileDelete = (fileToDelete: File) => {
    setFiles(files.filter(file => file !== fileToDelete));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    console.log("Files:", files);
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-white rounded-lg">
      <h1 className="text-2xl font-bold text-center mb-2">Have a Query? Let Us Know!</h1>
      <p className="text-center text-gray-600 mb-8">{`Fill out the form below, and we'll get back to you promptly.`}</p>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Full Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Full Name*</label>
            <Input
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-md"
              placeholder="Tell Us Who You Are"
            />
          </div>

          {/* Email Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email Address*</label>
            <Input
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-md"
              placeholder="Your Contact Email So We Can Reach You."
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2  gap-6 mb-6">
          {/* Phone with Country Selector - Styled to look like one box */}
          <div className="flex flex-col ">
            <label className="text-gray-700 mb-1 text-sm font-medium ">
               Phone 
            </label>
            <Input
              type="text"
              name="phone"
              onChange={handleChange}
              // value={values?.phone}
              placeholder="Enter Your Phone Number"
              className="p-2 border rounded-md"
              countrySelector={{
                defaultValue: "+91",
                name: "countryCode",
                onChange: handleChange,
                // value: values?.countryCode,
              }}
            />
          </div>

          {/* Reason for Contact */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Reason for Contact</label>
            <Select
              name="contactReason"
              value={formData.contactReason}
              onChange={handleChange}
              options={contactReasonOptions}
              className="w-full"
            />
          </div>
        </div>

        {/* Message - Using the custom Textarea component */}
        <div className="mb-6">
          <Textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            placeholder="Let Us Know How We Can Help."
            rows={4}
            label="Message"
            wrapperClassName="border border-gray-300 rounded-md"
            showCharCount={true}
            maxLength={500}
          />
        </div>

        {/* File Upload using the provided Dropzone */}
        <div className="mb-6">
          <Dropzone
            name="files"
            onChange={handleFileChange}
            value={files}
            preview={true}
            onFileDelete={handleFileDelete}
            formats="JPEG, PNG, PDF"
            containerClassName="bg-white"
            contentClassName="flex items-center justify-center min-h-32"
          >
            {/* Using the default DropPlaceholder from your component */}
          </Dropzone>
        </div>

        {/* reCAPTCHA */}
        <div className="mb-6 flex justify-center">
          <div className="border border-gray-300 rounded p-4 w-full md:w-80">
            <div className="flex items-center">
              <input type="checkbox" className="mr-2" />
              <span className="text-sm">{`I'm not a robot`}</span>
              <div className="ml-auto">
                <img src="/api/placeholder/40/40" alt="reCAPTCHA logo" />
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="mt-6">
          <Button 
            type="submit" 
            variant="main-revert"
            className="w-full bg-red-500 hover:bg-red-600 text-white py-3 rounded-md"
          >
            Send Inquiry
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ContactForm;