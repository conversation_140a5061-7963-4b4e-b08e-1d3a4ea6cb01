"use client";

import ListCard, { ListCardHeader } from "@/components/elements/card/list-card";
import ListPagination from "@/components/elements/list/pagination";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { getPaginationOptions, getPaginationSummary } from "@/lib/utils/data";
import { portfolioList } from "@/screens/user/dashboard/pages/seller/listing/module/portfolio/@redux/selectors";
import {
  deletePortfolio,
  fetchPortfolioList,
} from "@/screens/user/dashboard/pages/seller/listing/module/portfolio/@redux/thunk";
import { RootDispatch } from "@/redux/store";
import { debounce } from "lodash";
import { AlertCircle, Plus, Search } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";

const Portfolio = () => {
  const router = useRouter();
  const dispatch = useDispatch<RootDispatch>();
  const { data: currentProjects, pagination, loading } = useSelector(
    portfolioList
  );
  const [search, setSearch] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<string>("");

  useEffect(() => {
    dispatch(fetchPortfolioList());
  }, [dispatch]);

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (searchTerm?: string, page?: string) =>
          dispatch(fetchPortfolioList({ searchTerm, page })),
        500
      ),
    [dispatch]
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e?.target?.value);
    debouncedSearch(e?.target?.value);
  };

  const handlePagination = (page: string) => {
    setCurrentPage(page);
    debouncedSearch(search, page);
  };

  const handleEdit = (portfolio_id: string) => {
    router.push(`/dashboard/seller/listing/portfolio/${portfolio_id}`);
  };

  const handleDelete = (portfolio_id: string) => {
    dispatch(deletePortfolio(portfolio_id));
  };

  const isDataThere =
    !loading &&
    currentProjects &&
    pagination?.totalPages &&
    pagination?.totalPages > 1;

  return (
    <main className="w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="flex items-center space-x-4">
          <Button
            onClick={() =>
              router.push("/dashboard/seller/listing/portfolio/add")
            }
            className="rounded-md"
            variant="main-revert"
          >
            Add New Project
          </Button>
          <Button
            onClick={() =>
              router.push("/dashboard/seller/listing/portfolio/add")
            }
            className="rounded-md"
          >
            <Plus className="!w-5 !h-5" />
          </Button>

          {isDataThere && getPaginationSummary(pagination) && (
            <span className="px-3 text-sm text-slate-400 font-semibold">
              {getPaginationSummary(pagination)}
            </span>
          )}
        </div>

        <div className="flex justify-end items-center">
          <Input
            type="text"
            name="portfolio_search"
            onChange={(e) => handleChange(e)}
            value={search}
            placeholder="Search..."
            action={
              <button type="submit">
                <Search className="w-4 h-4" />
              </button>
            }
          />
        </div>
      </div>

      <div className="space-y-4">
        {Array.isArray(currentProjects) && currentProjects?.length > 0 ? (
          <>
            <ListCardHeader
              data={[
                {
                  label: "Name",
                  key: "name",
                  className: "max-w-[80%] text-start",
                },
                {
                  label: "Actions",
                  key: "action",
                  className: "max-w-[20%] text-center",
                },
              ]}
            />
            {currentProjects?.map((project: any, index: number) => {
              return (
                <ListCard
                  key={project?._id + String(index)}
                  title={project?.work_title}
                  category={project?.sector}
                  onEdit={() => handleEdit(project._id)}
                  onDelete={() => handleDelete(project._id)}
                  classNames={{ title: "!max-w-[80%]" }}
                  hideImage
                />
              );
            })}
          </>
        ) : (
          <div className="flex items-center gap-2 text-red-500 p-3.5 shadow-box-sm">
            <AlertCircle /> No Projects Available
          </div>
        )}
      </div>

      {isDataThere && (
        <div className="flex justify-between mt-8 items-center gap-4">
          <ListPagination
            totalPages={pagination?.totalPages}
            currentPage={pagination?.page}
            onPageChange={(page) => handlePagination(String(page))}
          />

          <div className="ml-auto">
            <Select
              name="pages"
              value={currentPage}
              onChange={(e) => handlePagination(e?.target?.value)}
              placeholder="Page"
              options={getPaginationOptions(pagination?.totalPages)}
              className="w-max"
            />
          </div>
        </div>
      )}
    </main>
  );
};

export default Portfolio;
