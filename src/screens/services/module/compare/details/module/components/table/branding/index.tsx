import MAIN_LOGO_ICON from "/assets/branding/icon.svg";
import { Button } from "@/components/ui/button";
import { selectedColumn } from "@/screens/company/compare/@redux/selectors";
import { Actions } from "@/screens/company/compare/@redux/slice";
import classNames from "classnames";
import { X } from "lucide-react";
import { IoIosGlobe } from "react-icons/io";
import { useDispatch, useSelector } from "react-redux";

const CompareBranding = () => {
  const dispatch = useDispatch();
  const current = useSelector(selectedColumn);

  const handleSelect = (index: number) => {
    dispatch(Actions.setSelectedColumn(index));
  };

  return (
    <div className="px-4 md:px-0 flex items-start gap-2 md:gap-0 shadow-box rounded-lg">
      <div className="pl-6 basis-1/4 max-w-[calc(100vw-100px)] hidden lg:block" />
      {Array.from({ length: 4 }).map((_, index) => {
        const isACtive = index === current;
        return (
          <CardRender
            key={index}
            index={index}
            active={isACtive}
            onClick={() => handleSelect(index)}
          />
        );
      })}
    </div>
  );
};

const CardRender = ({ index, active, ...rest }: any) => {
  return (
    <div
      role="button"
      className={classNames(
        "relative flex flex-col gap-4 basis-1/4 max-w-[calc(100vw-100px)] border-stone-200 md:border-l py-5 md:py-7 md:pe-7 md:ps-10 lg:ps-12",
        {
          "shadow-inner shadow-main md:shadow-none": active,
          "border-b-2 border-main md:border-b-0 md:border-inherit": active,
        },
        index > 0 &&
          "after:hidden md:after:block after:content-['VS'] after:absolute after:top-1/2 after:left-0 after:-translate-x-1/2 after:-translate-y-1/2 after:text-font after:text-2xl after:font-light after:border after:border-stone-200 after:bg-white after:rounded-full after:p-2 after:z-10"
      )}
      {...rest}
    >
      <div className="flex flex-col md:flex-row items-center gap-3">
        <div className="min-w-16 max-w-16 md:min-w-24 md:max-w-24 p-1.5 rounded-sm flex-1 border border-stone-300 flex items-center justify-center bg-white">
          <img
            src={MAIN_LOGO_ICON}
            alt="company icon"
            className="w-full h-full object-contain"
          />
        </div>
        {index === 0 && (
          <div className="hidden lg:block bg-[#14AA1F] text-white text-[0.6rem] font-semibold px-2 py-0.5 rounded-sm">
            Comparison Winner
          </div>
        )}
      </div>
      <Button className="max-w-max border-b md:border rounded-none md:rounded-full border-main bg-white text-main md:text-inherit !px-0 md:!px-4 !pb-0.5 md:!pb-2 mx-auto md:mx-0 text-xs md:text-sm">
        Get Quote
      </Button>

      <button className="text-start text-brown text-sm font-semibold md:underline hover:drop-shadow">
        <span className="md:hidden flex items-center justify-center border border-stone-300 rounded-full max-w-max p-0.5 mx-auto">
          <IoIosGlobe size={24} />
        </span>
        <span className="hidden md:block">View Website</span>
      </button>

      <button className="hidden md:flex items-center justify-center absolute top-2 right-2">
        <X />
      </button>
    </div>
  );
};

export default CompareBranding;
