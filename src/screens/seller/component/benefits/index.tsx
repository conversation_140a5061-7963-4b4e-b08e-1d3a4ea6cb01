"use client";

import React from "react";
import Benefit from "./benefitcomponent";
import FAQ from "../../../products/details/components/faq";
import { useRouter } from "next/navigation";

const Index = () => {
const router = useRouter();
  return (
    <div className="bg-[#FFFBFA] max-w-full w-full mx-auto xl:mt-12 py-4">
      <Benefit />

      {/* Centered Custom Black Button */}
      <div className="flex justify-center my-6">
        <button
          onClick={() => router.push("/free-listing")}
          className="w-40 md:w-52 bg-[#29100C] hover:bg-gray-800 text-white font-medium py-2 md:py-3 rounded-3xl transition-colors duration-200"
        >
          Join Now For Free
        </button>
      </div>

      <FAQ />
    </div>
  );
};

export default Index;
