const AboutData = {
  bullets: [
    {
      title: "Business Type",
      value: "Manufacturer",
    },
    {
      title: "Year Established",
      value: "1991",
    },
    {
      title: "Number Of Employees",
      value: "800-100 Employees",
    },
    {
      title: "Certifications",
      value: "ISO 14001, ISO 45001",
    },
    {
      title: "Languages",
      value: "International & Local",
    },
    {
      title: "Operating Hours",
      value: "Mon-Fri: 9 AM-5 AM",
    },
    {
      title: "Languages",
      value: "English English English English",
    },
    {
      title: "Regions Served",
      value: "India, UAE, India, UAE",
    },
    {
      title: "Certifications",
      value: "ISO 14001, ISO 14001",
    },
  ],
};

const AboutCompany = () => {
  return (
    <div className="flex flex-col items-start gap-5 [&>*]:w-full">
      <h3 className="font-bold">About Company</h3>
      {Array?.isArray(AboutData?.bullets) && (
        <div className="table sm:grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5">
          {AboutData?.bullets?.map((bullet, index) => (
            <div
              key={index}
              className="relative group flex-1 table-row sm:flex flex-row sm:flex-col flex-start gap-1.5"
            >
              <span className="table-cell p-2 sm:p-0 sm:block text-xs font-normal">
                {bullet?.title}
              </span>
              <span className="table-cell p-2 sm:p-0 sm:block text-sm font-semibold whitespace-nowrap overflow-hidden text-ellipsis">
                {bullet?.value}
              </span>

              <div className="absolute left-0 bottom-full p-3 bg-brown text-white hidden group-hover:flex flex-col flex-start gap-1.5 rounded-md shadow">
                <span className="table-cell p-2 sm:p-0 sm:block text-xs font-normal">
                  {bullet?.title}
                </span>
                <span className="table-cell p-2 sm:p-0 sm:block text-sm font-semibold">
                  {bullet?.value}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AboutCompany;
