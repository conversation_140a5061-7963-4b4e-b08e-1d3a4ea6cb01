import { apiRoutes } from "@/services/api/routes";
import apiClient from "@/services/axios/auth-client";
import {
  createPasswordType,
  LoginType,
  RegisterType,
  ResetPasswordType,
} from "../@context/types";

export const userAuth = async (token: string | null) => {
  try {
    return await apiClient.get(apiRoutes().auth.userAuth, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  } catch (exception) {
    throw exception;
  }
};

export const register = async (payload: RegisterType) => {
  try {
    return await apiClient.post(apiRoutes().auth.register, payload);
  } catch (exception) {
    throw exception;
  }
};

export const login = async (payload: LoginType) => {
  try {
    return await apiClient.post(apiRoutes().auth.login, payload);
  } catch (exception) {
    throw exception;
  }
};

export const resetPassword = async (payload: ResetPasswordType) => {
  try {
    return await apiClient.post(apiRoutes().auth.resetPassword, payload);
  } catch (exception) {
    throw exception;
  }
};

export const createPassword = async (payload: createPasswordType) => {
  try {
    return await apiClient.post(apiRoutes().auth.createPassword, payload);
  } catch (exception) {
    throw exception;
  }
};
