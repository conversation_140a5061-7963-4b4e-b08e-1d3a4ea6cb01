"use client";

import useHashLink from "@/lib/hooks/useHashLink";
import classNames from "classnames";
import HeaderActions from "./actions";

const menuData = [
  {
    title: "Overview",
    path: "overview",
  },
  {
    title: "Rating",
    path: "rating",
  },
  {
    title: "Products",
    path: "products",
  },
  {
    title: "Services",
    path: "services",
  },
];

const CompareDetailsHeader = () => {
  const [active] = useHashLink({
    initialState: menuData?.[0]?.path,
  });

  return (
    <div className="hidden md:block bg-white shadow-md sticky top-header-sm z-20 sm:top-header-md md:top-header">
      <div className="container flex items-center justify-between py-2">
        <div className="flex items-center justify-start gap-4">
          {menuData?.map((menu, index) => {
            const isActive = active
              ? active === menu?.path
              : menuData?.[0]?.path === menu?.path;

            return (
              <a
                key={index}
                href={`#${menu?.path}`}
                className={classNames("transition-all text-sm", {
                  "font-bold px-0.5 text-font": isActive,
                  "px-1 text-stone-600": !isActive,
                })}
              >
                {menu?.title}
              </a>
            );
          })}
        </div>

        <HeaderActions />
      </div>
    </div>
  );
};

export default CompareDetailsHeader;
