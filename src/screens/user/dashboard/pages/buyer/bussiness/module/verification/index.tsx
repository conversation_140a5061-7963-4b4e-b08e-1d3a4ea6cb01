'use client';

import { Badge } from "@/components/ui/badge";
import InputLabel from "@/components/ui/input/components/label";
import { ChangeEvent, useEffect, useState } from "react";
import { Dropzone, DropzoneInputTargetType, InlinePlaceholder, isDropzoneInputTargetType } from "@/components/ui/input/dropzone";
import { InputTargetType } from "@/components/ui/input/types";
import { MultiSelectTarget } from "@/components/ui/input/multi-select";
import OTPBox from "@/components/elements/form/otp";
import { X, Image } from "lucide-react";
import { businessDetails } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/slice";
import { useDispatch, useSelector } from "react-redux";
import { getBusinessDetails } from "../../@redux/thunk";
import { RootDispatch } from "@/redux/store";

interface VerificationProps {
  onValidationChange?: (isValid: boolean) => void;
  isSubmitted?: boolean;
}

interface RegistrationProof {
  url: string;
  file?: File;
  media_id?: string;
}

interface ProofWithUrl {
  url: string;
  media_id?: string;
}

type ProofType = File | ProofWithUrl;

const Verification = ({ onValidationChange, isSubmitted }: VerificationProps) => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values = {} } = useSelector(businessDetails);

  const [registrationProofs, setRegistrationProofs] = useState<RegistrationProof[]>([]);
  const [emailVerified, setEmailVerified] = useState<boolean>(false);
  const [smsVerified, setSmsVerified] = useState<boolean>(false);

  useEffect(() => {
    if (values?.government_registration_proof) {
      const proofs = Array.isArray(values.government_registration_proof)
        ? values.government_registration_proof
        : [values.government_registration_proof];

      const proofUrls = proofs.map((proof: any) => {
        if (proof instanceof File) {
          return { url: URL.createObjectURL(proof), file: proof };
        } else if (proof?.url) {
          return {
            url: proof.url,
            media_id: proof.media_id
          };
        }
        return null;
      }).filter((proof: RegistrationProof | null): proof is RegistrationProof => proof !== null);

      setRegistrationProofs(proofUrls);
    }

    setEmailVerified(Boolean(values?.business_email_verified));
    setSmsVerified(Boolean(values?.phone_number_verified));

    const isValid = Boolean(
      registrationProofs.length > 0 && emailVerified && smsVerified
    );

    onValidationChange?.(isValid);

    return () => {
      registrationProofs.forEach(proof => {
        if (proof.file) {
          URL.revokeObjectURL(proof.url);
        }
      });
    };
  }, [values, onValidationChange, emailVerified, smsVerified]);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        const newProofs = fileArray.map(file => ({
          file,
          url: URL.createObjectURL(file)
        }));

        dispatch(Actions.setFormValues({
          government_registration_proof: [...registrationProofs, ...newProofs]
        }));
      } else {
        dispatch(Actions.setFormValues({
          [e?.target?.name]: e?.target?.value,
        }));
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleRemoveDoc = (index: number, event?: React.MouseEvent) => {
    event?.stopPropagation();

    const proofToRemove = registrationProofs[index];

    if (proofToRemove.file) {
      URL.revokeObjectURL(proofToRemove.url);
    }

    const updatedProofs = registrationProofs.filter((_, i) => i !== index);
    setRegistrationProofs(updatedProofs);

    const updatedDeleteMediaIds =
      proofToRemove.media_id
        ? [...(values.delete_business_registration_certificates || []), proofToRemove.media_id]
        : values.delete_business_registration_certificates || [];

    dispatch(Actions.setFormValues({
      government_registration_proof: updatedProofs,
      ...(proofToRemove.media_id && {
        delete_business_registration_certificates: updatedDeleteMediaIds
      })
    }));
  };





  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Business Registration Certificate */}
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Govt Registration Proof</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload your government registration document
          </span>
        </div>

        <div className="flex-1 w-full">
          <Dropzone
            containerClassName="flex-1"
            onChange={(e) => handleChange(e)}
            name="government_registration_proof"
            value={registrationProofs.filter(proof => proof.file).map(proof => proof.file as File)}
            preview={false}
            onFileDelete={(file) => {
              const index = registrationProofs.findIndex(proof => proof.file === file);
              if (index !== -1) {
                handleRemoveDoc(index);
              }
            }}
            inputProps={{
              multiple: true,
              accept: "image/*,.pdf"
            }}
          >
            {registrationProofs.length === 0 ? (
              <InlinePlaceholder />
            ) : (
              <div className="flex flex-wrap gap-4 mt-2">
                {registrationProofs.map((proof, index) => (
                  <div key={index} className="relative w-32 h-32">
                    <img
                      src={proof.url}
                      alt={`Proof ${index + 1}`}
                      className="w-full h-full object-cover rounded-md"
                    />
                    <button
                      type="button"
                      onClick={(e) => handleRemoveDoc(index, e)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
                    >
                      <X size={16} />
                    </button>

                  </div>
                ))}
              </div>
            )}
          </Dropzone>
        </div>
      </div>

      {/* Business Email Verification */}
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Business Email Verification</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
        </div>
        <div className="flex-1">
          <OTPBox
            input={{
              value: emailVerified ? '1' : '',
              onChange: (value) => {
                console.log('Verification Component - Email OTP updated:', value);
                const isVerified = value === '1';
                setEmailVerified(isVerified);
                dispatch(Actions.setFormValues({ business_email_verified: isVerified }));
              }
            }}
          />
        </div>
      </div>

      {/* Business SMS Verification */}
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Business SMS Verification</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
        </div>
        <div className="flex-1">
          <OTPBox
            input={{
              value: smsVerified ? '1' : '',
              onChange: (value) => {
                console.log('Verification Component - SMS OTP updated:', value);
                const isVerified = value === '1';
                setSmsVerified(isVerified);
                dispatch(Actions.setFormValues({ phone_number_verified: isVerified }));
              }
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default Verification;
