'use client';

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";

const Listing = () => {
   const router = useRouter();

  useEffect(() => {
    router.push("/dashboard/buyer/favourite/companies");
  }, [router]);


  return null;
};

const TabsData = [
  {
    title: "Companies",
    path: "/dashboard/buyer/favourite/companies",
  },
  {
    title: "Products",
    path: "/dashboard/buyer/favourite/products",
  },
  {
    title: "Services",
    path: "/dashboard/buyer/favourite/services",
  }
];

export const ListingTabs = () => {
  const router = useRouter();
  const pathname = usePathname();


  const initialActive = TabsData?.[0]?.path;
  const [active, setActive] = useState<string | undefined>(initialActive);

  useEffect(() => {
    const current = TabsData?.find((item) => pathname?.includes(item?.path));
    setActive(current?.path);
  }, [pathname]);

  return (
    <Tabs
      defaultValue={initialActive}
      value={active}
      className="overflow-auto no-scrollbar py-1.5 border-b border-stone-300 mb-5"
    >
      <TabsList className="flex-nowrap">
        {TabsData?.map((tab, index) => {
          return (
            <TabsTrigger
              key={tab.path}
              value={tab?.path}
              className="flex-1 uppercase before:data-[state=active]:-bottom-2.5"
              onClick={() => router.push(tab?.path)}
            >
              {tab?.title}
            </TabsTrigger>
          );
        })}
      </TabsList>
    </Tabs>
  );
};

export default Listing;
