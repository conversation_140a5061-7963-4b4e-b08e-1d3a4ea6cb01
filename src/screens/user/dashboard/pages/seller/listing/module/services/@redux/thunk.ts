import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import * as API from "../@api/update";
import { Actions } from "./slice";

export const getServices = createAsyncThunk<
  any,
  { searchTerm?: string; page?: string; limit?: string } | undefined,
  { state: RootState }
>("servicesList", async (pagination, { rejectWithValue, dispatch }) => {
  try {
    const response: any = await API.getServices({
      searchTerm: pagination?.searchTerm,
      page: pagination?.page,
      limit: pagination?.limit,
    });

    window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
    dispatch(Actions.setCurrentPage(String(response?.meta?.pagination?.page)));

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const getService = createAsyncThunk<any, string, { state: RootState }>(
  "serviceDetails/getService",
  async (service_id, { rejectWithValue }) => {
    try {
      const response = await API.getService(service_id);
      window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const updateServiceDetails = createAsyncThunk<
  any,
  { service_id?: string; data: FormData },
  { state: RootState }
>(
  "serviceDetails/updateServiceDetails",
  async ({ service_id, data }, { rejectWithValue, dispatch }) => {
    try {
      const response: any = await API.updateServiceDetails(data, service_id);
      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const deleteService = createAsyncThunk<
  any,
  string,
  { state: RootState }
>("deleteService", async (service_id, { rejectWithValue, dispatch }) => {
  try {
    const response: any = await API.deleteService(service_id);
    toast.success(response?.meta?.message as string, {
      classNames: {
        toast: "!bg-green-500 !text-white",
      },
    });
    dispatch(getServices());
    return response;
  } catch (error: any) {
    toast.error(error?.meta?.message as string, {
      classNames: {
        toast: "!bg-red-500 !text-white",
      },
    });

    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const thunks = {
  getService,
  getServices,
  updateServiceDetails,
};
