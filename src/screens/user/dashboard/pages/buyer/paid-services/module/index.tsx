"use client";

import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/input/switch";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const Listing = () => {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (pathname === "/dashboard/buyer/paid-services") {
      router.push("/dashboard/buyer/paid-services/membership");
    }
  }, [router, pathname]);

  return <ListingTabs />;
};

const TabsData = [
  {
    title: "Membership",
    path: "/dashboard/buyer/paid-services/membership",
  },
  {
    title: "Advertising Plan",
    path: "/dashboard/buyer/paid-services/advertising",
  },
  {
    title: "Invoice",
    path: "/dashboard/buyer/paid-services/leads",
  },
];

export const ListingTabs = () => {
  const router = useRouter();
  const pathname = usePathname();

  const initialActive = TabsData?.[0]?.path;
  const [active, setActive] = useState<string | undefined>(initialActive);

  useEffect(() => {
    const current = TabsData?.find((item) => pathname?.includes(item?.path));
    setActive(current?.path);
  }, [pathname]);

  return (
    <div className="relative w-full h-full grid grid-cols-1">
      <PriceModalSwitch />
      <Tabs
        defaultValue={initialActive}
        value={active}
        className="overflow-auto no-scrollbar py-1.5 border-b border-stone-300 mb-5"
      >
        <TabsList className="flex-nowrap">
          {TabsData?.map((tab, index) => {
            return (
              <TabsTrigger
                key={index}
                value={tab?.path}
                className="flex-1 uppercase before:data-[state=active]:-bottom-2.5"
                onClick={() => router.push(tab?.path)}
              >
                {tab?.title}
              </TabsTrigger>
            );
          })}
        </TabsList>
      </Tabs>
    </div>
  );
};

export default Listing;
export const PriceModalSwitch = () => {
  return (
    <div className="static md:absolute right-0 -top-9 order-2 md:order-1 mb-6 md:mb-0 ms-3 md:ms-0">
      <div className="flex items-center gap-2">
        <span className="font-semibold text-base">Monthly</span>
        <Switch />
        <span className="font-semibold text-base">Yearly</span>
        <Badge>15%</Badge>
      </div>
    </div>
  );
};
