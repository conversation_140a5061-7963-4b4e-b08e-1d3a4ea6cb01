import Suggestions from "@/components/elements/form/suggestions";
import { Badge } from "@/components/ui/badge";
import { InputLabel } from "@/components/ui/input/components/label";
import { Input } from "@/components/ui/input/text";
import { Loader } from "@/components/ui/loader";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import useOutsideAlert from "@/lib/hooks/useOutsideAlert";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import classNames from "classnames";
import { debounce } from "lodash";
import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getSubSubCategories } from "../../../../../company/@redux/thunk";
import { serviceSuggestions } from "../../../../@redux/selectors";
import {
  getServicePreValues,
  getServiceSuggestions,
} from "../../../../@redux/thunk";

interface ServiceNameType {
  handleChange: (e: any) => void;
  values: any;
  modalPopover?: boolean;
}

const ServiceName: FC<ServiceNameType> = ({
  handleChange,
  values,
  modalPopover,
}) => {
  const dispatch = useDispatch<RootDispatch>();
  const contentRef = useRef(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const { data: suggestions, loading } = useSelector(serviceSuggestions);

  const optionData = useMemo(() => {
    if (Array.isArray(suggestions) && suggestions.length > 0) {
      return suggestions.map((item: any) => ({
        ...item,
        label: item?.service_name,
      }));
    }

    return [];
  }, [suggestions]);

  const value = values?.service_name ?? "";
  const [isPopoverOpen, setIsPopoverOpen] = useState<boolean>(false);
  const [inputFocused, setInputFocused] = useState<boolean>(false);

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (value: string) =>
          dispatch(getServiceSuggestions({ searchTerm: value })),
        500
      ),
    [dispatch]
  );

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const shouldOpen = useMemo(
    () => value?.trim() && Array.isArray(optionData) && optionData?.length > 0,
    [value, optionData]
  );

  useEffect(() => {
    if (inputFocused && shouldOpen && !isPopoverOpen) {
      setIsPopoverOpen(true);
    }
  }, [inputFocused, shouldOpen, isPopoverOpen]);

  const handleSearch = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value;
      handleChange(e);
      inputRef.current?.focus();

      if (val?.trim()?.length > 0) {
        debouncedSearch(val);
      }

      if (val?.trim() && Array.isArray(optionData) && optionData?.length > 0) {
        setIsPopoverOpen(true);
      }
    },
    [handleChange, debouncedSearch, optionData]
  );

  const handleFocus = useCallback(() => {
    setInputFocused(true);
    if (shouldOpen) {
      setIsPopoverOpen(true);
    }
  }, [shouldOpen]);

  const handleBlur = useCallback(() => {
    setInputFocused(false);
  }, []);

  const handleSelect = useCallback(
    (suggestion: Record<string, any>) => {
      dispatch(getServicePreValues({ suggestionId: suggestion?._id }));
      dispatch(Actions.setSuggestedService(suggestion));
      const payload: any = {};
      payload.sub_sub_category = suggestion?.sub_sub_category;
      dispatch(Actions.setServiceDetails(payload));
      dispatch(
        getSubSubCategories({ search: suggestion?.sub_sub_category_name })
      );
      setIsPopoverOpen(false);
      setInputFocused(false);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    },
    [dispatch]
  );

  useOutsideAlert(contentRef, () => {
    setIsPopoverOpen(false);
  });

  return (
    <div className="flex flex-col md:flex-row items-start gap-6 [&>*]:w-full md:[&>*]:w-auto">
      <div className="min-w-min flex flex-col text-start gap-2 md:w-[25%]">
        <div className="flex items-center justify-start gap-1.5">
          <InputLabel>Service Name</InputLabel>
          <Badge
            className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
            variant="destructive"
          >
            Required
          </Badge>
        </div>
        <span className="text-stone-500 text-[0.7rem]">
          Required, Text (Mx 100 chars)
        </span>
      </div>
      <Popover
        open={isPopoverOpen}
        onOpenChange={(open) => {
          setIsPopoverOpen(open);
          if (!open) {
            setInputFocused(false);
          }
        }}
        modal={modalPopover}
      >
        <PopoverTrigger
          className="flex-1 items-start w-full text-start"
          onClick={(e) => e.preventDefault()}
        >
          <Input
            ref={inputRef}
            name="service_name"
            type="text"
            value={value}
            onChange={handleSearch}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder="Service Name"
            containerClassName="flex-1"
            labelClassName="hidden md:block"
            maxLength={70}
            showCharCount
            autoComplete="off"
            action={loading && <Loader small className="!w-max" />}
            required
          />
        </PopoverTrigger>

        {optionData.length > 0 && (
          <PopoverContent
            align="start"
            onEscapeKeyDown={() => setIsPopoverOpen(false)}
            ref={contentRef}
            className={classNames("!p-1", value && "-translate-y-6")}
            onOpenAutoFocus={(e) => e.preventDefault()}
          >
            <Suggestions
              data={optionData}
              value={value}
              params={{
                subText: [
                  "category_name",
                  "sub_category_name",
                  "sub_sub_category_name",
                ],
                description: ["brand_name"],
              }}
              onSelect={handleSelect}
            />
          </PopoverContent>
        )}
      </Popover>
    </div>
  );
};

export default ServiceName;
