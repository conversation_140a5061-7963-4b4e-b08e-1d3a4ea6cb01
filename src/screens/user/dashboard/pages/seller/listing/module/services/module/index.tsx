"use client";

import ListPagination from "@/components/elements/list/pagination";
import { But<PERSON> } from "@/components/ui/button";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import {
  getFilteredData,
  getPaginationOptions,
  getPaginationSummary,
} from "@/lib/utils/data";
import {
  currentPage,
  searchValue,
  servicesList,
} from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import { getServices } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/thunk";
import { RootDispatch } from "@/redux/store";
import { debounce } from "lodash";
import { Plus, Search } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import ServiceList from "./list";

const Services = () => {
  const router = useRouter();
  const dispatch = useDispatch<RootDispatch>();
  const { data: serviceData, pagination, loading } = useSelector(servicesList);
  const search = useSelector(searchValue);
  const pageNow = useSelector(currentPage);

  useEffect(() => {
    dispatch(getServices());
  }, [dispatch]);

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (searchTerm?: string, page?: string) =>
          dispatch(getServices({ searchTerm, page })),
        500
      ),
    [dispatch]
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(Actions.setSearchValue(e?.target?.value));
    debouncedSearch(e?.target?.value);
  };

  const handlePagination = (page: string) => {
    dispatch(Actions.setCurrentPage(page));
    debouncedSearch(search, page);
  };

  const filteredData = getFilteredData({
    data: serviceData,
    compareValue: search,
    compareParams: ["service_name"],
  });

  const isDataThere =
    !loading && serviceData && pagination && (pagination?.totalPages || 0) > 1;

  return (
    <main className="w-full h-full">
      {/* <h2 className="text-base font-semibold mb-6">Service List</h2> */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="flex items-center space-x-4">
          <Button
            onClick={() =>
              router.push("/dashboard/seller/listing/services/add")
            }
            className="rounded-md"
            variant="main-revert"
          >
            Add New Service
          </Button>
          <Button
            onClick={() =>
              router.push("/dashboard/seller/listing/services/add")
            }
            className="rounded-md"
          >
            <Plus className="!w-5 !h-5" />
          </Button>

          {isDataThere && pagination && pagination.limit && pagination.page && pagination.totalData && (
            <span className="px-3 text-sm text-slate-400 font-semibold">
              {getPaginationSummary({
                limit: pagination.limit,
                page: pagination.page,
                totalData: pagination.totalData
              })}
            </span>
          )}
        </div>

        <div className="flex justify-end items-center">
          <Input
            type="text"
            name="service_search"
            onChange={(e) => handleChange(e)}
            value={search}
            placeholder="Search..."
            action={
              <button type="submit">
                <Search className="w-4 h-4" />
              </button>
            }
          />
        </div>
      </div>

      <ServiceList services={filteredData} />

      {isDataThere && (
        <div className="flex justify-between mt-8 items-center gap-4">
          <ListPagination
            totalPages={pagination?.totalPages || 1}
            currentPage={pagination?.page || 1}
            onPageChange={(page) => handlePagination(String(page))}
          />

          <div className="ml-auto">
            <Select
              name="pages"
              value={pageNow}
              onChange={(e) => handlePagination(e?.target?.value)}
              placeholder="Page"
              options={getPaginationOptions(pagination?.totalPages || 1)}
              className="w-max"
            />
          </div>
        </div>
      )}
    </main>
  );
};

export default Services;
