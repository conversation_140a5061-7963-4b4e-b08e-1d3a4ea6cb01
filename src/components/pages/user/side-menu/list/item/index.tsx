import IconRender from "@/components/elements/icon";
import Transition from "@/components/ui/transition";
import useIsActive from "@/lib/hooks/useIsActivePath";
import classNames from "classnames";
import { ChevronDown } from "lucide-react";
import SideMenuList from "..";
import { MenuType } from "../../types";

const MenuListItem = ({
  menu,
  onClick,
  isSubMenu,
}: {
  menu: MenuType;
  onClick: () => void;
  isSubMenu?: boolean;
}) => {
  const isActive = useIsActive("dashboard", menu?.activePathname ?? "/");

  return (
    <div className="flex flex-col items-start gap-2 [&>*]:w-full transition-all">
      <button
        onClick={() => onClick()}
        className={classNames(
          "flex items-center justify-between gap-2 px-4 rounded-md transition-all",
          {
            "py-2 hover:bg-white": !isSubMenu,
            "py-1.5 hover:text-main": isSubMenu,
            "bg-white hover:bg-white text-font": isActive && !isSubMenu,
            "text-main": isActive && isSubMenu,
            "bg-white hover:bg-white": (menu?.active || isActive) && !isSubMenu,
          }
        )}
      >
        <div title={menu?.label} className="flex items-center gap-2.5 w-full">
          {menu?.icon && (
            <div
              className={classNames(
                "flex items-center justify-center rounded-full p-2",
                {
                  "text-stone-400": !(isActive || menu?.active),
                  "text-font": isActive || menu?.active,
                }
              )}
            >
              <IconRender
                icon={menu?.icon}
                className={classNames({
                  "fill-stone-400 stroke-stone-400 stroke-1": !(
                    isActive || menu?.active
                  ),
                  "fill-font stroke-font stroke-1": isActive || menu?.active,
                })}
                parentColor
              />
            </div>
          )}
          <h4 className="text-start w-full text-sm font-semibold text-ellipsis whitespace-nowrap overflow-hidden ">
            {menu?.label}
          </h4>
        </div>
        {menu?.subMenu && (
          <ChevronDown
            className={classNames(
              "transition-all w-4 h-4",
              menu?.active && "rotate-180"
            )}
          />
        )}
      </button>
      {menu?.subMenu && (
        <Transition start={Boolean(menu?.active) || isActive}>
          <SideMenuList
            data={menu?.subMenu}
            className={classNames(
              // "bg-main/10 max-w-[92%] ms-auto rounded-md",
              menu?.active || isActive ? "block" : "hidden"
            )}
            isSubMenu
          />
        </Transition>
      )}
    </div>
  );
};

export default MenuListItem;
