"use client";

import { selectedColumn } from "@/screens/company/compare/@redux/selectors";
import classNames from "classnames";
import { Star } from "lucide-react";
import { useSelector } from "react-redux";

const CompareRating = () => {
  const current = useSelector(selectedColumn);

  return (
    <section id="rating" className="md:shadow-box rounded-lg">
      <div className="px-7 py-3 text-lg font-bold md:text-base border-b-none md:border-b border-stone-200">
        Ratings
      </div>

      <div className="px-4 md:px-0 flex flex-col md:flex-row items-start gap-4 md:gap-0 rounded-lg overflow-auto">
        <div className="flex-1 hidden lg:block w-full" />
        {Array.from({ length: 4 }).map((_, index) => {
          if (window?.innerWidth < 768 && current !== index) return null;
          return <RatingCard key={index} />;
        })}
      </div>
    </section>
  );
};

const RatingCard = () => {
  return (
    <div className="relative flex flex-col gap-5 items-start [&>*]:w-full w-full flex-1 md:border-l border-stone-200 p-6 bg-white shadow-box md:shadow-none rounded-lg">
      <div className="bg-[#E6E6E6] px-4 py-1 -translate-x-6 md:hidden block">
        <span className="text-sm font-bold">
          Accenture Solutions Private Limited.
        </span>
      </div>
      <div className="flex items-center gap-2">
        <span className="font-bold text-xl">4.5/5</span>
        <div className="flex items-center gap-1">
          {[...Array(+5).keys()]?.map((_, index) => {
            return (
              <Star
                key={index}
                className={classNames(
                  index < 4 && "text-[#347E29] fill-[#347E29] "
                )}
              />
            );
          })}
        </div>
      </div>
      <span className="font-light text-base hidden md:block">All Ratings</span>
      <div className="flex-[2] flex flex-col items-start gap-2 [&>*]:w-full">
        {Array.from({ length: 6 })
          .map((_, index) => {
            return (
              <div key={index} className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-base font-semibold min-w-2.5">
                    {index + 1}
                  </span>
                  <div className="bg-[#347E29] rounded-full p-1">
                    <Star
                      key={index}
                      size={12}
                      className={classNames("text-white fill-white")}
                    />
                  </div>
                </div>
                <span className="text-sm">Work-life balance</span>
              </div>
            );
          })
          ?.reverse()}
      </div>
    </div>
  );
};

export default CompareRating;
