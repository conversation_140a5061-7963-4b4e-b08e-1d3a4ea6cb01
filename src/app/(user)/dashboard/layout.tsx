import SideMenu from "@/components/pages/user/side-menu";
import BreadcrumbRender from "@/components/ui/breadcrumb/render";

export default async function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <main className="py-12 bg-white md:bg-light/20">
        <div className="container">
          <div className="flex items-start gap-6">
            <SideMenu />
            <div className="flex-1 w-full h-full">
              <BreadcrumbRender className="mb-4 font-medium" />
              {children}
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
