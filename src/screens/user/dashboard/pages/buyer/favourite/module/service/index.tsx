'use client';

import React, { useEffect } from "react";
import ListCard from "./service-card";
import { getFavourites, deleteFavourite } from "../../@redux/thunk"; // ✅ Import deleteFavourite
import { getFavouritesSelector } from "../../@redux/selector";
import { useDispatch, useSelector } from "react-redux";
import { RootDispatch } from "@/redux/store";

const ServiceListings = () => {
  const dispatch = useDispatch<RootDispatch>();

  // Get favourites from Redux store
  const { data: favouriteServices, loading } = useSelector(getFavouritesSelector);

  // Dispatch API call when component mounts
  useEffect(() => {
    dispatch(
      getFavourites({
        favoriteType: "service",
        priority: 1,
      })
    );
  }, [dispatch]);

  // Transform API data to match ListCard expected format with static fallbacks
  const transformServiceData = (apiData: any) => {
    // Static fallback data for missing fields
    const staticFallback = {
      name: "Premium Service Solutions",
      images: [
        "/assets/pages/buyer/building.jpg",
        "/assets/pages/buyer/building_1.jpg"
      ],
      location: {
        flag: "https://flagcdn.com/w40/in.png", // India flag as default
        address: "Service District, Downtown",
        label: "Commercial Area"
      },
      verified: true,
      offerPrice: 499,
      actualPrice: 699,
      rating: {
        stars: 4.3,
        reviewsCount: 89
      },
      tags: [
        { title: "Professional Services" },
        { title: "Quality Assured" },
        { title: "24/7 Support" },
        { title: "Licensed" },
        { title: "Insured" }
      ],
      description: "Professional service provider with quality assurance and customer satisfaction.",
      shortIntro: "Your trusted partner for professional services and exceptional support.",
      strengthRange: "Flexible",
      businessAge: "2+ years",
      minOrderQuantity: 1,
    };

    return {
      id: apiData._id || Math.random().toString(),
      name: apiData.service_name || apiData.name || staticFallback.name,
      shortIntro: apiData.shortIntro || staticFallback.shortIntro,
      images: apiData.main_service_images?.length > 0
        ? apiData.main_service_images
        : staticFallback.images,
      location: {
        flag: staticFallback.location.flag,
        address: apiData.location?.[0] || apiData.service_address || staticFallback.location.address,
        label: apiData.location?.[0] || `${apiData.city || "Unknown City"}, ${apiData.state || "Unknown State"}, ${apiData.country || "Unknown Country"}`
      },
      verified: staticFallback.verified,
      offerPrice: apiData.min_price || staticFallback.offerPrice,
      actualPrice: apiData.max_price || staticFallback.actualPrice,
      rating: {
        stars: apiData.average_rating || staticFallback.rating.stars,
        reviewsCount: apiData.reviews_count || staticFallback.rating.reviewsCount
      },
      tags: apiData.tags?.length > 0 ? apiData.tags : staticFallback.tags,
      description: apiData.description || staticFallback.description,

      strengthRange: apiData.strengthRange || staticFallback.strengthRange,
      businessAge: apiData.businessAge || staticFallback.businessAge,
      minOrderQuantity: apiData.minOrderQuantity || staticFallback.minOrderQuantity,
    };
  };


  const handleUnfavourite = (serviceId: string) => {
    dispatch(
      deleteFavourite({
        favouriteId: serviceId,
        filters: {
          favoriteType: "service",
          priority: 1,
        },
      })
    );
  };

  return (
    <div className="flex flex-col bg-white p-3 sm:p-6 gap-4">
      <div className="flex justify-between items-center mb-2 sm:mb-4">
        <h2 className="text-base sm:text-lg font-medium text-gray-800">
          {favouriteServices?.length || 0} results found
        </h2>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <p className="text-gray-500">Loading services...</p>
        </div>
      ) : favouriteServices?.length > 0 ? (
        favouriteServices.map((item: any, index: number) => (
          <ListCard
            key={item.id || index}
            company={transformServiceData(item.favoriteDetails || item)}
            onUnfavourite={() => handleUnfavourite(item._id)} // ✅ Pass unfavorite handler
          />
        ))
      ) : (
        <div className="flex items-center justify-center py-8">
          <p className="text-gray-500">No favorite services found</p>
        </div>
      )}
    </div>
  );
};

export default ServiceListings;