'use client';

import { useState, ChangeEvent, useEffect } from "react";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import { useDispatch, useSelector } from "react-redux";
import { RootDispatch } from "@/redux/store";
import { getProfileDetailsSelector } from "../../../@redux/selector";
import { getProfileDetails, updateProfileDetails } from "../../../@redux/thunk";
import { Button } from "@/components/ui/button";

const PersonalInformation = () => {

  const dispatch = useDispatch<RootDispatch>();
  const profile = useSelector(getProfileDetailsSelector);

  const [values, setValues] = useState({
    gender: "",
    phoneNumber: "",
    zipCode: "",
    emailAddress: "",
    countryCode: ""
  });

useEffect(() => {
  if (profile.data?.personalInformation) {
    setValues({
      gender: profile.data.personalInformation.gender || "",
      phoneNumber: profile.data.personalInformation.phone || "",
      zipCode: profile.data.personalInformation.zipcode || "",
      emailAddress: profile.data.personalInformation.email || "",
      countryCode: "+91" // or derive from phone if needed
    });
  }
}, [profile.data]);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | InputTargetType
  ) => {
    try {
      setValues((prev) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  const handleSave = () => {
   dispatch(updateProfileDetails({
  gender: values.gender,
  phone_number: values.phoneNumber,
  zipcode: values.zipCode,
  email: values.emailAddress,
  country_code: values.countryCode,
} as any)).then(() => {
  dispatch(getProfileDetails());
});
  };

  // Gender options
  const genderOptions = [
    { label: "Male", value: "male" },
    { label: "Female", value: "female" },
    { label: "Other", value: "other" }
  ];

  return (
    <div className="bg-white rounded-md shadow-sm p-6 max-w-7xl mx-auto">
      <h2 className="text-lg font-medium mb-6 border-b pb-4">Personal Information</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Gender */}
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Gender
          </label>
          <Select
            name="gender"
            onChange={handleChange}
            value={values.gender}
            placeholder="Select Gender"
            options={genderOptions}
            className="p-2 border rounded-md"
          />
        </div>

        {/* Phone Number */}
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Phone Number
          </label>
          <Input
            type="text"
            name="phoneNumber"
            onChange={handleChange}
            value={values.phoneNumber}
            placeholder="Enter Phone Number"
            className="p-2 border rounded-md"
            countrySelector={{
              defaultValue: "+91",
              name: "countryCode",
              onChange: handleChange,
              value: values.countryCode,
            }}
          />
        </div>

        {/* Zip Code */}
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Zip Code
          </label>
          <Input
            type="text"
            name="zipCode"
            onChange={handleChange}
            value={values.zipCode}
            placeholder="Enter Zip Code"
            className="p-2 border rounded-md"
          />
        </div>

        {/* Email Address */}
        <div className="flex flex-col">
          <label className="text-gray-700 text-sm font-medium mb-2">
            Email Address
          </label>
          <Input
            type="email"
            name="emailAddress"
            onChange={handleChange}
            value={values.emailAddress}
            placeholder="Enter Email Address"
            className="p-2 border rounded-md"
          />
        </div>
      </div>

      
      {/* Save Button */}
      <div className="mt-6 flex justify-end">
        <Button
          variant="main-revert"
          size="default"
          type="button"
          className="w-20 rounded-md"
          onClick={handleSave} // ✅ Hook this in
        >
          Save
        </Button>
        </div>
    </div>
  );
};

export default PersonalInformation;