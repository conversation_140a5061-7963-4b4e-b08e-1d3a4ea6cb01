'use client';

import { Input } from "@/components/ui/input/text";
import React, { useState } from 'react';

interface ProductDetailsProps {
  onProductDetailsChange?: (details: ProductDetails) => void;
  initialDetails?: ProductDetails;
}

interface ProductDetails {
  title: string;
}

const ProductDetailsInput: React.FC<ProductDetailsProps> = ({
  onProductDetailsChange,
  initialDetails = {
    title: ""
  }
}) => {
  const [productDetails, setProductDetails] = useState<ProductDetails>(initialDetails);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const updatedDetails = {
      ...productDetails,
      [name]: value
    };
    setProductDetails(updatedDetails);
    if (onProductDetailsChange) {
      onProductDetailsChange(updatedDetails);
    }
  };
  
  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col items-center mb-16 max-w-md w-full mt-0 sm:mt-0 md:mt-0">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-6 text-center">What product do you need?</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <Input
            name="title"
            placeholder="Product Title"
            value={productDetails.title}
            onChange={handleInputChange}
          />
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsInput;