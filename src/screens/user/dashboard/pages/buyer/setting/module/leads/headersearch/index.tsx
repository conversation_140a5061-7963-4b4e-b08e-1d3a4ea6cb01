'use client';

import { X } from "lucide-react";
import { FC, FormEvent, useCallback, useEffect, useRef, useState } from "react";
import { FiSearch } from "react-icons/fi";
import { IoMicOutline } from "react-icons/io5";
import classNames from "classnames";
import useOutsideAlert from "@/lib/hooks/useOutsideAlert";

interface HeaderSearchProps {
  className?: string;
  placeholder?: string;
  optionsData?: any[];
  onSelectionsChange?: (selections: any[]) => void;
  onSearch?: (searchTerm: string) => void;
}

const HeaderSearch: FC<HeaderSearchProps> = ({
  className,
  placeholder = "Search for products, services, companies",
  optionsData = [],
  onSelectionsChange,
  onSearch,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState<string>("");
  const [focused, setFocused] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  const handleClose = useCallback(() => {
    try {
      setFocused(false);
      setShowDropdown(false);
    } catch (error) {
      console.error();
    }
  }, []);

  const handleSearch = (e: FormEvent<HTMLInputElement>) => {
    try {
      const searchValue = e.currentTarget.value;
      setValue(searchValue);
      setShowDropdown(true);
      if (onSearch) {
        onSearch(searchValue);
      }
    } catch (error) {
      console.error();
    }
  };

  const handleSelect = (item: any) => {
    try {
      const isSelected = selectedItems.some(selected => selected.id === item.id);
      let newItems;
      
      if (isSelected) {
        // Remove item if already selected
        newItems = selectedItems.filter(i => i.id !== item.id);
      } else {
        // Add item if not selected
        newItems = [...selectedItems, item];
      }
      
      setSelectedItems(newItems);
      if (onSelectionsChange) {
        onSelectionsChange(newItems);
      }
    } catch (error) {
      console.error();
    }
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    try {
      e.preventDefault();
      if (onSearch) {
        onSearch(value);
      }
    } catch (error) {
      console.error();
    }
  };

  const handleRemove = (item: any) => {
    try {
      const newItems = selectedItems.filter(i => i.id !== item.id);
      setSelectedItems(newItems);
      if (onSelectionsChange) {
        onSelectionsChange(newItems);
      }
    } catch (error) {
      console.error();
    }
  };

  // Remove the filter from filteredOptions to show all items
  const filteredOptions = optionsData.filter(item => 
    item.title.toLowerCase().includes(value.toLowerCase())
  );

  const handleSelectAll = () => {
    if (selectedItems.length === optionsData.length) {
      setSelectedItems([]);
      if (onSelectionsChange) {
        onSelectionsChange([]);
      }
    } else {
      setSelectedItems([...optionsData]);
      if (onSelectionsChange) {
        onSelectionsChange([...optionsData]);
      }
    }
  };

  return (
    <div
      ref={containerRef}
      className={classNames(
        "md:relative md:w-full md:h-full py-[1.315rem] px-0 transition-all overflow-hidden md:overflow-visible",
        focused &&
          "fixed w-screen h-screen top-0 left-0 bg-white md:bg-inherit transition-all px-6 md:px-0 !flex flex-col items-start [&>*]:w-full ",
        className
      )}
    >
      <div className="flex items-center gap-2">
        <form
          onSubmit={handleSubmit}
          className={classNames(
            "flex-1 min-h-min w-full flex items-center gap-1.5 border bg-white border-slate-400 rounded-full overflow-hidden p-0.5"
          )}
        >
          <button type="button" className="sm:hidden w-8 flex items-center justify-center text-stone-700 hover:text-font hover:drop-shadow-md">
            <FiSearch size={16} />
          </button>
          
          <div className="flex-1 flex items-center gap-2 px-3">
            {selectedItems.map((item) => (
              <span
                key={item.id}
                className="flex items-center gap-1 bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-sm"
              >
                {item.title}
                <button
                  type="button"
                  onClick={() => handleRemove(item)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X size={14} />
                </button>
              </span>
            ))}
            
            <input
              ref={inputRef}
              type="text"
              className="flex-1 bg-transparent !outline-0 focus:!outline-0 focus-within:!outline-0 !outline-transparent !ring-0 !border-0 border-none text-sm"
              placeholder={selectedItems.length === 0 ? placeholder : ""}
              value={value}
              onChange={handleSearch}
              onFocus={() => {
                setFocused(true);
                setShowDropdown(true);
              }}
            />
          </div>

          <button type="button" className="text-stone-700 hover:text-font hover:drop-shadow-md flex items-center justify-center w-6">
            <IoMicOutline size={22} />
          </button>
          
          <button
            type="submit"
            className="hidden bg-red-500 text-white sm:flex items-center justify-center gap-1 h-full px-3.5 py-2 rounded-full outline-0 border-none"
          >
            <FiSearch size={16} />
            <span className="font-semibold text-sm">Search</span>
          </button>
        </form>

        {focused && (
          <button
            className="flex items-center justify-center md:hidden"
            onClick={handleClose}
          >
            <X size={22} />
          </button>
        )}
      </div>

      {showDropdown && (
        <div className="mt-6 md:mt-0 md:w-[280px] flex-1 md:absolute z-[1] top-[100%] left-0 bg-white pb-6 md:pb-0 border border-gray-200 rounded-lg shadow-lg">
          <div className="p-3 border-b border-gray-200">
            <div className="relative mb-3">
              <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                className="w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:border-gray-300"
                placeholder="Search..."
                value={value}
                onChange={handleSearch}
              />
            </div>
            <label className="flex items-center gap-2 cursor-pointer py-1">
              <input
                type="checkbox"
                className="w-4 h-4 rounded border-gray-300 text-red-500 focus:ring-red-500"
                checked={selectedItems.length === optionsData.length}
                onChange={handleSelectAll}
              />
              <span className="text-sm text-gray-600">(Select All)</span>
            </label>
          </div>

          <div className="max-h-[300px] overflow-y-auto py-2">
            {filteredOptions.map((item) => (
              <div
                key={item.id}
                className="w-full px-4 py-2 hover:bg-gray-50"
              >
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    className="w-4 h-4 rounded border-gray-300 text-red-500 focus:ring-red-500"
                    checked={selectedItems.some(selected => selected.id === item.id)}
                    onChange={() => handleSelect(item)}
                  />
                  <span className="text-sm text-gray-700">{item.title}</span>
                </label>
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between p-3 border-t border-gray-200">
            <button
              type="button"
              onClick={() => {
                setSelectedItems([]);
                if (onSelectionsChange) onSelectionsChange([]);
              }}
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Clear
            </button>
            <button
              type="button"
              onClick={handleClose}
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default HeaderSearch;