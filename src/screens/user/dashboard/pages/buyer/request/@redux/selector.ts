import { createSelector } from "reselect";
import { RootState } from "@/redux/root-reducer";

// Safely access user_buyer_rfq slice, providing default structure if undefined
const reducer = (state: RootState) =>
  state.user_buyer_rfq || {
    productDetails: { data: null, loading: false, meta: null, formData: {} },
    serviceDetails: { data: null, loading: false, meta: null, formData: {} },
  };

// Selector to retrieve productDetails from user_buyer_rfq state slice
export const productDetails = createSelector(
  [reducer],
  (state) => state.productDetails
);

// Selector to retrieve serviceDetails from user_buyer_rfq state slice
export const serviceDetails = createSelector(
  [reducer],
  (state) => state.serviceDetails
);
