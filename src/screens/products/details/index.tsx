import { Skeleton } from "@/components/ui/loader/skeleton";
import classNames from "classnames";
import { Fragment, lazy, Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";
import ProductSelections from "./components/selections";

const TabsData = [
  {
    title: "Products gallery mobile",
    path: "gallery-mob",
    component: lazy(() => import("./components/gallery/mobile")),
    hideForDesktop: true,
    ad: true,
  },
  {
    title: "Products profile",
    path: "display",
    component: lazy(() => import("./components/profile")),
  },
  {
    title: "Products navigation",
    path: "navigation",
    component: lazy(() => import("./components/navigation")),
    hideForDesktop: true,
    ad: true,
    sticky: true,
  },
  {
    title: "Products gallery",
    path: "display",
    component: lazy(() => import("./components/gallery")),
    props: {},
    ad: true,
    hideForMobile: true,
  },
  {
    title: "Products selections",
    path: "selections",
    component: lazy(() => import("./components/selections")),
    hideForDesktop: true,
  },
  {
    title: "Products Description",
    path: "description",
    component: lazy(() => import("./components/description")),
  },
  {
    title: "Products Overview",
    path: "overview",
    component: lazy(() => import("./components/overview")),
  },
  {
    title: "Products Inquiry",
    path: "inquiry",
    component: lazy(() => import("./components/inquiry")),
  },
  {
    title: "Products Catalogs",
    component: lazy(() => import("./components/catalogs")),
  },
  {
    title: "projects",
    component: lazy(() => import("./components/projects")),
  },
  {
    title: "supplier",
    component: lazy(() => import("./components/supplier")),
  },
  {
    title: "FAQ",
    component: lazy(() => import("./components/faq")),
  },
  {
    title: "Reviews",
    component: lazy(
      () =>
        import("@/screens/company/details/landing/components/information/reviews")
    ),
  },
  {
    title: "Ad",
    ad: true,
    component: lazy(() => import("@/components/layout/ads/banner")),
  },
  {
    title: "Related Products",
    component: lazy(() => import("./components/related")),
  },
];

const ProductDetails = () => {
  return (
    <main id="product-detail-page" className="p-0 sm:container mb-12 md:mt-12">
      <div className="relative grid grid-cols-1 md:grid-cols-5 gap-6 items-start w-full max-w-full">
        <div
          className={classNames(
            "md:col-span-3 grid grid-cols-1 gap-2.5 md:gap-6"
          )}
        >
          {TabsData?.map((tab, index) => {
            const key = `${tab.path || tab.title || `tab-${index}`}-${index}`;
            return (
              <Fragment key={key}>
                <ErrorBoundary fallback={<div>Failed to load section</div>}>
                  <Suspense fallback={<Skeleton className="w-full h-44" />}>
                    <section
                      className={classNames(
                        "w-full sm:scroll-mt-44 rounded-md",
                        "bg-white",
                        {
                          "block md:hidden": tab?.hideForDesktop,
                          "hidden md:block": tab?.hideForMobile,
                          "sm:shadow-box px-6 md:px-8 py-3 md:py-7": !tab?.ad,
                          "sticky top-header-sm  lg:top-header z-30":
                            tab?.sticky,
                          "bg-gradient-to-l from-[#FFF5F6] via-[#FDFDFD] to-[#FFFAF4]":
                            tab?.path && ["inquiry"].includes(tab?.path),
                        }
                      )}
                    >
                      <tab.component {...(tab?.props ?? {})} />
                    </section>
                  </Suspense>
                </ErrorBoundary>
              </Fragment>
            );
          })}

        </div>
        <ProductSelections className="hidden md:flex md:col-span-2 sticky top-header bg-white sm:shadow-box p-4 rounded-md" />
      </div>
    </main>
  );
};

export default ProductDetails;
