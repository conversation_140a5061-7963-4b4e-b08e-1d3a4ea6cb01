import type {
  DataActionType,
  PaginationType,
} from "@/lib/hooks/useInfiniteScroll";
import { ValueUniqueParam } from "@/types/shared";
import { MutableRefObject, ReactNode } from "react";

export interface DropdownListDataType {
  label: string;
  icon?: string | ReactNode;
  active?: boolean;
  checked?: boolean;
  event?: (menuItem: DropdownListDataType) => void;
  [key: string]: any;
}

export interface DropdownListType {
  parentRef?: MutableRefObject<null>;
  iconClassName?: string;
  closeOnSelect?: boolean;
  checkbox?: boolean;
  search?: boolean;
  loading?: boolean;
  highlightSelected?: boolean;
  searchPlaceholder?: string;
  className?: string;
  menuClassName?: string;
  data: DropdownListDataType[];
  onClose?: (data?: any) => void;
  onSelect?: (data: DropdownListDataType) => void;
  onSearch?: (data: string) => void;
  resetAction?: () => void;
  value?: string | DropdownListDataType | DropdownListDataType[];
  valueUniqueParam?: ValueUniqueParam;
  searchCompareParams?: (string | undefined)[];
  optionKeys?: {
    label?: string;
    value?: string;
    icon?: string;
  };

  /** Mandatory if need pagination */
  dataAction?: DataActionType;

  /** Mandatory if need pagination */
  pagination?: PaginationType;
}

export interface DropdownListSectionType {
  collapsible?: boolean;
  section: DropdownListDataType;
  index: number;
}
