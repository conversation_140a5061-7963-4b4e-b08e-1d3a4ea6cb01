"use client";

import classNames from "classnames";
import { ArrowDown, ArrowUp } from "lucide-react";
import { useState } from "react";

const ProductDescription = () => {
  const [readFull, setReadFull] = useState<boolean>(false);

  const text =
    "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores read more. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores read more..";

  return (
    <div className="relative w-full">
      <h3 className="font-bold mb-2">Product Description</h3>
      <p className={classNames("text-sm", { "line-clamp-5": !readFull })}>
        {text}
      </p>

      <div className="flex items-center justify-start mt-4">
        <button
          className="text-font hover:text-main text-xs hover:underline hover:drop-shadow flex items-center gap-1 capitalize [&>svg]:w-4 [&>svg]:h-4 w-max"
          onClick={() => setReadFull((prev) => !prev)}
        >
          {readFull ? "read less" : "read more"}
          {readFull ? <ArrowUp /> : <ArrowDown />}
        </button>
      </div>
    </div>
  );
};

export default ProductDescription;
