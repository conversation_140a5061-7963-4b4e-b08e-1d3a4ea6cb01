import CompanyCard from "@/components/elements/card/company";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

const RelatedCompanies = () => {
  return (
    <div className="flex flex-col items-start gap-5 [&>*]:w-full">
      <h3 className="font-bold">Related Companies</h3>
      <div className="hidden md:grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {Array.from({ length: 8 })
          .slice(0, 4)
          .map((_, index) => (
            <div key={index} className="sm:basis-1/2 lg:basis-1/3 xl:basis-1/4">
              <CompanyCard />
            </div>
          ))}
      </div>

      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full static block md:hidden"
      >
        <CarouselContent className="-ml-6">
          {Array.from({ length: 5 }).map((_, index) => (
            <CarouselItem
              key={index}
              className="pl-6 sm:basis-1/2 lg:basis-1/3 xl:basis-1/4 my-4 max-w-60"
            >
              <CompanyCard />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="-left-4 shadow-xl bg-white" />
        <CarouselNext className="-right-4 shadow-xl bg-white" />
      </Carousel>
      <div className="flex items-center justify-center">
        <Button className="mt-2" variant="ghost-brown">
          Load more
        </Button>
      </div>
    </div>
  );
};

export default RelatedCompanies;
