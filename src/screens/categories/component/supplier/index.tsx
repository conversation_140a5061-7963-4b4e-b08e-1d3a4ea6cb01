"use client";

import React from 'react';
import ProductCard from './card';
import ProductSlider from './slider';

export default function ProductCardExample() {
  const productsData = [
    {
      name: "Earpiece",
      logoLine1: "Giggling",
      logoLine2: "Earpiece",
      rating: "4.5",
      reviewCount: 150,
      isVerified: true,
      images: [
        { src: "assets/pages/buyer/building.jpg", alt: "Person using earpiece while driving" },
        { src: "assets/pages/buyer/building.jpg", alt: "Person using earpiece at work" },
        { src: "assets/pages/buyer/building.jpg", alt: "Couple using earpiece" },
        { src: "assets/pages/buyer/building.jpg", alt: "Professional with earpiece" }
      ]
    },
    {
      name: "Smart Speaker",
      logoLine1: "Smart",
      logoLine2: "Speaker",
      rating: "4.8",
      reviewCount: 230,
      isVerified: true,
      images: [
        { src: "assets/pages/buyer/building.jpg", alt: "Speaker in living room" },
        { src: "assets/pages/buyer/building.jpg", alt: "Speaker close-up" },
        { src: "assets/pages/buyer/building.jpg", alt: "Speaker in kitchen" },
        { src: "assets/pages/buyer/building.jpg", alt: "Speaker with smartphone" }
      ]
    },
    {
      name: "Headphones",
      logoLine1: "Wireless",
      logoLine2: "Headphones",
      rating: "4.3",
      reviewCount: 189,
      isVerified: true,
      images: [
        { src: "assets/pages/buyer/building.jpg", alt: "Headphones on desk" },
        { src: "assets/pages/buyer/building.jpg", alt: "Person wearing headphones" },
        { src: "assets/pages/buyer/building.jpg", alt: "Headphones case" },
        { src: "assets/pages/buyer/building.jpg", alt: "Headphones charging" }
      ]
    },
    {
      name: "Smart Watch",
      logoLine1: "Smart",
      logoLine2: "Watch",
      rating: "4.6",
      reviewCount: 315,
      isVerified: true,
      images: [
        { src: "assets/pages/buyer/building.jpg", alt: "Watch on wrist" },
        { src: "assets/pages/buyer/building.jpg", alt: "Watch features" },
        { src: "assets/pages/buyer/building.jpg", alt: "Watch charging" },
        { src: "assets/pages/buyer/building.jpg", alt: "Watch app" }
      ]
    },{
      name: "Smart Watch",
      logoLine1: "Smart",
      logoLine2: "Watch",
      rating: "4.6",
      reviewCount: 315,
      isVerified: true,
      images: [
        { src: "assets/pages/buyer/building.jpg", alt: "Watch on wrist" },
        { src: "assets/pages/buyer/building.jpg", alt: "Watch features" },
        { src: "assets/pages/buyer/building.jpg", alt: "Watch charging" },
        { src: "assets/pages/buyer/building.jpg", alt: "Watch app" }
      ]
    }
  ];

  return (
    <div className="flex justify-center mb-6 w-full mt-7 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-screen-2xl 2xl:max-w-[90%]"> {/* Increased width here to max-w-screen-2xl */}
        <div className="mb-4 px-1">
          <h3 className="text-base font-bold text-gray-800">Verified Suppliers</h3>
        </div>
  
        <ProductSlider>
          {productsData.map((product, index) => (
            <ProductCard
              key={index}
              name={product.name}
              logoLine1={product.logoLine1}
              logoLine2={product.logoLine2}
              rating={product.rating}
              reviewCount={product.reviewCount}
              isVerified={product.isVerified}
              images={product.images}
            />
          ))}
        </ProductSlider>
      </div>
    </div>
  );
}
