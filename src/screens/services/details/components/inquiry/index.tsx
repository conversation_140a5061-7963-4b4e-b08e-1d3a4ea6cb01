"use client"; 

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { ChangeEvent, useState } from "react";

const ServiceInquiry = () => {
  const [values, setValues] = useState<any>({});

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    try {
      setValues((prev: any) => ({
        ...prev,
        [e?.target?.name]: e?.target?.value,
      }));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col items-start gap-5">
      <h3 className="font-bold mb-1">Contact & Inquiry</h3>

      <Input
        type="email"
        name="email"
        label="To"
        onChange={handleChange}
        value={values?.email}
        labelClassName="mb-1 ml-5"
        placeholder="Enter email address"
        containerClassName="w-full"
      />

      <Textarea
        name="message"
        label="Inquiry Message"
        onChange={handleChange}
        value={values?.message}
        placeholder="Write here the inquiry Message"
        className="min-h-20"
        labelClassName="mb-1 ml-5"
        containerClassName="w-full"
      />

      <Button
        type="submit"
        variant="ghost-brown"
        className="w-full rounded-full"
      >
        Submit
      </Button>
    </div>
  );
};

export default ServiceInquiry;
