"use client";

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Actions } from '@/screens/user/dashboard/pages/buyer/request/@redux/slice';
import { serviceDetails } from '@/screens/user/dashboard/pages/buyer/request/@redux/selector';
import ServiceDetailsInput from '../pages/servicedetails';
import Location from '../pages/serviceLocation';
import Language from '../pages/language';
import Team from '../pages/team';
import Skills from '../pages/skills';
import Duration from '../pages/duration';
import Requirement from '../pages/scope';
import Budget from '../pages/budget';
import Attachment from '../pages/attachment';
import AdditionalInfo from '../pages/additional';
import OTP from '../pages/otp';

type StepType = 'selection' | 'category' | 'quantity' | 'location' | 'urgency' | 'provider' | 'budget' | 'requirement' | 'attachment' | 'additional' | 'otp' | 'skills' | 'language' | 'team' | 'duration';

interface ServiceProps {
  currentStep: StepType;
  onValidationChange?: (isValid: boolean) => void;
}

const Service: React.FC<ServiceProps> = ({ currentStep, onValidationChange }) => {
  const dispatch = useDispatch();
  const serviceDetailsState = useSelector(serviceDetails);
  const formData = serviceDetailsState?.formData || {};
  const serviceType = formData.type || {};

  const [isCurrentStepValid, setIsCurrentStepValid] = useState(false);

  useEffect(() => {

    if (!serviceType.name || serviceType.name !== "service") {
      dispatch(Actions.updateServiceFormData({
        field: 'type',
        data: {
          name: "service",
          timestamp: new Date().toISOString(),
          isProduct: false
        }
      }));
    }
  }, [currentStep, dispatch, formData, serviceType.name]);

  useEffect(() => {
    validateCurrentStep();
  }, [currentStep, formData]);

  useEffect(() => {
    if (onValidationChange) {
      onValidationChange(isCurrentStepValid);
    }
  }, [isCurrentStepValid, onValidationChange]);

  const validateCurrentStep = () => {
    let isValid = false;

    switch (currentStep) {
      case 'category':
        isValid = !!formData.serviceDetails?.title;
        break;
      case 'location':
        isValid = !!formData.serviceLocation?.countries && formData.serviceLocation.countries.length > 0;
        break;
      case 'language':
        isValid = !!formData.language?.languages && formData.language.languages.length > 0;
        break;
      case 'team':
        isValid = !!formData.team?.teamSize;
        break;
      case 'skills':
        isValid = !!formData.skills?.skills && formData.skills.skills.length > 0;
        break;
      case 'duration':
        isValid = !!formData.duration?.duration;
        break;
      case 'budget':
        isValid = !!formData.serviceBudget?.budgetRange?.min && !!formData.serviceBudget?.budgetRange?.max;
        break;
      case 'requirement':
        isValid = !!formData.serviceRequirement?.scope;
        break;
      case 'additional':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const phoneRegex = /^\d{10}$/;

        isValid = !!formData.serviceAdditional?.contactName &&
          !!formData.serviceAdditional?.email &&
          emailRegex.test(formData.serviceAdditional.email) &&
          !!formData.serviceAdditional?.phoneNumber &&
          phoneRegex.test(formData.serviceAdditional.phoneNumber);
        break;
      default:
        isValid = true;
    }

    setIsCurrentStepValid(isValid);
  };

  const handleServiceDetailsChange = (details: any) => {
    const formattedDetails = {
      ...details,
      titleArray: details.title ? [details.title] : [""]
    };

    dispatch(Actions.updateServiceFormData({ field: 'serviceDetails', data: formattedDetails }));

    dispatch(Actions.updateServiceFormData({
      field: 'title',
      data: details.title || ""
    }));

  };

  const handleLocationChange = (details: any) => {
    dispatch(Actions.updateServiceFormData({ field: 'serviceLocation', data: details }));
  };

  const handleLanguageChange = (details: any) => {
    dispatch(Actions.updateServiceFormData({ field: 'language', data: details }));
  };

  const handleTeamChange = (details: any) => {
    dispatch(Actions.updateServiceFormData({ field: 'team', data: details }));
  };

  const handleSkillsChange = (details: any) => {
    dispatch(Actions.updateServiceFormData({ field: 'skills', data: details }));
  };

  const handleDurationChange = (details: any) => {
    dispatch(Actions.updateServiceFormData({ field: 'duration', data: details }));
  };

  const handleBudgetChange = (details: any) => {
    dispatch(Actions.updateServiceFormData({ field: 'serviceBudget', data: details }));
  };

  const handleRequirementChange = (details: any) => {
    dispatch(Actions.updateServiceFormData({ field: 'serviceRequirement', data: details }));
  };

  const handleAdditionalInfoChange = (details: any) => {
    dispatch(Actions.updateServiceFormData({ field: 'serviceAdditional', data: details }));
  };

  return (
    <div className="mb-8">
      {currentStep === 'category' && (
        <ServiceDetailsInput
          onServiceDetailsChange={handleServiceDetailsChange}
          initialDetails={formData.serviceDetails || {}}
        />
      )}
      {currentStep === 'location' && (
        <Location
          onLocationChange={handleLocationChange}
          initialDetails={formData.serviceLocation || {}}
        />
      )}
      {currentStep === 'language' && (
        <Language
          onLanguageChange={handleLanguageChange}
          initialDetails={formData.language || {}}
        />
      )}
      {currentStep === 'team' && (
        <Team
          onTeamChange={handleTeamChange}
          initialDetails={formData.team || { teamSize: "" }}
        />
      )}
      {currentStep === 'skills' && (
        <Skills
          onSkillsChange={handleSkillsChange}
          initialDetails={formData.skills || { skills: [] }}
        />
      )}
      {currentStep === 'duration' && (
        <Duration
          onDurationChange={handleDurationChange}
          initialDetails={formData.duration || { duration: "" }}
        />
      )}
      {currentStep === 'budget' && (
        <Budget
          onBudgetChange={handleBudgetChange}
          initialDetails={formData.serviceBudget || {
            budgetRange: {
              min: 0,
              max: 0,
              currency: "USD"
            }
          }}
        />
      )}
      {currentStep === 'requirement' && (
        <Requirement
          onRequirementChange={handleRequirementChange}
          initialDetails={formData.serviceRequirement || {}}
        />
      )}
      {currentStep === 'attachment' && (
        <Attachment />
      )}
      {currentStep === 'additional' && (
        <AdditionalInfo
          onAdditionalInfoChange={handleAdditionalInfoChange}
          initialDetails={formData.serviceAdditional || {}}
        />
      )}
      {currentStep === 'otp' && (
        <OTP />
      )}
    </div>
  );
};

export default Service;