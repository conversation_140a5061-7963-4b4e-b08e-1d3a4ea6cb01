"use client";

import {
  Carousel,
  CarouselBullet,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { useState, useEffect } from "react";
// import "assets/pages/buyer/freepik__upload__40729.png from "./freepik__upload__40729.png";
import { type CarouselApi } from "@/components/ui/carousel";

interface ImageItem {
  id: number;
  src: string;
  alt: string;
}

interface SliderImageProps {
  image: ImageItem;
}

const ImageSlider: React.FC = () => {
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [desktopApi, setDesktopApi] = useState<CarouselApi>();
  const [mobileApi, setMobileApi] = useState<CarouselApi>();
  const [desktopCurrent, setDesktopCurrent] = useState(0);
  const [mobileCurrent, setMobileCurrent] = useState(0);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);

  useEffect(() => {
    if (!desktopApi) return;

    const onSelect = () => {
      setDesktopCurrent(desktopApi.selectedScrollSnap());
    };

    desktopApi.on("select", onSelect);
    onSelect();

    return () => {
      desktopApi.off("select", onSelect);
    };
  }, [desktopApi]);

  useEffect(() => {
    if (!mobileApi) return;

    const onSelect = () => {
      setMobileCurrent(mobileApi.selectedScrollSnap());
    };

    mobileApi.on("select", onSelect);
    onSelect();

    return () => {
      mobileApi.off("select", onSelect);
    };
  }, [mobileApi]);

  const images: ImageItem[] = [
    {
      id: 1,
      src: "assets/pages/buyer/freepik__upload__40729.png",
      alt: "Uploaded Visual",
    },
    {
      id: 2,
      src: "assets/pages/buyer/freepik__upload__40729.png",
      alt: "Uploaded Visual",
    },
    {
      id: 3,
      src: "assets/pages/buyer/freepik__upload__40729.png",
      alt: "Uploaded Visual",
    },
    {
      id: 4,
      src: "assets/pages/buyer/freepik__upload__40729.png",
      alt: "Uploaded Visual",
    },
  ];

  const SliderImage: React.FC<SliderImageProps> = ({ image }) => (
    <div className="w-full h-full">
      <img
        src={image.src}
        alt={image.alt}
        className="w-full h-auto object-cover hover:opacity-90 transition-opacity"
      />
    </div>
  );

  const handleDotClick = (index: number, api: CarouselApi) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  return (
    <div className="flex justify-center mb-6 w-full mt-7 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-screen-2xl 2xl:max-w-[90%]">
         <div className="mb-4 px-1">
          <h3 className="text-base font-bold text-gray-800">Just For You</h3>
        </div>
        <div className="p-0 bg-transparent shadow-none border-none">
          {/* Desktop Carousel - Hidden on mobile */}
          <div className="hidden md:block group">
            <Carousel
              setApi={setDesktopApi}
              opts={{
                align: "center",
                loop: true,
                dragFree: false,
              }}
              className="w-full relative"
            >
              <CarouselContent>
                {images.map((image) => (
                  <CarouselItem key={image.id} className="lg:basis-full">
                    <SliderImage image={image} />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="bg-white shadow-lg border border-gray-100 rounded-full h-12 w-12 left-4 focus:ring-1 focus:ring-gray-200 focus:outline-none !opacity-0 group-hover:!opacity-100 transition-opacity duration-200" />
              <CarouselNext className="bg-white shadow-lg border border-gray-100 rounded-full h-12 w-12 right-4 focus:ring-1 focus:ring-gray-200 focus:outline-none !opacity-0 group-hover:!opacity-100 transition-opacity duration-200" />
            </Carousel>
            
            {/* Custom bullet navigation below carousel for desktop */}
            <div className="mt-4 flex justify-center gap-2">
              {images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => handleDotClick(index, desktopApi)}
                  className={`w-2 h-2 rounded-full cursor-pointer transition-colors ${
                    index === desktopCurrent 
                      ? 'bg-gray-600' 
                      : 'bg-gray-300 hover:bg-gray-500'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>

          {/* Mobile Slider - Visible only on mobile */}
          <div className="block md:hidden group">
            <Carousel
              setApi={setMobileApi}
              opts={{
                align: "start",
                loop: true,
                dragFree: false,
              }}
              className="w-full"
            >
              <CarouselContent>
                {images.map((image) => (
                  <CarouselItem key={image.id} className="basis-full">
                    <SliderImage image={image} />
                  </CarouselItem>
                ))}
              </CarouselContent>
              {/* Add navigation buttons for mobile too if needed */}
              <CarouselPrevious className="bg-white shadow-lg border border-gray-100 rounded-full h-10 w-10 left-2 focus:ring-1 focus:ring-gray-200 focus:outline-none !opacity-0 group-hover:!opacity-100 transition-opacity duration-200" />
              <CarouselNext className="bg-white shadow-lg border border-gray-100 rounded-full h-10 w-10 right-2 focus:ring-1 focus:ring-gray-200 focus:outline-none !opacity-0 group-hover:!opacity-100 transition-opacity duration-200" />
            </Carousel>
            
            {/* Custom bullet navigation below carousel for mobile */}
            <div className="mt-4 flex justify-center gap-2">
              {images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => handleDotClick(index, mobileApi)}
                  className={`w-3 h-3 rounded-full cursor-pointer transition-colors ${
                    index === mobileCurrent 
                      ? 'bg-gray-600' 
                      : 'bg-gray-300 hover:bg-gray-500'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageSlider;