"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { InputLabel } from "@/components/ui/input/components/label";
import {
  Dropzone,
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import { Switch, SwitchTargetType } from "@/components/ui/input/switch";
import { Loader } from "@/components/ui/loader";
import { deleteMediaHandler } from "@/lib/utils/helper";
import { RootDispatch } from "@/redux/store";
import { Actions } from "@/screens/user/dashboard/pages/seller/website/@redux/slice";
import {
  fetchWebsiteDetails,
  updateWebsite,
} from "@/screens/user/dashboard/pages/seller/website/@redux/thunk";
import classNames from "classnames";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { websiteDetails } from "../@redux/selectors";
import { deleteMedia } from "@/screens/user/dashboard/@redux/selectors";

const Website = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values, loading } = useSelector(websiteDetails);
  const { loading: deleteLoading } = useSelector(deleteMedia);

  useEffect(() => {
    dispatch(fetchWebsiteDetails());
  }, [dispatch]);

  const handleChange = (e: SwitchTargetType | DropzoneInputTargetType) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const file = e.target.files?.[0];
        payload[e?.target?.name] = file;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setWebsiteDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  const handleCancel = () => {
    try {
      dispatch(Actions.setWebsiteDetails(null));
    } catch (error) {
      console.error(error);
    }
  };

  const handleSave = () => {
    try {
      if (values) {
        const data = new FormData();
        Object.entries(values).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (
              Array.isArray(value) &&
              value.some((fl) => fl instanceof File)
            ) {
              value.forEach((file: any) => {
                if (file instanceof File) {
                  data.append(key, file);
                }
              });
            } else if (value instanceof File || value instanceof Blob) {
              data.append(key, value);
            } else if (
              Array.isArray(value) &&
              value.some((fl) => typeof fl === "string")
            ) {
              value.forEach((str) => {
                data.append(key, str);
              });
            } else {
              data.append(key, String(value));
            }
          }
        });

        dispatch(updateWebsite(data));
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleDeleteMedia = async (file: any, name: string) => {
    const updatedFiles = await deleteMediaHandler(values, file, name);
    dispatch(Actions.setWebsiteDetails({ [name]: updatedFiles }));
  };

  return (
    <main className="relative bg-white rounded-lg md:shadow-lg md:p-4 w-full h-full">
      {loading && <Loader box big center />}
      <div className="grid grid-cols-1 gap-6">
        <div className="flex flex-col gap-6 [&>*]:w-full">
          <h6 className="border-b border-stone-200 text-start px-1 py-3.5">
            Update Website
          </h6>

          <div className="w-full flex items-center justify-between gap-2 shadow-box p-3">
            <InputLabel>Enable Website</InputLabel>
            <Switch
              name="has_website"
              checked={values?.has_website}
              onValueChange={handleChange}
              defaultChecked={false}
            />
          </div>

          <div className="flex-1 flex flex-col gap-3 [&>*]:w-full">
            <InputLabel>Website Banner</InputLabel>
            <Dropzone
              containerClassName={classNames(
                "flex-1 [&_.file-preview]:!max-w-full [&_.file-preview]:max-h-96",
                values?.website_banner?.length > 0
                  ? "[&_.file-preview-container]:!h-96"
                  : "[&_.file-preview-container]:!h-64"
              )}
              onChange={(e) => handleChange(e as DropzoneInputTargetType)}
              onFileDelete={(file, fieldName) =>
                handleDeleteMedia(file, fieldName)
              }
              name="website_banner"
              value={[values?.website_banner]}
              preview
              inputProps={{
                disabled: values?.website_banner,
              }}
              deleteLoading={deleteLoading}
            >
              {values?.website_banner ? " " : undefined}
            </Dropzone>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 mt-7 md:mt-10">
        <Button
          className="min-w-28 rounded-md"
          variant="destructive"
          onClick={() => handleCancel()}
        >
          Cancel
        </Button>
        <Button
          className="min-w-28 rounded-md"
          variant="main-revert"
          onClick={() => handleSave()}
        >
          Save
        </Button>
      </div>
    </main>
  );
};

export default Website;
