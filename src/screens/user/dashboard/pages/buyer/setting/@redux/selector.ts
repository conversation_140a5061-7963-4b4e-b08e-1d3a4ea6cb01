import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "@/redux/root-reducer";

// Base selector to access user buyer settings state
const reducer = (state: RootState) => state.user_buyer_settings;

// Selector for fetching profile details state
export const getProfileDetailsSelector = createSelector(
  [reducer],
  (reducer) => reducer.getProfileDetails
);

// Selector for updating profile image state
export const getUpdateProfileImageSelector = createSelector(
  [reducer],
  (reducer) => reducer.updateProfileImage
);

// Selector for updating profile details state
export const getUpdateProfileDetailsSelector = createSelector(
  [reducer],
  (reducer) => reducer.updateProfileDetails
);

// Selector for changing user password state
export const getChangePasswordSelector = createSelector(
  [reducer],
  (reducer) => reducer.changePassword
);
