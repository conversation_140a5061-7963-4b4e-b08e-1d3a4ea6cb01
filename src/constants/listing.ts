import { DropdownListDataType } from "@/components/ui/dropdown/list/types";
import { Dispatch, SetStateAction } from "react";

export const ListingData = [...Array(+20).keys()].map((_, index) => {
  return {
    name: "Tencent Holdings",
    images: [
      "https://i.pinimg.com/736x/4c/a4/55/4ca45500bfb228a0f62ddaf7c2837094.jpg",
      "https://c4.wallpaperflare.com/wallpaper/229/242/345/moscow-city-2017-art-irbis-production-wallpaper-thumb.jpg",
    ],
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.rem",
    location: {
      flag: "https://upload.wikimedia.org/wikipedia/en/thumb/4/41/Flag_of_India.svg/1200px-Flag_of_India.svg.png",
      address: "India Mumbai",
      label: "Service Provider",
    },
    verified: true,
    shortIntro: "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.",
    rating: {
      stars: 4.5,
      reviewsCount: 150,
    },
    strengthRange: "800 - 1000",
    businessAge: "20",
    minOrderQuantity: 300,
    tags: [
      { title: "innovation" },
      { title: "Efficiency" },
      { title: "Returns" },
      { title: "Sustainability" },
      { title: "Worldwide" },
      { title: "collaboration" },
      { title: "performance" },
      { title: "knowledge" },
      { title: "strategy" },
      { title: "funding" },
      { title: "technology" },
    ],
    offerPrice: 5000.99, // Added Offer Price
    actualPrice: 6000.99, // Added Actual Price
  };
});


export const DropdownOptions = (
  selected: any,
  setSelected: Dispatch<SetStateAction<any[]>>,
  count?: number
) => {
  const handleSelect = (option: any): void => {
    setSelected((prev) => {
      let options = Array.isArray(prev) ? Array.from(prev) : [];

      if (Boolean(options?.find((item) => item?.key === option?.key))) {
        options = options?.filter((item) => item?.key !== option?.key);
      } else {
        options.push(option);
      }

      return options;
    });
  };

  return [...Array(+20).keys()]?.slice(0, count ?? 20).map((_, index) => ({
    label: `Option ${index + 1}`,
    key: `option-${index + 1}`,
    event: (menu: DropdownListDataType) => handleSelect(menu),
    checked: Array.isArray(selected)
      ? Boolean(selected?.find((item) => item?.key === `option-${index + 1}`))
      : selected?.key === `option-${index + 1}`,
  }));
};
