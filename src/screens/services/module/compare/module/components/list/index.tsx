"use client";

import { comparedList } from "@/screens/company/compare/@redux/selectors";
import { Actions } from "@/screens/company/compare/@redux/slice";
import classNames from "classnames";
import { X } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";

const ComparedList = () => {
  const dispatch = useDispatch();
  const compared = useSelector(comparedList);

  const handleRemove = (selected: any) => {
    try {
      dispatch(Actions.removeComparedItem(selected));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col items-start gap-5 my-8 max-w-sm lg:max-w-lg mx-auto">
      {compared?.map((company: any, index: number) => {
        return (
          <div
            key={index}
            className={classNames(
              "relative w-full ps-4",
              "before:w-[1px] before:h-2/3 before:absolute before:top-1/2 before:-translate-y-1/2 before:left-0 before:bg-white before:z-20",
              index > 0 &&
                "after:content-['VS'] after:absolute after:top-0 after:left-0 after:-translate-x-1/2 after:-translate-y-full after:text-white after:text-sm after:z-20"
            )}
          >
            <div
              key={index}
              className={classNames(
                "w-full bg-white rounded-full px-3 py-2 flex items-center justify-between gap-4"
              )}
            >
              <div className="flex items-center justify-start gap-2">
                <img
                  src={company?.icon}
                  alt="company icon"
                  className="w-8 h-8 object-cover rounded-full"
                />
                <span className="text-sm font-medium">{company?.title}</span>
              </div>

              <button
                className="flex items-center justify-center"
                onClick={() => handleRemove(company)}
              >
                <X />
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ComparedList;
