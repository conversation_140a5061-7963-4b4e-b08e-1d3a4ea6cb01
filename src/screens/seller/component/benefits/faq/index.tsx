import FrequentlyAskedQuestions from "@/components/elements/list/faq";

const FAQSection = () => {
  const faqItems = [
    {
      title: "How much does it cost?",
      description:
        "There are no fees for buyers to browse and purchase products on our platform. We earn our revenue through a small commission from sellers on each successful transaction.",
    },
    {
      title: "Are suppliers trustworthy?",
      description:
        "All suppliers undergo a verification process before joining our platform. You can also check supplier ratings, reviews from other buyers, and view their business credentials on their profile.",
    },
    {
      title: "Can I negotiate with suppliers?",
      description:
        "Yes, our platform allows direct communication with suppliers. You can discuss pricing, minimum order quantities, customization options, and delivery terms through our secure messaging system.",
    },
    {
      title: "What products can I source?",
      description:
        "Our platform offers a wide range of products across multiple categories including electronics, textiles, home goods, machinery, and raw materials. You can browse by category or use our search feature to find specific items.",
    },
    {
      title: "Can I connect with suppliers?",
      description:
        "Yes, our platform hosts suppliers from over 50 countries. You can filter search results by location to find domestic or international suppliers based on your preferences.",
    },
  ];

  return (
    <div className="w-full max-w-[100%] sm:max-w-[94%] lg:max-w-[95%] xl:max-w-[96%] 2xl:max-w-[90%] shadow-none sm:shadow-xl rounded-2xl mb-4 mx-auto px-4 sm:px-6 md:px-8 py-2 sm:py-6">
      <h1 className="text-base justify-center sm:text-2xl md:text-3xl xl:text-4xl font-bold text-start ml-2 mb-4 sm:mb-8 text-stone-900">
        Sellers Frequently Asked Questions
      </h1>

      <FrequentlyAskedQuestions
        data={faqItems}
        type="single"
        collapsible
        className="space-y-2 sm:space-y-4"
        // itemProps={{
        //   className: "bg-[#F5F5F5] rounded-lg shadow-sm mb-2 sm:mb-4 px-0 border-none"
        // }}
        triggerProps={{
          className:
            "p-4 sm:p-5 text-base sm:text-lg font-medium text-stone-900",
        }}
        contentProps={{
          className:
            "px-4 pb-3 sm:px-5 sm:pb-5 text-sm sm:text-base text-gray-700",
        }}
      />
    </div>
  );
};

export default FAQSection;
