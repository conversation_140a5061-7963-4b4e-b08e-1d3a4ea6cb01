import { CheckIcon } from "lucide-react";

const SetsApartList = () => {
  return (
    <div className="mx-auto bg-[#FAFAFA] w-full">
      <h2 className="text-base font-bold mb-8">What Sets Us Apart?</h2>

      <div className="grid grid-cols-1 gap-6">
        {[...Array(4).keys()].map((client, index) => (
          <div
            key={index}
            className="bg-[#F2F2F2] shadow-sm rounded-xl text-start p-4 flex flex-col flex-start gap-2.5"
          >
            <div className="flex items-start justify-start gap-2">
              <div className="bg-white flex items-center justify-center p-1 rounded-sm w-max mt-0.5">
                <CheckIcon className="w-3 h-3" />
              </div>
              <h3 className="font-bold text-left">The First B2B Platform in Albania</h3>
            </div>
            <p className="text-sm text-start">
              Pioneering trade connections across industries.
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SetsApartList;
