"use client";

import { Badge } from "@/components/ui/badge";
import InputLabel from "@/components/ui/input/components/label";
import {
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { Textarea } from "@/components/ui/input/textarea";
import { InputTargetType } from "@/components/ui/input/types";
import { sub_sub_categories } from "@/screens/user/dashboard/pages/seller/listing/module/company/@redux/selectors";
import { Actions } from "@/screens/user/dashboard/pages/seller/listing/module/services/@redux/slice";
import { ChangeEvent } from "react";
import { useDispatch, useSelector } from "react-redux";
import { serviceDetails } from "../../../@redux/selectors";
import { RootDispatch } from "@/redux/store";
import Combobox from "@/components/ui/input/combobox";
import { languages, regions } from "@/constants/user/seller/company/update";

const ServiceInformation = () => {
  const dispatch = useDispatch<RootDispatch>();
  const { data: values } = useSelector(serviceDetails);
  const { data: categoriesData } = useSelector(sub_sub_categories);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        let fileList = Array.isArray(values[e?.target?.name])
          ? Array.from(values[e?.target?.name])
          : [];
        fileList = fileList.concat(fileArray);
        payload[e?.target?.name] = fileList;
      } else {
        payload[e?.target?.name] = e?.target?.value;
      }
      dispatch(Actions.setServiceDetails(payload));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6 [&>*]:w-full md:[&>*]:w-auto">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[25%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Service Name</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Required, Text (Mx 100 chars)
          </span>
        </div>
        <Input
          type="text"
          name="service_name"
          label="Service Name"
          onChange={handleChange}
          value={values?.service_name}
          placeholder="Service Name"
          containerClassName="flex-1"
          labelClassName="hidden md:block"
          maxLength={70}
          showCharCount
        />
      </div>
      
      {/* TODO */}
      {/* Value coming as object */}
      <Combobox
        name="sub_sub_category"
        label="Category"
        onChange={handleChange}
        value={
          typeof values?.sub_sub_category === "string"
            ? values?.sub_sub_category
            : (values?.sub_sub_category?._id ?? "")
        }
        placeholder="Select Category"
        options={categoriesData}
        optionKeys={{
          label: "sub_sub_category_name",
          value: "_id",
        }}
      />
      <MultiSelect
        onValueChange={handleChange}
        name="usp"
        defaultValue={values?.usp}
        value={values?.usp}
        label="USP (Unique Selling price)"
        placeholder="Select Sub-Category"
        variant="inverted"
        maxCount={3}
        options={[]}
        addNew
      />
      <MultiSelect
        onValueChange={handleChange}
        name="location"
        defaultValue={values?.location}
        value={values?.location}
        label="Service Area/Location"
        placeholder="Select Locations"
        variant="inverted"
        maxCount={3}
        options={regions}
      />
      <MultiSelect
        onValueChange={handleChange}
        name="language"
        defaultValue={values?.language}
        value={values?.language}
        label="Languages Supported"
        placeholder="Select Languages"
        variant="inverted"
        maxCount={3}
        options={languages}
      />
      <Input
        type="text"
        name="meta_title"
        label="Meta Title"
        placeholder="Enter title"
        value={values?.meta_title}
        onChange={handleChange}
      />
      <Textarea
        name="meta_description"
        label="Meta Description"
        onChange={handleChange}
        value={values?.meta_description}
        placeholder="Type here"
        className="min-h-20"
        containerClassName="flex-1"
        maxLength={500}
        showCharCount
      />
      <MultiSelect
        label="Meta Keywords"
        placeholder="Add Keywords"
        name="meta_keywords"
        defaultValue={values?.meta_keywords ?? []}
        onValueChange={handleChange}
        options={[]}
        maxCount={3}
        addNew
      />
      <Select
        name="service_status"
        label="Service Status"
        onChange={handleChange}
        value={values?.service_status}
        placeholder="Select Status"
        options={[
          { label: "Active", value: true },
          { label: "Inactive", value: false },
        ]}
      />
    </>
  );
};

export default ServiceInformation;
