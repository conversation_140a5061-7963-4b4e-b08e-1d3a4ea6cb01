import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";
import privateApiClient from "@/services/axios/private-api-client";

const formDataConfig = {
  headers: {
    "Content-Type": "multipart/form-data",
  },
};

export const getCompanies = async ({ searchTerm, page, limit }: any = {}) => {
  return await privateApiClient.get(apiRoutes().user.seller.company.fetchAll, {
    params: { searchTerm, page, limit },
  });
};

export const getCompany = async () => {
  return await privateApiClient.get(`${apiRoutes().user.seller.company.fetch}`);
};

export const updateCompanyDetails = async (payload: FormData) => {
  return await privateApiClient.post(
    apiRoutes().user.seller.company.update,
    payload,
    formDataConfig
  );
};

export const companyEmailVerifyInitiate = async (payload: {
  email: string;
}) => {
  return await privateApiClient.post(
    apiRoutes().user.seller.company.verification.email.initiate,
    payload
  );
};

export const companyEmailVerify = async (payload: { email_otp: string }) => {
  return await privateApiClient.post(
    apiRoutes().user.seller.company.verification.email.verify,
    payload
  );
};

export const companyBusinessPhotosUpload = async () => {
  return await formDataClient.post(
    apiRoutes().user.seller.company.files.businessPhotos
  );
};

export const companyGovRegistrationUpload = async () => {
  return await formDataClient.post(
    apiRoutes().user.seller.company.files.govRegistration
  );
};

export const companyPhotosUpload = async () => {
  return await formDataClient.post(
    apiRoutes().user.seller.company.files.companyPhotos
  );
};

// CATEGORIES START

export const getCategories = async (params: any) => {
  return await privateApiClient.get(apiRoutes().user.category.getAll, {
    params,
  });
};

export const get_sub_Categories = async (params: any) => {
  return await privateApiClient.get(apiRoutes().user.category.sub.fetchAll, {
    params,
  });
};

export const get_sub_sub_Categories = async (params: any) => {
  return await privateApiClient.get(
    apiRoutes().user.category.sub_sub.fetchAll,
    {
      params,
    }
  );
};

export const getDynamicFieldsByCategory = async (category_id: string) => {
  return await privateApiClient.get(
    `${apiRoutes().user.category.fetchDynamicField}/${category_id}`
  );
};

// CATEGORIES END
