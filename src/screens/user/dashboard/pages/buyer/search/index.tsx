"use client";

import { useState, useEffect } from "react";
import CreateRouter from "@/router/helper/create-router";
import routes from "@/router/routes/types/user/dashboard/buyer/search/route";
import { ListingTabs } from "./module";
import HeaderSearch from "@/components/layout/header/components/search";

const UserSellerListings = () => {
  const [showSearch, setShowSearch] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      // Show search bar only when at the top
      setShowSearch(window.scrollY < 10);
    };

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);

    // Remove event listener on cleanup
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div className="flex flex-col w-full">
      {showSearch && (
        <div className="w-full flex justify-center mb-6">
          <div className="max-w-[600px] w-full">
            <HeaderSearch className="w-full" />
          </div>
        </div>
      )}
      <ListingTabs />
      <CreateRouter routeData={routes} />
    </div>
  );
};

export default UserSellerListings;