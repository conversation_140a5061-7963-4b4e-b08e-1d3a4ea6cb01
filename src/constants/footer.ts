export const staticPageUrl = (path: string): string =>
  `${process.env.NEXT_PUBLIC_STATIC_PAGES_BASE_URL}/${path}`;

export const footerMenu = [
  {
    title: "Quick Links",
    menu: [
      {
        title: "About Us",
        path: staticPageUrl("about-us"),
      },
      {
        title: "Contact",
        path: staticPageUrl("contact"),
      },
    ],
  },
  {
    title: "Collaborate",
    menu: [
      {
        title: "Seller",
        path: staticPageUrl("buyer"),
      },
      {
        title: "Buyer",
        path: staticPageUrl("seller"),
      },
      {
        title: "Advertise",
        path: staticPageUrl("advertisers"),
      },
      {
        title: "All-in-one",
        path: staticPageUrl("all-in-one"),
      },
    ],
  },
  {
    title: "Help",
    menu: [
      {
        title: "FAQs",
        path: staticPageUrl("frequently-asked-questions"),
      },
      {
        title: "Feedback",
        path: staticPageUrl("feedback"),
      },
      {
        title: "Complaints",
        path: staticPageUrl("complaints"),
      },
      {
        title: "How to use Platform",
        path: staticPageUrl("how-to-use-platform"),
      },
      {
        title: "User Guides",
        path: staticPageUrl("user-guides"),
      },
    ],
  },
  {
    title: "Resource",
    menu: [
      {
        title: "Industry News & Insights",
        path: staticPageUrl(""),
      },
      {
        title: "Blogs",
        path: staticPageUrl("blogs"),
      },
      {
        title: "Articles",
        path: staticPageUrl("articles"),
      },
      {
        title: "Tips for Buyers",
        path: staticPageUrl("tips-for-buyers"),
      },
      {
        title: "Tips for Seller",
        path: staticPageUrl("tips-for-seller"),
      },
    ],
  },
  {
    title: "Legal",
    menu: [
      {
        title: "Terms of Service",
        path: staticPageUrl("terms-and-conditions"),
      },
      {
        title: "Privacy Policy",
        path: staticPageUrl("privacy-policy-2"),
      },
      {
        title: "Disclaimer",
        path: staticPageUrl("disclaimer"),
      },
      {
        title: "Cookie Policy",
        path: staticPageUrl("cookie-policy"),
      },
      {
        title: "Intellectual Property",
        path: staticPageUrl("intellectual-property"),
      },
      {
        title: "Listing Guidelines",
        path: staticPageUrl("listing-guidelines"),
      },
      {
        title: "Others",
        path: staticPageUrl("others"),
      },
    ],
  },
];
