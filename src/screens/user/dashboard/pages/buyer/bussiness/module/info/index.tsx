'use client';

import { Badge } from "@/components/ui/badge";
import InputLabel from "@/components/ui/input/components/label";
import {
  Dropzone,
  DropzoneInputTargetType,
  isDropzoneInputTargetType,
} from "@/components/ui/input/dropzone";
import {
  MultiSelect,
  MultiSelectTarget,
} from "@/components/ui/input/multi-select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/input/radio";
import { Select } from "@/components/ui/input/select";
import { Input } from "@/components/ui/input/text";
import { InputTargetType } from "@/components/ui/input/types";
import { extractArrayOfKey } from "@/lib/utils/data";
import {
  businessDetails,
  sub_sub_categories,
} from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/selector";
import { Actions } from "@/screens/user/dashboard/pages/buyer/bussiness/@redux/slice";
import { RootDispatch } from "@/redux/store";
import { Image, X } from "lucide-react";
import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getBusinessDetails, getSubSubCategories } from "../../@redux/thunk";

type CompanyFormValues = {
  business_logo?: File[];
  [key: string]: any;
};

interface InformationProps {
  onValidationChange?: (isValid: boolean) => void;
  isSubmitted?: boolean;
}

const Information = ({ onValidationChange, isSubmitted }: InformationProps) => {
  const dispatch = useDispatch<RootDispatch>();
  const businessDetailsState = useSelector(businessDetails);
  const subSubCategoriesState = useSelector(sub_sub_categories);

  const categoriesData = subSubCategoriesState?.data;
  const categoriesLoading = subSubCategoriesState?.loading;

  const [companyLogo, setCompanyLogo] = useState<string | null>(null);

  useEffect(() => {
    console.log("Fetching sub-sub categories...");
    dispatch(getSubSubCategories());
  }, [dispatch]);

  useEffect(() => {
    if (businessDetailsState?.data?.business_logo?.url) {
      setCompanyLogo(businessDetailsState.data.business_logo.url);
    } else if (
      businessDetailsState?.data?.business_logo instanceof File ||
      (Array.isArray(businessDetailsState?.data?.business_logo) &&
        businessDetailsState?.data?.business_logo.length > 0)
    ) {
      const logoFile = Array.isArray(businessDetailsState?.data?.business_logo)
        ? businessDetailsState?.data?.business_logo[0]
        : businessDetailsState?.data?.business_logo;

      if (logoFile instanceof File) {
        setCompanyLogo(URL.createObjectURL(logoFile));
      }
    }
  }, [businessDetailsState]);

  useEffect(() => {
    const isValid = Boolean(
      companyLogo &&
      businessDetailsState?.data?.business_name &&
      businessDetailsState?.data?.business_type &&
      businessDetailsState?.data?.industry_category?.length &&
      (businessDetailsState?.data?.area_name ||
        businessDetailsState?.data?.street_address) &&
      businessDetailsState?.data?.city &&
      businessDetailsState?.data?.country &&
      businessDetailsState?.data?.years_of_establishment &&
      businessDetailsState?.data?.web_url
    );

    onValidationChange?.(isValid);
  }, [businessDetailsState?.data, companyLogo, onValidationChange]);

  const handleChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | InputTargetType
      | MultiSelectTarget
      | DropzoneInputTargetType
  ) => {
    try {
      const payload: any = {};
      if (isDropzoneInputTargetType(e)) {
        const fileArray = Array.from(e.target.files);
        if (e.target.name === "business_logo" && fileArray.length > 0) {
          const file = fileArray[0];
          setCompanyLogo(URL.createObjectURL(file));
          payload[e.target.name] = fileArray;
        } else {
          let fileList = Array.isArray(businessDetailsState?.data?.[e?.target?.name])
            ? Array.from(businessDetailsState?.data?.[e?.target?.name])
            : [];
          fileList = fileList.concat(fileArray);
          payload[e?.target?.name] = fileList;
        }
      } else if (e?.target?.name === "industry_category") {
        const selectedValues = Array.isArray(e.target.value)
          ? e.target.value
          : [e.target.value];

        // Map selected values to objects with _id and name
        payload[e.target.name] = selectedValues.map((valueId: string) => {
          const foundCategory = categoriesData?.find((cat: any) => cat._id === valueId);
          return {
            _id: valueId,
            name: foundCategory?.sub_sub_category_name || valueId,
            sub_category_id: foundCategory?.sub_category_id || "",
          };
        });
      } else {
        if (e?.target?.name === "business_type") {
          payload[e.target.name] = [e.target.value];
        } else {
          payload[e?.target?.name] = e?.target?.value;
        }
      }

      dispatch(Actions.setFormValues(payload));
    } catch (error) {
      console.error("Error in handleChange:", error);
    }
  };

  const handleRemoveLogo = () => {
    setCompanyLogo(null);

    if (businessDetailsState?.data?.business_logo?.media_id) {
      dispatch(
        Actions.setFormValues({
          delete_business_logo: [businessDetailsState.data.business_logo.media_id],
          business_logo: [],
        })
      );
    } else {
      dispatch(Actions.setFormValues({ business_logo: [] }));
    }
  };

  if (businessDetailsState?.loading) {
    return <div>Loading business details...</div>;
  }

  const values = businessDetailsState?.data || {};

  const getBusinessType = () => {
    if (!values.business_type) return "";
    if (Array.isArray(values.business_type)) {
      return values.business_type[0];
    }
    return values.business_type;
  };

  const getIndustryCategoryValues = () => {
    if (!values.industry_category) return [];

    // Return an array of _id values for MultiSelect
    return values.industry_category.map((category: any) =>
      typeof category === "object" && category._id ? category._id : category
    );
  };

  // Prepare categories options for MultiSelect
  const getCategoriesOptions = () => {
    if (!categoriesData || !Array.isArray(categoriesData)) {
      console.log("Categories data not available or not an array:", categoriesData);
      return [];
    }

    // Remove duplicates by _id and ensure valid options
    const uniqueCategories = new Map();
    categoriesData
      .filter((cat: any) => Boolean(cat?.sub_sub_category_name?.trim()))
      .forEach((cat: any, index: number) => {
        const key = cat._id;
        if (!uniqueCategories.has(key)) {
          uniqueCategories.set(key, {
            label: cat.sub_sub_category_name,
            value: cat._id,
          });
        } else {
          console.warn(`Duplicate _id found: ${key}. Skipping duplicate.`);
        }
      });

    return Array.from(uniqueCategories.values());
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-start gap-6">
        <div className="min-w-min flex flex-col text-start gap-2 md:w-[30%]">
          <div className="flex items-center justify-start gap-1.5">
            <InputLabel>Business Logo</InputLabel>
            <Badge
              className="!text-[0.7rem] font-bold leading-3 !px-[2.5px] !py-0 w-max"
              variant="destructive"
            >
              Required
            </Badge>
          </div>
          <span className="text-stone-500 text-[0.7rem]">
            Upload logo & images (500×500 px)
          </span>
        </div>
        <div className="flex-1 w-full">
          <div className="border border-dashed border-slate-400 rounded-lg p-6 relative">
            {companyLogo ? (
              <div className="relative">
                <div className="flex justify-center">
                  <div className="relative rounded-full overflow-hidden w-24 h-24">
                    <img
                      src={companyLogo}
                      alt="Company Logo"
                      className="w-full h-full object-cover"
                    />
                    <button
                      onClick={handleRemoveLogo}
                      className="absolute top-3 right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center"
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>
                <p className="text-center mt-2 text-sm">Company Logo</p>
              </div>
            ) : (
              <Dropzone
                containerClassName="flex-1"
                onChange={(e) => handleChange(e)}
                name="business_logo"
                value={values?.business_logo}
                preview
                contentClassName="!border-none !p-0"
              >
                <div className="flex flex-col items-center justify-center">
                  <div className="text-center">
                    <p className="text-sm text-red-500 mb-1">Upload a file</p>
                    <p className="text-xs text-gray-500">or drag and drop</p>
                  </div>
                </div>
              </Dropzone>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          type="text"
          name="business_name"
          label="Business Name"
          onChange={handleChange}
          value={values?.business_name || ""}
          placeholder="Official company name"
        />

        <Select
          name="business_type"
          label="Business Type"
          onChange={handleChange}
          value={getBusinessType()}
          placeholder="Manufacturers"
          options={[
            { label: "Manufacturers", value: "manufacturers" },
            { label: "Retailers", value: "retailers" },
            { label: "Wholesalers", value: "wholesalers" },
            { label: "Service Providers", value: "service_providers" },
          ]}
        />
      </div>

      <div className="grid grid-cols-1 gap-6">
        <MultiSelect
          onValueChange={handleChange}
          name="industry_category"
          label="Industry category"
          placeholder={categoriesLoading ? "Loading categories..." : "Choose main Industry"}
          variant="inverted"
          maxCount={10}
          options={getCategoriesOptions()}
          value={getIndustryCategoryValues()}
          disabled={categoriesLoading}
        />
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Input
          type="text"
          name="area_name"
          label="Street Address"
          onChange={handleChange}
          value={values?.area_name || ""}
          placeholder="Enter your business address"
          maxLength={150}
          showCharCount
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Select
          name="city"
          label="City"
          onChange={handleChange}
          value={values?.city || ""}
          placeholder="Dhnabad"
          options={[
            { label: "Delhi", value: "delhi" },
            { label: "Mumbai", value: "mumbai" },
            { label: "Bangalore", value: "bangalore" },
            { label: "Chennai", value: "chennai" },
          ]}
        />

        <Select
          name="country"
          label="Country"
          onChange={handleChange}
          value={values?.country || ""}
          placeholder="India"
          options={[
            { label: "India", value: "india" },
            { label: "USA", value: "usa" },
            { label: "UK", value: "uk" },
            { label: "Canada", value: "canada" },
          ]}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          type="text"
          name="years_of_establishment"
          label="Years of Establishment"
          onChange={handleChange}
          value={values?.years_of_establishment || ""}
          placeholder="yyyy"
        />

        <Input
          type="text"
          name="web_url"
          label="Website address"
          onChange={handleChange}
          value={values?.web_url || ""}
          placeholder="www.example.com"
        />
      </div>
    </>
  );
};

export default Information;