'use client';

import { Select } from "@/components/ui/input/select";
import React, { useState } from 'react';
import { InputTargetType } from "@/components/ui/input/select";

interface TeamDetails {
  teamSize: string;
}

interface TeamProps {
  onTeamChange?: (details: TeamDetails) => void;
  initialDetails?: TeamDetails;
}

const TeamSizeDropdown: React.FC<TeamProps> = ({ 
  onTeamChange, 
  initialDetails = {
    teamSize: ""
  }
}) => {
  const [teamDetails, setTeamDetails] = useState<TeamDetails>(initialDetails);

  const handleTeamSizeChange = (e: InputTargetType) => {
    const updatedDetails = {
      ...teamDetails,
      teamSize: e.target.value
    };
    setTeamDetails(updatedDetails);
    if (onTeamChange) {
      onTeamChange(updatedDetails);
    }
  };

  const teamSizeOptions = [
    { label: "1-2 people", value: "1-2" },
    { label: "3-5 people", value: "3-5" },
    { label: "6+ people", value: "6+" }
  ];

  return (
    <div className="flex flex-col justify-center items-center bg-white min-h-[60vh] w-full mx-auto p-4 sm:p-6">
      <div className="flex flex-col  mb-16 items-center max-w-md w-full">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-8 text-center">Select the size of your team or organization.</h1>
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md space-y-4">
          <Select
            placeholder="Select team size"
            options={teamSizeOptions}
            value={teamDetails.teamSize}
            onChange={handleTeamSizeChange}
          />
        </div>
      </div>
    </div>
  );
};

export default TeamSizeDropdown;