"use client";

import { Check, ChevronsUpDown } from "lucide-react";
import * as React from "react";

import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import classNames from "classnames";
import InputLabel from "../components/label";
import { InputWrapper } from "../components/wrapper";
import { ErrorBoundary } from "react-error-boundary";

export type InputTargetType = {
  target: {
    value: string;
    name: string;
  };
};

interface OptionType {
  label: string;
  value: string;
  icon?: string;
  [key: string]: any;
}

interface ComboboxType {
  id?: string;
  icon?: React.ReactNode | string;

  options: OptionType[];
  optionKeys?: {
    label?: string;
    value?: string;
    icon?: string;
  };
  onChange?: (e: InputTargetType) => void;
  value?: string;
  name?: string;
  placeholder?: string;
  className?: string;
  containerClassName?: string;
  wrapperClassName?: string;
  label?: string;
  labelClassName?: string;
}

const Combobox: React.FC<ComboboxType> = ({
  id,
  icon,
  options,
  placeholder,
  onChange,
  name,
  value,
  className,
  containerClassName,
  wrapperClassName,
  label,
  labelClassName,
  optionKeys,
}) => {
  const [open, setOpen] = React.useState(false);

  const uniqueId = React.useId();

  const handleChange = (val: string) => {
    try {
      if (onChange) {
        onChange({
          target: {
            value: val,
            name: name ?? "select",
          },
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const getLabel = (option: OptionType) => {
    try {
      if (optionKeys && optionKeys?.label) {
        return option?.[optionKeys?.label];
      }

      return option?.label;
    } catch {
      return option?.label;
    }
  };

  const getValue = (option: OptionType) => {
    try {
      if (optionKeys && optionKeys?.value) {
        return option?.[optionKeys?.value];
      }

      return option?.value;
    } catch {
      return option?.value;
    }
  };

  const selectedOption = options?.find((option) => getValue(option) === value);

  return (
    <ErrorBoundary fallback={<div>Failed to load combobox</div>}>
      <Popover open={open} onOpenChange={setOpen}>
        <div
          className={classNames(
            "flex flex-col gap-1.5 items-center [&>*]:w-full",
            containerClassName
          )}
        >
          {label && (
            <InputLabel htmlFor={id ?? uniqueId} className={labelClassName}>
              {label}
            </InputLabel>
          )}
          <PopoverTrigger asChild>
            <InputWrapper
              icon={icon}
              className={classNames("w-full !h-max", wrapperClassName)}
            >
              <button
                role="combobox"
                aria-expanded={open}
                className={classNames(
                  "w-full text-sm !shadow-none border-light !ring-0 flex items-center gap-3 justify-between",
                  className
                )}
              >
                {value
                  ? selectedOption
                    ? getLabel(selectedOption)
                    : String(value)
                  : placeholder || "Select"}
                <ChevronsUpDown className="opacity-50 w-4 h-4" />
              </button>
            </InputWrapper>
          </PopoverTrigger>
        </div>
        <PopoverContent className="w-[240px] p-0 bg-white" align="start">
          <Command>
            <CommandInput placeholder="Search..." className="h-9" />
            <CommandList>
              <CommandEmpty>No Result found.</CommandEmpty>
              <CommandGroup>
                {options?.map((option) => (
                  <CommandItem
                    key={getValue(option)}
                    value={getValue(option)}
                    onSelect={(currentValue) => {
                      handleChange(currentValue === value ? "" : currentValue);
                      setOpen(false);
                    }}
                    className="cursor-pointer py-2 hover:bg-slate-100"
                    title={option?.name}
                  >
                    {getLabel(option)}
                    <Check
                      className={classNames(
                        "ml-auto",
                        value === getValue(option) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </ErrorBoundary>
  );
};

export default Combobox;
