import { Skeleton } from "@/components/ui/loader/skeleton";
import classNames from "classnames";
import { Fragment, lazy, Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";
import ServiceSelections from "./components/selection";

const TabsData = [
  {
    title: "Services gallery mobile",
    path: "gallery-mob",
    component: lazy(() => import("./components/gallery/mobile")),
    hideForDesktop: true,
    ad: true,
  },
  {
    title: "Services Profile",
    path: "profile",
    component: lazy(() => import("./components/profile")),
  },
  {
    title: "Services gallery",
    path: "display",
    component: lazy(() => import("./components/gallery")),
    props: {},
    ad: true,
    hideForMobile: true,
  },
  {
    title: "Services Description",
    path: "description",
    component: lazy(() => import("./components/description")),
  },
  // {
  //   title: "Services Overview",
  //   path: "overview",
  //   component: lazy(() => import("./components/selection")),
  //   hideOnDesktop: true,
  //   props: {
  //     className: "block md:hidden",
  //   },
  // },
  {
    title: "Services Overview",
    path: "overview",
    component: lazy(() => import("./components/overview")),
  },
  {
    title: "Services Package",
    path: "package",
    component: lazy(() => import("./components/package")),
  },
  {
    title: "Service Portfolio",
    component: lazy(() => import("./components/portfolio")),
  },
  // {
  //   title: "Services Catalogs",
  //   component: lazy(() => import("./components/projects")),
  // },
  {
    title: "FAQ",
    component: lazy(() => import("./components/faq")),
  },
  {
    title: "Reviews",
    component: lazy(
      () =>
        import("@/screens/company/details/landing/components/information/reviews")
    ),
  },
  {
    title: "Service Inquiry",
    path: "inquiry",
    component: lazy(() => import("./components/inquiry")),
  },
  {
    title: "supplier",
    component: lazy(() => import("./components/supplier")),
  },
  // {
  //   title: "Ad",
  //   ad: true,
  //   component: lazy(() => import("@/components/layout/ads/banner")),
  // },
  {
    title: "Related Services",
    component: lazy(() => import("./components/related")),
  },
];

const ServiceDetails = () => {
  return (
    <main id="product-detail-page" className="p-0 sm:container my-12">
      <div className="relative grid grid-cols-1 md:grid-cols-5 gap-6 items-start w-full max-w-full">
        <div className={classNames("md:col-span-3 grid grid-cols-1 gap-6")}>
          {TabsData?.map((tab, index) => {
            const key = tab.path || tab.title || `tab-${index}`;
            return (
              <Fragment key={key}>
                <ErrorBoundary fallback={<div>Failed to load section</div>}>
                  <Suspense fallback={<Skeleton className="w-full h-44" />}>
                    <section
                      className={classNames(
                        "w-full sm:scroll-mt-44 sm:shadow-box rounded-md",
                        "bg-white",
                        {
                          "block md:hidden": tab?.hideForDesktop,
                          "hidden md:block": tab?.hideForMobile,
                          "px-6 md:px-8 py-7": !tab?.ad,
                          "bg-gradient-to-l from-[#FFF5F6] via-[#FDFDFD]  to-[#FFFAF4]":
                            tab?.path && ["inquiry"].includes(tab?.path),
                        }
                      )}
                    >
                      <tab.component {...(tab?.props ?? {})} />
                    </section>
                  </Suspense>
                </ErrorBoundary>
              </Fragment>
            );
          })}
        </div>
        <ServiceSelections className="md:col-span-2 sticky top-header bg-white sm:shadow-box p-4 rounded-md" />
      </div>
    </main>
  );
};

export default ServiceDetails;
