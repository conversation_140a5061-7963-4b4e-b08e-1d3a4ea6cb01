"use client";

import StartPriceCard from "@/components/elements/card/price/start";
import FrequentlyAskedQuestions from "@/components/elements/list/faq";
import { useRouter } from "next/navigation";

export const faqData = Array.from({ length: 4 })?.map(() => ({
  title: "Is listing my business free of charge?",
  description:
    "Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and Lorem Ipsum is simply dummy text of the printing and.",
}));

const AdvertisingPlans = () => {
const router = useRouter();

  return (
    <div className="w-full">
      <div className="grid grid-cols-2 gap-6">
        {[...Array(+4).keys()]?.map((_, index) => {
          return (
            <StartPriceCard
              key={index}
              onSelect={() => router.push("/dashboard/seller/paid-services/confirmation")}
              title="Free Plan"
              description="Lorem Ipsum is simply dummy text of the printing and typesetting
            industry."
              price="00"
              checklist={[
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: true,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: true,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: false,
                },
                {
                  label:
                    "Lorem Ipsum is simply dummy text of the printing and.",
                  available: false,
                },
              ]}
            />
          );
        })}
      </div>

      <div className="w-full bg-white  shadow-md rounded-md px-7 py-10 mt-8">
        <h3 className="font-bold mb-4">Frequently Asked Questions</h3>
        <FrequentlyAskedQuestions type="single" data={faqData} />
      </div>
    </div>
  );
};

export default AdvertisingPlans;
