import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";

interface stateType {
  showSidebar: boolean;
}

const initialState = {
  showSidebar: true,
} satisfies stateType as stateType;

export const slice = createSlice({
  name: "user_dashboard",
  initialState,
  reducers: {
    setSidebarShow: (state, action: PayloadAction<boolean>) => {
      state.showSidebar = action?.payload;
    },
  },
});

export const Actions = slice.actions;
export default slice.reducer;
