import ProductCard from "@/components/elements/card/product";
import React from "react";

const ProductsCompany = ({ category_id }: { category_id?: string }) => {
  return (
    <div className="relative container my-12">
      <div className="grid grid-cols-1 gap-6">
        <h3 className="font-bold">
          All Products {category_id && "by" + category_id}
        </h3>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {[...Array(25)].map((_, i) => (
            <ProductCard key={i} compare />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProductsCompany;
