"use client";

import * as React from "react";
import * as SwitchPrimitives from "@radix-ui/react-switch";
import classNames from "classnames";

type SwitchTargetType = {
  target: {
    name: string;
    value: boolean;
  };
};

interface SwitchType
  extends React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> {
  onValueChange?: (d: SwitchTargetType) => void;
}

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  SwitchType
>(
  (
    { name = "Switch", className, onValueChange, onCheckedChange, ...props },
    ref
  ) => {
    const handleChange = (value: boolean) => {
      if (onCheckedChange) onCheckedChange(value);
      if (onValueChange) {
        onValueChange({ target: { name, value } });
      }
    };
    return (
      <SwitchPrimitives.Root
        className={classNames(
          "switch peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-red-500 hover:bg-slate-50 shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-main focus-visible:ring-offset-2 focus-visible:ring-offset-dark disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:border-green-500 data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500",
          className
        )}
        onCheckedChange={handleChange}
        {...props}
        ref={ref}
      >
        <SwitchPrimitives.Thumb
          className={classNames(
            "switch-thumb pointer-events-none block h-4 w-4 rounded-full bg-slate-50 shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"
          )}
        />
      </SwitchPrimitives.Root>
    );
  }
);
Switch.displayName = SwitchPrimitives.Root.displayName;

export { Switch };
export type { SwitchType, SwitchTargetType };
