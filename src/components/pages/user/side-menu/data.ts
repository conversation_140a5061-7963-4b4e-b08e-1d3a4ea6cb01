import { MenuData } from "./types";

export const sellerMenuData: MenuData = [
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/dashboard.svg",
    title: "Insights",
    activePathname: ["/dashboard/seller"],
    pathname: "/dashboard/seller",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/listing.svg",
    title: "Listing",
    pathname: "/dashboard/seller/listing",
    activePathname: ["/dashboard/seller/listing/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
    title: "Website",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
    title: "Inbox",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    title: "Opportunities",
    pathname: "/dashboard/seller/opportunities",
    activePathname: ["/dashboard/seller/opportunities/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/paid.svg",
    title: "Paid Services",
    pathname: "/dashboard/seller/paid-services",
    activePathname: ["/dashboard/seller/paid-services/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/reviews.svg",
    title: "Reviews",
    pathname: "/dashboard/seller/reviews",
    activePathname: ["/dashboard/seller/reviews"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/media.svg",
    title: "Photos & Media",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/setting.svg",
    title: "Setting",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/support.svg",
    title: "Support",
  },
];

// TODO
// Path name should be all small

export const BuyerMenuData: MenuData = [
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/dashboard.svg",
    title: "Insights",
    activePathname: ["/dashboard/buyer"],
    pathname: "/dashboard/buyer",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/listing.svg",
    title: "Profile",
    pathname: "/dashboard/buyer/bussiness",
    activePathname: ["/dashboard/buyer/bussiness/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
    title: "Search & Compare",
    pathname: "/dashboard/buyer/search",
    activePathname: ["/dashboard/buyer/search/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/website.svg",
    title: "Inbox",
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
    title: "Request Quotations",
    pathname: "/dashboard/buyer/request",
    activePathname: ["/dashboard/buyer/request/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/inbox.svg",
    title: "Manage Quotations",
    pathname: "/dashboard/buyer/manage",
    activePathname: ["/dashboard/buyer/manage/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    title: "Reviews",
    pathname: "/dashboard/buyer/reviews",
    activePathname: ["/dashboard/buyer/reviews/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    title: "Favourite",
    pathname: "/dashboard/buyer/favourite",
    activePathname: ["/dashboard/buyer/favourite/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg",
    title: "Photos & Media",
    pathname: "/dashboard/buyer/media",
    activePathname: ["/dashboard/buyer/media/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/paid.svg",
    title: "Paid Services",
    pathname: "/dashboard/buyer/paid-services",
    activePathname: ["/dashboard/buyer/paid-services/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/reviews.svg",
    title: "Settings",
    pathname: "/dashboard/buyer/settings",
    activePathname: ["/dashboard/buyer/settings/*"],
  },
  {
    icon: "/assets/pages/user/dashboard/sidebar/seller/media.svg",
    title: "Support",
    pathname: "/dashboard/buyer/support",
    activePathname: ["/dashboard/buyer/support/*"],
  },
];
