import { RootState } from "@/redux/store";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { getDynamicFields } from "../../../../@api/common";
import * as API from "../@api/update";
import { Actions } from "./slice";

export const getServices = createAsyncThunk<
  any,
  { searchTerm?: string; page?: string; limit?: string } | undefined,
  { state: RootState }
>("servicesList", async (pagination, { rejectWithValue, dispatch }) => {
  try {
    const response: any = await API.getServices({
      searchTerm: pagination?.searchTerm,
      page: pagination?.page,
      limit: pagination?.limit,
    });

    window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
    dispatch(Actions.setCurrentPage(String(response?.meta?.pagination?.page)));

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const getService = createAsyncThunk<any, string, { state: RootState }>(
  "serviceDetails/getService",
  async (service_id, { rejectWithValue }) => {
    try {
      const response = await API.getService(service_id);
      window?.scrollTo?.({ top: 0, left: 0, behavior: "auto" });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const getServiceDynamicFields = createAsyncThunk<
  any,
  { sub_sub_category: string },
  { state: RootState }
>("dynamicFields", async ({ sub_sub_category }, { rejectWithValue }) => {
  try {
    const response: any = await getDynamicFields({
      entityType: "service",
      sub_sub_category: sub_sub_category,
    });

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const getServiceSuggestions = createAsyncThunk<
  any,
  { searchTerm: string },
  { state: RootState }
>("serviceSuggestions", async ({ searchTerm }, { rejectWithValue }) => {
  try {
    const response: any = await API.getServiceSuggestions({ searchTerm });

    return response;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const getServicePreValues = createAsyncThunk<
  any,
  { suggestionId: string },
  { state: RootState }
>(
  "serviceDetails/getServicePreValues",
  async ({ suggestionId }, { rejectWithValue }) => {
    try {
      const response: any = await API.getServicePreValues({ suggestionId });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const updateServiceDetails = createAsyncThunk<
  any,
  { service_id?: string; data: FormData },
  { state: RootState }
>(
  "updateServiceDetails",
  async ({ service_id, data }, { rejectWithValue, dispatch }) => {
    try {
      const response: any = await API.updateServiceDetails(data, service_id);
      toast.success(response?.meta?.message as string, {
        classNames: {
          toast: "!bg-green-500 !text-white",
        },
      });

      return response;
    } catch (error: any) {
      toast.error(error?.meta?.message as string, {
        classNames: {
          toast: "!bg-red-500 !text-white",
        },
      });

      return rejectWithValue(error?.response?.data || error?.message);
    }
  }
);

export const deleteService = createAsyncThunk<
  any,
  string,
  { state: RootState }
>("deleteService", async (service_id, { rejectWithValue, dispatch }) => {
  try {
    const response: any = await API.deleteService(service_id);
    toast.success(response?.meta?.message as string, {
      classNames: {
        toast: "!bg-green-500 !text-white",
      },
    });
    dispatch(getServices());
    return response;
  } catch (error: any) {
    toast.error(error?.meta?.message as string, {
      classNames: {
        toast: "!bg-red-500 !text-white",
      },
    });

    return rejectWithValue(error?.response?.data || error?.message);
  }
});

export const thunks = {
  getService,
  getServices,
  updateServiceDetails,
  getServiceDynamicFields,
  getServiceSuggestions,
  getServicePreValues,
};
