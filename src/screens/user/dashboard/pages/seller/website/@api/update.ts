import { apiRoutes } from "@/services/api/routes";
import formDataClient from "@/services/axios/formData-client";
import privateApiClient from "@/services/axios/private-api-client";

const formDataConfig = {
  headers: {
    "Content-Type": "multipart/form-data",
  },
};

export const fetchWebsite = async () => {
  return await privateApiClient.get(apiRoutes().user.seller.website.fetch);
};

export const addUpdateWebsite = async (payload: FormData) => {
  return await formDataClient.post(
    apiRoutes().user.seller.website.update,
    payload,
    {
      ...formDataConfig,
    }
  );
};
