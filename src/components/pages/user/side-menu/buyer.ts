import DASHBOARD from "/assets/pages/user/dashboard/sidebar/seller/dashboard.svg";
import INBOX from "/assets/pages/user/dashboard/sidebar/seller/inbox.svg";
import LISTING from "/assets/pages/user/dashboard/sidebar/seller/listing.svg";
import MEDIA from "/assets/pages/user/dashboard/sidebar/seller/media.svg";
import OPPORTUNITIES from "/assets/pages/user/dashboard/sidebar/seller/opportunities.svg";
import PAID from "/assets/pages/user/dashboard/sidebar/seller/paid.svg";
import REVIEWS from "/assets/pages/user/dashboard/sidebar/seller/reviews.svg";
import WEBSITE from "/assets/pages/user/dashboard/sidebar/seller/website.svg";

import { MenuData } from "./types";

export const BuyererMenuData: MenuData = [
    {
        icon: DASHBOARD,
        title: "Insights",
        activePathname: ["/dashboard/buyer"],
        pathname: "/dashboard/buyer",
      },
      {
        icon: LISTING,
        title: "Profile",
        pathname: "/dashboard/buyer/Bussiness",
        activePathname: ["/dashboard/buyer/bussiness/*"],
      },
      {
        icon: WEBSITE,
        title: "Search & Compare",
        pathname: "/dashboard/buyer/search",
        activePathname: ["/dashboard/buyer/search/*"],
      },
      {
        icon: WEBSITE,
        title: "Inbox"
      },
      {
        icon: INBOX,
        title: "Request Quotations",
        pathname: "/dashboard/buyer/request",
        activePathname: ["/dashboard/buyer/request/*"],
      },
      {
        icon: INBOX,
        title: "Manage Quotations",
        pathname: "/dashboard/buyer/manage",
        activePathname: ["/dashboard/buyer/manage/*"],
      },
      {
        icon: OPPORTUNITIES,
        title: "Reviews",
        pathname: "/dashboard/buyer/reviews",
        activePathname: ["/dashboard/buyer/reviews/*"],
      },
      {
        icon: OPPORTUNITIES,
        title: "Favourite",
        pathname: "/dashboard/buyer/favourite",
        activePathname: ["/dashboard/buyer/favourite/*"],
      },
      {
        icon: OPPORTUNITIES,
        title: "Photos & Media",
        pathname: "/dashboard/buyer/media",
        activePathname: ["/dashboard/buyer/media/*"],
      },
      {
        icon: PAID,
        title: "Paid Services",
        pathname: "/dashboard/buyer/service",
        activePathname: ["/dashboard/buyer/service/*"],
      },
      {
        icon: REVIEWS,
        title: "Settings",
        pathname: "/dashboard/buyer/settings",
        activePathname: ["/dashboard/buyer/settings/*"],
      },
      {
        icon: MEDIA,
        title: "Support",
        pathname: "/dashboard/buyer/support",
        activePathname: ["/dashboard/buyer/support/*"],
      }
];
