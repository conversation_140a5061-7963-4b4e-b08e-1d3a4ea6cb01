import { Loader } from "@/components/ui/loader";
import React, { lazy, Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";

const TextField = lazy(() =>
  import("@/components/ui/input/text").then((module) => ({
    default: module.Input,
  }))
);

const Select = lazy(() =>
  import("@/components/ui/input/select").then((module) => ({
    default: module.Select,
  }))
);

interface FieldType {
  _id: string;
  name: string;
  slug: string;
  allowMultiple: boolean;
  isRequired: boolean;
  allowCustomValue: boolean;
  order: number;
  description: string;
  helpText: string;
  units: {
    _id: string;
    name: string;
    slug: string;
    symbol: string;
    decimalPrecision: number;
    order: number;
    description: string;
    isActive: boolean;
  }[];
}

type FormValues = {
  measure?: string;
  unit?: string;
};

const DynamicMeasurementsRenderer = ({
  field,
  values = [],
  onChange,
}: {
  field: FieldType;
  values: {
    measurementId: string;
    unitId: string;
    value: string | number;
  }[];
  onChange?: (e: any) => void;
}) => {
  const [formValues, setFormValues] = React.useState<FormValues>();

  const getFieldData = () => {
    try {
      if (!Array.isArray(values)) return undefined;
      return values?.find((v: any) => v?.measurementId === field?._id);
    } catch (error) {
      console.error(error);
      return undefined;
    }
  };
  const fieldData = getFieldData();

  React.useEffect(() => {
    setFormValues({
      measure: fieldData?.value?.toString() ?? "",
      unit: fieldData?.unitId ?? "",
    });
  }, [fieldData]);

  const handleChange = (e: any) => {
    try {
      const name = e?.target?.name;
      const value = e?.target?.value;

      const updatedValues = {
        ...formValues,
        [name]: value,
      };

      setFormValues(updatedValues);

      if (onChange) {
        const payload = {
          measurementId: field?._id,
          unitId: updatedValues?.unit,
          value: updatedValues?.measure,
        };
        onChange(payload);
      }
    } catch (error) {
      console.error(error);
    }
  };

  if (!field) return null;

  return (
    <ErrorBoundary fallback={<span>{field?.name} failed to load</span>}>
      <Suspense fallback={<Loader center />}>
        <div className="flex gap-2">
          <TextField
            type="text"
            name="measure"
            label={field?.name}
            placeholder={field?.helpText}
            value={formValues?.measure ?? ""}
            onChange={handleChange}
            required={field?.isRequired}
            containerClassName="flex-1"
          />
          {Array.isArray(field?.units) && field?.units?.length > 0 && (
            <Select
              name="unit"
              onChange={handleChange}
              value={formValues?.unit ?? ""}
              placeholder={"Unit"}
              options={field?.units?.map((unit) => ({
                label: `${unit?.symbol} - ${unit?.name}`,
                value: unit?._id,
              }))}
              containerClassName="w-max mt-5"
            />
          )}
        </div>
      </Suspense>
    </ErrorBoundary>
  );
};

export default DynamicMeasurementsRenderer;
