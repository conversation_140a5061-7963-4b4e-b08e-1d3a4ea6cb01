import RATING_STAR from "/assets/pages/rating-star.svg";
import VERIFIES_GRAY from "/assets/pages/verified-gray.svg";
import VERIFIES_WHITE from "/assets/pages/verified-white.svg";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/input/checkbox";
import { ListingData } from "@/constants/listing";
import classNames from "classnames";
import { useRouter } from "next/navigation";
// import ContactNowModal from "../../inquiry";
import ImageGallery from "@/screens/company/listing/module/components/list/list-card/images";

const ListCard = ({ company }: { company: (typeof ListingData)[0] }) => {
const router = useRouter();

  return (
    <div className="flex-1 w-full h-max bg-white shadow-md hover:shadow-lg">
      <div className="flex flex-col gap-0">
        <div className="flex flex-col sm:flex-row gap-6 px-6 pt-6 pb-4">
          <div className="w-full sm:w-max flex items-start justify-between gap-4">
            <ImageGallery images={company?.images} />
            <CheckboxMapping className="flex sm:hidden" />
          </div>

          <div className="flex-1 flex flex-col items-start gap-1 [&>*]:w-full">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center justify-start gap-1.5">
                <h2
                  role="button"
                  className="text-xl font-bold hover:underline text-black hover:drop-shadow"
                  onClick={() => router.push("/company")}
                >
                  {company?.name}
                </h2>
                <div className="flex items-center gap-2 sm:hidden ">
                  <img
                    src={VERIFIES_GRAY}
                    alt="flag"
                    className="aspect-auto w-5 h-auto object-contain"
                  />
                  <span className="flex-1 text-xs text-stone-600">
                    Sponsored
                  </span>
                </div>
              </div>
              <CheckboxMapping className="hidden sm:flex" />
            </div>

            <div className="flex items-center gap-1.5 mt-1 mb-2">
              <img
                src={company?.location?.flag}
                alt="flag"
                className="flex-1 aspect-auto max-w-5 w-5 h-auto object-contain"
              />
              <div className="flex item flex-wrap gap-1 [&>*]:text-xs [&>*]:text-stone-700 ">
                <span>
                  {company?.location?.address}
                  {", "}
                </span>
                <span>{company?.location?.label}</span>
              </div>
            </div>
            {company?.verified && (
              <div className="flex items-center gap-2 mb-2">
                <div className="hidden sm:flex bg-[#9F9795] text-white px-2 py-1 items-center justify-start gap-1.5 w-max max-w-max rounded-tl-sm rounded-bl-sm rounded-tr-md rounded-br-md">
                  <img
                    src={VERIFIES_WHITE}
                    alt="flag"
                    className="flex-1 aspect-auto max-w-3 w-3 h-auto object-contain"
                  />
                  <span className="flex-1 text-[0.6rem] font-medium">
                    Verified
                  </span>
                </div>
                <span className="text-xs text-stone-600">Sponsored</span>
              </div>
            )}
            <div className="flex items-center gap-2 mb-2">
              <div className="flex items-center gap-3 pr-4"> 
                <span className="font-bold text-[#070707] text-2xl">
                  ${company?.offerPrice}
                </span>

                <span className="font-bold text-[#A2A2A2] text-xl line-through">
                  ${company?.actualPrice}
                </span>
              </div>

              <div className="flex items-center gap-2.5">
                <img
                  src={RATING_STAR}
                  alt="rating star"
                  className="aspect-auto w-5 md:w-7 h-5 md:h-7 object-contain"
                />
                <div className="flex item flex-wrap gap-2.5 [&>*]:text-base [&>*]:font-semibold [&>*]:text-font [&>*]:leading-4">
                  <span className="border-r border-slate-300 pr-2.5">
                    {company?.rating?.stars}/5
                  </span>
                  <span>{company?.rating?.reviewsCount} Reviews</span>
                </div>
              </div>
            </div>



            {/* Tags */}
            <div
              className="hidden md:flex items-center flex-wrap gap-2 max-w-full md:max-w-[75%] mt-3 mb-2 overflow-hidden"
              style={{
                WebkitBoxOrient: "vertical",
                WebkitLineClamp: 2,
                maxHeight: "calc(2 * (1.3rem + 0.5rem))",
              }}
            >
              {Array.isArray(company?.tags) &&
                company?.tags?.length > 0 &&
                company?.tags?.slice(0, 10)?.map((tag, index) => {
                  return (
                    <button
                      key={index}
                      className="flex items-center justify-center px-3 py-0.5 bg-stone-50 hover:bg-stone-100 border border-stone-300 rounded-full text-stone-800 text-xs"
                    >
                      {tag?.title}
                    </button>
                  );
                })}
            </div>

            {/* Description */}
            <div className="mt-1">
              <p
                title={company?.description}
                className="text-[0.92rem] text-stone-700 line-clamp-2"
              >
                {company?.description}
              </p>
            </div>
          </div>
        </div>

        {/* Card Footer */}
        <div className="flex flex-col sm:flex-row sm:flex-wrap items-center gap-4 justify-between border-t-0 sm:border-t border-slate-200 px-6 pt-2.5 pb-6 sm:pb-2.5">
          <div className="w-full md:w-max flex items-center justify-center gap-4 [&>*:not(:last-child)]:border-r [&>*:not(:last-child)]:border-slate-300 [&>*:not(:last-child)]:pr-4 [&>*]:text-xs [&>*]:font-light [&>*]:leading-4 bg-stone-50 sm:bg-transparent px-3 sm:px-0 py-4 sm:py-1">
            <p className="flex-1 text-nowrap [&>*]:text-xs flex flex-col sm:flex-row items-center gap-2">
              <span className="font-semibold">{company?.strengthRange}</span>
              <span>Employees</span>
            </p>
            <p className="flex-1 text-nowrap [&>*]:text-xs flex flex-col sm:flex-row items-center gap-2">
              <span className="font-semibold">{company?.businessAge}</span>
              <span>Years in business</span>
            </p>
            <p className="flex-1 text-nowrap [&>*]:text-xs flex flex-col sm:flex-row items-center gap-2">
              <span className="font-semibold">
                Min Order Value: {company?.minOrderQuantity}
              </span>{" "}
              <span>USD</span>
            </p>
          </div>
          <div className="w-full md:w-max flex items-center justify-between sm:justify-center gap-3 [&>*]:flex-1 [&>*]:py-5 [&>*]:font-bold sm:[&>*]:font-semibold md:[&>*]:py-4">
            <Button variant="outline" size="sm">
              Portfolio
            </Button>
            <Button variant="outline" size="sm">
              website
            </Button><Button variant="outline" size="sm">
              Get Quote
            </Button>
            {/* <ContactNowModal /> */}
          </div>
        </div>
      </div>
    </div>
  );
};

const CheckboxMapping = ({ className }: { className?: string }) => {
  return (
    <Checkbox
      label="Add to Compare"
      labelClassName="whitespace-nowrap"
      containerClassName={classNames("order-1 sm:order-2", className)}
    />
  );
};

export default ListCard;
