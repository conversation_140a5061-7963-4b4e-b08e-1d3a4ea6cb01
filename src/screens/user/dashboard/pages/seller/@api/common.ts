import { apiRoutes } from "@/services/api/routes";
import privateApiClient from "@/services/axios/private-api-client";

type getDynamicFieldsType = {
  entityType: string;
  sub_sub_category: string;
};

// TODO
// correct the common endpoint

export const getDynamicFields = async ({
  entityType,
  sub_sub_category,
}: getDynamicFieldsType) => {
  return await privateApiClient.get(
    apiRoutes().user.seller.common.getDynamicFields,
    {
      params: { entityType, sub_sub_category },
    }
  );
};