import classNames from "classnames";
import { FC, LabelHTMLAttributes, ReactNode } from "react";

interface InputLabelType extends LabelHTMLAttributes<HTMLLabelElement> {
  children: ReactNode;
  className?: string;
  required?: boolean;
}

const InputLabel: FC<InputLabelType> = ({
  children,
  className,
  required,
  ...props
}) => {
  return (
    <label
      title={
        required ? "Required" : typeof children === "string" ? children : ""
      }
      className={classNames(
        "input-label text-sm font-medium text-font/90 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer",
        className
      )}
      {...props}
    >
      {children}

      {required && (
        <span
          title="required"
          className="text-danger text-base ml-0.5 align-middle leading-0"
        >
          <sup>*</sup>
        </span>
      )}
    </label>
  );
};

export { InputLabel };
